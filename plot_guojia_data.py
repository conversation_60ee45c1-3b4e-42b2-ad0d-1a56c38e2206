#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制国家数据的tp和flow时间序列图
tp变量以顶部框线为横轴，从上到下递增绘制
flow变量以底部框线为横轴，从下到上递增绘制
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np

# 设置字体，避免中文显示问题
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_tp_flow_data():
    """绘制tp和flow数据的时间序列图"""
    
    # 读取数据
    try:
        # df = pd.read_csv('Final_data/guojia.csv')
        df = pd.read_csv('Final_data/tp-flow/mituo.csv')
        
        print(f"成功读取数据，共{len(df)}行")
    except FileNotFoundError:
        # print("错误：找不到文件 Final_data/guojia.csv")
        print("错误：找不到文件 Final_data/tp-flow/mituo.csv")
        return
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return
    
    # 转换日期格式
    df['date'] = pd.to_datetime(df['date'], format='%Y/%m/%d %H:%M')
    
    # 创建图形和子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
    
    # 绘制tp数据（顶部子图，y轴反转）
    ax1.plot(df['date'], df['tp'], color='blue', linewidth=1, label='Precipitation (tp)')
    ax1.set_ylabel('Precipitation (mm)', fontsize=12)
    ax1.set_title('Time Series of Precipitation and Flow', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper right')
    
    # 反转tp的y轴，使其从上到下递增
    ax1.invert_yaxis()
    
    # 设置tp的y轴范围，确保0在顶部
    tp_max = df['tp'].max()
    ax1.set_ylim(tp_max * 1.1, -tp_max * 0.05)
    
    # 绘制flow数据（底部子图）
    ax2.plot(df['date'], df['flow'], color='green', linewidth=1, label='Flow (flow)')
    ax2.set_ylabel('Flow (m³/s)', fontsize=12)
    ax2.set_xlabel('Time', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper right')
    
    # 设置x轴日期格式
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    # 显示统计信息
    print(f"Data time range: {df['date'].min()} to {df['date'].max()}")
    print(f"Precipitation range: {df['tp'].min():.3f} - {df['tp'].max():.3f} mm")
    print(f"Flow range: {df['flow'].min():.2f} - {df['flow'].max():.2f} m³/s")
    
    # 保存图像
    # plt.savefig('guojia_tp_flow_plot.png', dpi=300, bbox_inches='tight')
    # print("Image saved as guojia_tp_flow_plot.png")
    plt.savefig('mituo_tp_flow_plot.png', dpi=300, bbox_inches='tight')
    print("Image saved as mituo_tp_flow_plot.png")
    
    # 显示图像
    plt.show()

def plot_combined_chart():
    """绘制tp和flow在同一张图上的组合图表"""
    
    # 读取数据
    try:
        # df = pd.read_csv('Final_data/guojia.csv')
        df = pd.read_csv('Final_data/tp-flow/mituo.csv')
        print(f"成功读取数据，共{len(df)}行")
    except FileNotFoundError:
        # print("错误：找不到文件 Final_data/guojia.csv")
        print("错误：找不到文件 Final_data/tp-flow/mituo.csv")
        return
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return
    
    # 转换日期格式
    df['date'] = pd.to_datetime(df['date'], format='%Y/%m/%d %H:%M')
    
    # 创建图形
    fig, ax1 = plt.subplots(figsize=(15, 8))
    
    # 绘制flow数据（底部，正常方向）
    color1 = 'green'
    ax1.set_xlabel('Time', fontsize=12)
    ax1.set_ylabel('Flow (m³/s)', color=color1, fontsize=12)
    line1 = ax1.plot(df['date'], df['flow'], color=color1, linewidth=1, label='Flow (flow)')
    ax1.tick_params(axis='y', labelcolor=color1)
    ax1.grid(True, alpha=0.3)
    
    # 创建第二个y轴用于tp数据
    ax2 = ax1.twinx()
    
    # 绘制tp数据（顶部，反转方向）
    color2 = 'blue'
    ax2.set_ylabel('Precipitation (mm)', color=color2, fontsize=12)
    line2 = ax2.plot(df['date'], df['tp'], color=color2, linewidth=1, label='Precipitation (tp)')
    ax2.tick_params(axis='y', labelcolor=color2)
    
    # 反转tp的y轴，使其从上到下递增
    ax2.invert_yaxis()
    
    # 设置标题
    ax1.set_title('Time Series of Precipitation and Flow (Combined Chart)', fontsize=14, fontweight='bold')
    
    # 设置x轴日期格式
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 添加图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图像
    # plt.savefig('guojia_combined_plot.png', dpi=300, bbox_inches='tight')
    # print("Combined image saved as guojia_combined_plot.png")
    plt.savefig('mituo_combined_plot.png', dpi=300, bbox_inches='tight')
    print("Combined image saved as mituo_combined_plot.png")    
    
    # 显示图像
    plt.show()

if __name__ == "__main__":
    print("Plotting separate charts...")
    plot_tp_flow_data()

    print("\nPlotting combined chart...")
    plot_combined_chart()
