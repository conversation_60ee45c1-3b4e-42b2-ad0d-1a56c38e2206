#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析yinhe.csv文件中的时间点问题和流量缺失值
"""

import pandas as pd
from datetime import datetime, timedelta

def main():
    try:
        print("=== yinhe.csv 时间点和数据分析报告 ===\n")
        
        # 读取CSV文件
        print("正在读取CSV文件...")
        df = pd.read_csv('Real_flow/csv-use/yinhe.csv')
        
        print(f"数据总行数: {len(df)}")
        print(f"时间范围: {df['时间'].iloc[0]} 到 {df['时间'].iloc[-1]}\n")
        
        # 1. 检查不规则（非整点）的时间点
        print("1. 检查不规则（非整点）的时间点")
        print("-" * 50)
        
        # 将时间列转换为datetime格式
        df['datetime'] = pd.to_datetime(df['时间'])
        
        # 检查非整点时间（分钟和秒不为0的时间）
        irregular_count = 0
        irregular_examples = []
        
        for idx, dt in enumerate(df['datetime']):
            if dt.minute != 0 or dt.second != 0:
                irregular_count += 1
                if len(irregular_examples) < 10:
                    irregular_examples.append(f"  行 {idx + 2}: {df['时间'].iloc[idx]}")
        
        print(f"不规则（非整点）时间点数量: {irregular_count}")
        if irregular_examples:
            print("示例:")
            for example in irregular_examples:
                print(example)
            if irregular_count > 10:
                print(f"  ... 还有 {irregular_count - 10} 个")
        
        # 2. 检查缺失的时间点
        print("\n2. 检查缺失的时间点")
        print("-" * 50)
        
        # 定义期望的时间范围
        start_time = datetime(2024, 1, 1, 8, 0)
        end_time = datetime(2025, 5, 10, 17, 0)
        
        print(f"期望时间范围: {start_time} 到 {end_time}")
        
        # 生成期望的完整时间序列（每小时一次）
        expected_times = []
        current_time = start_time
        while current_time <= end_time:
            expected_times.append(current_time)
            current_time += timedelta(hours=1)
        
        print(f"期望时间点总数: {len(expected_times)}")
        
        # 获取实际存在的时间点
        actual_times = set(df['datetime'])
        expected_times_set = set(expected_times)
        
        # 找出缺失的时间点
        missing_times = expected_times_set - actual_times
        missing_times_list = sorted(list(missing_times))
        
        print(f"实际时间点总数: {len(actual_times)}")
        print(f"缺失时间点数量: {len(missing_times_list)}")
        
        if missing_times_list:
            print(f"缺失时间点范围: {missing_times_list[0]} 到 {missing_times_list[-1]}")
            print("前10个缺失时间点:")
            for missing_time in missing_times_list[:10]:
                print(f"  {missing_time}")
            if len(missing_times_list) > 10:
                print(f"  ... 还有 {len(missing_times_list) - 10} 个")
        
        # 3. 检查流量缺失值及对应的水位范围
        print("\n3. 流量缺失值分析")
        print("-" * 50)
        
        flow_col = '流量(m3/s)'
        water_level_col = '水位(m)'
        
        # 转换为数值类型
        df['flow_numeric'] = pd.to_numeric(df[flow_col], errors='coerce')
        df['water_level_numeric'] = pd.to_numeric(df[water_level_col], errors='coerce')
        
        # 找出流量缺失的行
        flow_missing_mask = df['flow_numeric'].isna()
        flow_missing_count = flow_missing_mask.sum()
        
        print(f"流量缺失值数量: {flow_missing_count}")
        
        if flow_missing_count > 0:
            # 获取流量缺失时对应的水位值
            missing_water_levels = df.loc[flow_missing_mask, 'water_level_numeric'].dropna()
            
            if len(missing_water_levels) > 0:
                min_water_level = missing_water_levels.min()
                max_water_level = missing_water_levels.max()
                mean_water_level = missing_water_levels.mean()
                
                print(f"流量缺失时对应的水位范围: {min_water_level:.3f}m - {max_water_level:.3f}m")
                print(f"流量缺失时对应的水位平均值: {mean_water_level:.3f}m")
                
                # 显示前几个流量缺失的记录
                missing_records = df[flow_missing_mask][['时间', water_level_col, flow_col]].head(10)
                print("\n前10个流量缺失的记录:")
                for idx, row in missing_records.iterrows():
                    print(f"  {row['时间']}: 水位={row[water_level_col]}, 流量={row[flow_col]}")
            else:
                print("流量缺失的记录中水位也都缺失")
        else:
            print("未发现流量缺失值")
        
        # 检查水位缺失值
        water_level_missing_count = df['water_level_numeric'].isna().sum()
        print(f"\n水位缺失值数量: {water_level_missing_count}")
        
        # 4. 总结
        print("\n4. 总结")
        print("-" * 50)
        print(f"• 不规则时间点数量: {irregular_count}")
        print(f"• 缺失时间点数量: {len(missing_times_list)}")
        print(f"• 流量缺失值数量: {flow_missing_count}")
        print(f"• 水位缺失值数量: {water_level_missing_count}")
        
        if irregular_count == 0 and len(missing_times_list) == 0:
            print("\n✅ 数据时间序列完整且规范！")
        else:
            print("\n⚠️  数据时间序列存在问题，需要进一步处理。")
        
        if flow_missing_count > 0 or water_level_missing_count > 0:
            print("⚠️  数据中存在缺失值，需要进一步处理。")
            
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
