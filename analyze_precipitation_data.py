#!/usr/bin/env python3
"""
分析降水预报产品与实际降水的统计指标
计算E1, E2, China, NOAA四个预报产品与real_tp的相关系数、RMSE和MAE
同时给出real_tp的均值、标准差和最大值
"""

import csv
import math
import os

def calculate_mean(values):
    """计算均值"""
    if not values:
        return float('nan')
    return sum(values) / len(values)

def calculate_std(values):
    """计算标准差"""
    if len(values) < 2:
        return float('nan')
    mean = calculate_mean(values)
    variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
    return math.sqrt(variance)

def calculate_correlation(x, y):
    """计算皮尔逊相关系数"""
    if len(x) != len(y) or len(x) < 2:
        return float('nan')

    n = len(x)
    mean_x = calculate_mean(x)
    mean_y = calculate_mean(y)

    # 计算协方差和标准差
    covariance = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n)) / (n - 1)
    std_x = calculate_std(x)
    std_y = calculate_std(y)

    if std_x == 0 or std_y == 0 or math.isnan(std_x) or math.isnan(std_y):
        return float('nan')

    return covariance / (std_x * std_y)

def calculate_metrics(forecast, observed):
    """
    计算预报与观测的统计指标

    Parameters:
    forecast: 预报值列表
    observed: 观测值列表

    Returns:
    dict: 包含相关系数、RMSE、MAE的字典
    """
    # 移除缺失值对
    clean_pairs = []
    for f, o in zip(forecast, observed):
        if not (math.isnan(f) or math.isnan(o)):
            clean_pairs.append((f, o))

    if len(clean_pairs) == 0:
        return {'correlation': float('nan'), 'rmse': float('nan'), 'mae': float('nan')}

    forecast_clean = [pair[0] for pair in clean_pairs]
    observed_clean = [pair[1] for pair in clean_pairs]

    # 计算相关系数
    correlation = calculate_correlation(forecast_clean, observed_clean)

    # 计算RMSE
    mse = sum((f - o) ** 2 for f, o in clean_pairs) / len(clean_pairs)
    rmse = math.sqrt(mse)

    # 计算MAE
    mae = sum(abs(f - o) for f, o in clean_pairs) / len(clean_pairs)

    return {
        'correlation': correlation,
        'rmse': rmse,
        'mae': mae
    }

def safe_float(value):
    """安全转换为浮点数"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return float('nan')

def analyze_file(filepath):
    """
    分析单个CSV文件

    Parameters:
    filepath: CSV文件路径

    Returns:
    dict: 分析结果
    """
    print(f"\n正在分析文件: {filepath}")

    # 读取数据
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            data = list(reader)
        print(f"数据行数: {len(data)}")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

    # 检查必要的列是否存在
    required_columns = ['E1', 'E2', 'China', 'NOAA', 'real_tp']
    if not data:
        print("文件为空")
        return None

    missing_columns = [col for col in required_columns if col not in data[0].keys()]
    if missing_columns:
        print(f"缺少必要的列: {missing_columns}")
        return None

    # 转换数据为数值类型
    processed_data = {}
    for col in required_columns:
        processed_data[col] = [safe_float(row[col]) for row in data]

    # 计算real_tp的基本统计量
    real_tp = processed_data['real_tp']
    real_tp_clean = [x for x in real_tp if not math.isnan(x)]

    real_tp_stats = {
        'mean': calculate_mean(real_tp_clean),
        'std': calculate_std(real_tp_clean),
        'max': max(real_tp_clean) if real_tp_clean else float('nan'),
        'count': len(real_tp_clean)
    }
    
    print(f"real_tp统计量:")
    print(f"  均值: {real_tp_stats['mean']:.6f}")
    print(f"  标准差: {real_tp_stats['std']:.6f}")
    print(f"  最大值: {real_tp_stats['max']:.6f}")
    print(f"  有效数据点: {real_tp_stats['count']}")

    # 计算各预报产品的指标
    forecast_products = ['E1', 'E2', 'China', 'NOAA']
    results = {'real_tp_stats': real_tp_stats, 'forecast_metrics': {}}

    for product in forecast_products:
        forecast = processed_data[product]
        metrics = calculate_metrics(forecast, real_tp)
        results['forecast_metrics'][product] = metrics

        print(f"\n{product}预报产品指标:")
        print(f"  相关系数: {metrics['correlation']:.6f}")
        print(f"  RMSE: {metrics['rmse']:.6f}")
        print(f"  MAE: {metrics['mae']:.6f}")

    return results

def main():
    """主函数"""
    data_dir = "Multi-source-fusion/data"
    csv_files = ['chengkou.csv', 'guojia.csv', 'lianghe.csv', 'yantang.csv']
    
    all_results = {}
    
    print("=" * 60)
    print("降水预报产品统计分析")
    print("=" * 60)
    
    for csv_file in csv_files:
        filepath = os.path.join(data_dir, csv_file)
        if os.path.exists(filepath):
            station_name = csv_file.replace('.csv', '')
            results = analyze_file(filepath)
            if results:
                all_results[station_name] = results
        else:
            print(f"文件不存在: {filepath}")
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("汇总结果")
    print("=" * 60)
    
    # 创建汇总表格
    forecast_products = ['E1', 'E2', 'China', 'NOAA']
    
    print("\n1. real_tp基本统计量:")
    print(f"{'站点':<10} {'均值':<12} {'标准差':<12} {'最大值':<12} {'数据点数':<10}")
    print("-" * 60)
    for station, results in all_results.items():
        stats = results['real_tp_stats']
        print(f"{station:<10} {stats['mean']:<12.6f} {stats['std']:<12.6f} {stats['max']:<12.6f} {stats['count']:<10}")
    
    print("\n2. 预报产品相关系数:")
    print(f"{'站点':<10} {'E1':<12} {'E2':<12} {'China':<12} {'NOAA':<12}")
    print("-" * 70)
    for station, results in all_results.items():
        metrics = results['forecast_metrics']
        print(f"{station:<10} {metrics['E1']['correlation']:<12.6f} {metrics['E2']['correlation']:<12.6f} "
              f"{metrics['China']['correlation']:<12.6f} {metrics['NOAA']['correlation']:<12.6f}")
    
    print("\n3. 预报产品RMSE:")
    print(f"{'站点':<10} {'E1':<12} {'E2':<12} {'China':<12} {'NOAA':<12}")
    print("-" * 70)
    for station, results in all_results.items():
        metrics = results['forecast_metrics']
        print(f"{station:<10} {metrics['E1']['rmse']:<12.6f} {metrics['E2']['rmse']:<12.6f} "
              f"{metrics['China']['rmse']:<12.6f} {metrics['NOAA']['rmse']:<12.6f}")
    
    print("\n4. 预报产品MAE:")
    print(f"{'站点':<10} {'E1':<12} {'E2':<12} {'China':<12} {'NOAA':<12}")
    print("-" * 70)
    for station, results in all_results.items():
        metrics = results['forecast_metrics']
        print(f"{station:<10} {metrics['E1']['mae']:<12.6f} {metrics['E2']['mae']:<12.6f} "
              f"{metrics['China']['mae']:<12.6f} {metrics['NOAA']['mae']:<12.6f}")

    # 保存结果到CSV文件
    save_results_to_csv(all_results)

def save_results_to_csv(all_results):
    """将分析结果保存到CSV文件"""

    # 1. 保存real_tp基本统计量
    stats_filename = "real_tp_statistics.csv"
    with open(stats_filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['站点', '均值', '标准差', '最大值', '数据点数'])
        for station, results in all_results.items():
            stats = results['real_tp_stats']
            writer.writerow([station, f"{stats['mean']:.6f}", f"{stats['std']:.6f}",
                           f"{stats['max']:.6f}", stats['count']])
    print(f"\nreal_tp统计量已保存到: {stats_filename}")

    # 2. 保存相关系数
    corr_filename = "correlation_coefficients.csv"
    with open(corr_filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['站点', 'E1', 'E2', 'China', 'NOAA'])
        for station, results in all_results.items():
            metrics = results['forecast_metrics']
            writer.writerow([station, f"{metrics['E1']['correlation']:.6f}",
                           f"{metrics['E2']['correlation']:.6f}",
                           f"{metrics['China']['correlation']:.6f}",
                           f"{metrics['NOAA']['correlation']:.6f}"])
    print(f"相关系数已保存到: {corr_filename}")

    # 3. 保存RMSE
    rmse_filename = "rmse_values.csv"
    with open(rmse_filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['站点', 'E1', 'E2', 'China', 'NOAA'])
        for station, results in all_results.items():
            metrics = results['forecast_metrics']
            writer.writerow([station, f"{metrics['E1']['rmse']:.6f}",
                           f"{metrics['E2']['rmse']:.6f}",
                           f"{metrics['China']['rmse']:.6f}",
                           f"{metrics['NOAA']['rmse']:.6f}"])
    print(f"RMSE值已保存到: {rmse_filename}")

    # 4. 保存MAE
    mae_filename = "mae_values.csv"
    with open(mae_filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['站点', 'E1', 'E2', 'China', 'NOAA'])
        for station, results in all_results.items():
            metrics = results['forecast_metrics']
            writer.writerow([station, f"{metrics['E1']['mae']:.6f}",
                           f"{metrics['E2']['mae']:.6f}",
                           f"{metrics['China']['mae']:.6f}",
                           f"{metrics['NOAA']['mae']:.6f}"])
    print(f"MAE值已保存到: {mae_filename}")

    # 5. 保存综合结果表
    summary_filename = "precipitation_analysis_summary.csv"
    with open(summary_filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        # 写入表头
        writer.writerow(['站点', '指标类型', 'E1', 'E2', 'China', 'NOAA',
                        'real_tp_均值', 'real_tp_标准差', 'real_tp_最大值', 'real_tp_数据点数'])

        for station, results in all_results.items():
            metrics = results['forecast_metrics']
            stats = results['real_tp_stats']

            # 相关系数行
            writer.writerow([station, '相关系数',
                           f"{metrics['E1']['correlation']:.6f}",
                           f"{metrics['E2']['correlation']:.6f}",
                           f"{metrics['China']['correlation']:.6f}",
                           f"{metrics['NOAA']['correlation']:.6f}",
                           f"{stats['mean']:.6f}",
                           f"{stats['std']:.6f}",
                           f"{stats['max']:.6f}",
                           stats['count']])

            # RMSE行
            writer.writerow([station, 'RMSE',
                           f"{metrics['E1']['rmse']:.6f}",
                           f"{metrics['E2']['rmse']:.6f}",
                           f"{metrics['China']['rmse']:.6f}",
                           f"{metrics['NOAA']['rmse']:.6f}",
                           '', '', '', ''])

            # MAE行
            writer.writerow([station, 'MAE',
                           f"{metrics['E1']['mae']:.6f}",
                           f"{metrics['E2']['mae']:.6f}",
                           f"{metrics['China']['mae']:.6f}",
                           f"{metrics['NOAA']['mae']:.6f}",
                           '', '', '', ''])

    print(f"综合分析结果已保存到: {summary_filename}")
    print("\n所有结果文件已生成完成！")

if __name__ == "__main__":
    main()
