{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m运行具有“data (Python 3.12.11)”的单元格需要ipykernel包。\n", "\u001b[1;31m使用所需的包 <a href='command:jupyter.createPythonEnvAndSelectController'>创建 Python 环境</a>。\n", "\u001b[1;31m或使用命令“conda install -n data ipykernel --update-deps --force-reinstall”安装“ipykernel”"]}], "source": ["import pandas as pd\n", "import xarray as xr\n", "from herbie import <PERSON>ie\n", "from datetime import datetime\n", "import os\n", "\n", "# --- 1. 参数设置 ---\n", "start_date = \"2024-01-01\"\n", "end_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "lat_slice = slice(32.2, 28.2)\n", "lon_slice = slice(105.3, 110.2)\n", "\n", "# 主输出目录\n", "output_dir = \"/home/<USER>/Flood_flow_prediction/EC\"\n", "\n", "daily_files_dir = os.path.join(output_dir, \"daily_files\")\n", "os.makedirs(daily_files_dir, exist_ok=True)\n", "\n", "final_output_filename = os.path.join(output_dir, f\"ecmwf_ifs_3hourly_precip_{start_date}_to_{end_date}_FINAL.nc\")\n", "\n", "dates = pd.date_range(start=start_date, end=end_date, freq=\"D\")\n", "\n", "print(f\"开始处理从 {start_date} 到 {end_date} 的数据... (每天保存一个文件)\")\n", "\n", "for date in dates:\n", "    print(f\"\\n===== 正在处理日期: {date.strftime('%Y-%m-%d')} =====\")\n", "    \n", "    daily_filename = os.path.join(daily_files_dir, f\"precip_{date.strftime('%Y%m%d')}.nc\")\n", "    \n", "    # 如果当天的文件已经存在，则跳过，实现断点续传\n", "    if os.path.exists(daily_filename):\n", "        print(f\"  文件 {daily_filename} 已存在，跳过。\")\n", "        continue\n", "\n", "    run_date = date.strftime(\"%Y-%m-%d 00:00\")\n", "    tp_prev = 0 \n", "    precip_for_day = []\n", "    \n", "    fxx_steps = range(3, 25, 3)\n", "\n", "    for fxx in fxx_steps:\n", "        try:\n", "            print(f\"  正在获取 F{fxx:02d} ...\")\n", "            H_curr = <PERSON><PERSON>(run_date, model='ifs', product='oper', fxx=fxx)\n", "            ds_curr = H_curr.xarray(':tp:')\n", "            tp_curr = ds_curr.sel(latitude=lat_slice, longitude=lon_slice).tp\n", "\n", "            precip_3hr_m = tp_curr - tp_prev\n", "            precip_3hr_mm = precip_3hr_m * 1000\n", "            \n", "            precip_3hr_mm.attrs['long_name'] = f'3-Hourly Total Precipitation (valid at {fxx:02d}z)'\n", "            precip_3hr_mm.attrs['units'] = 'mm'\n", "            precip_3hr_mm.name = \"precip_3hr\"\n", "\n", "            precip_3hr_mm = precip_3hr_mm.expand_dims(\n", "                time=[ds_curr.valid_time.values]\n", "            )\n", "            precip_for_day.append(precip_3hr_mm)\n", "            tp_prev = tp_curr\n", "\n", "        except Exception as e:\n", "            print(f\"  处理 {run_date} F{fxx:02d} 失败。错误: {e}\")\n", "            continue\n", "            \n", "\n", "    if precip_for_day:\n", "        daily_ds = xr.concat(precip_for_day, dim=\"time\")\n", "        daily_ds.to_netcdf(daily_filename)\n", "        print(f\"完成日期: {date.strftime('%Y-%m-%d')}，数据已保存至 {daily_filename}\")\n", "\n", "print(\"\\n===== 所有日期的下载任务已完成。开始合并所有每日文件... =====\")\n", "\n", "daily_files = [os.path.join(daily_files_dir, f) for f in os.listdir(daily_files_dir) if f.endswith('.nc')]\n", "daily_files.sort() \n", "\n", "if daily_files:\n", "    final_ds = xr.open_mfdataset(daily_files, combine='by_coords')\n", "    \n", "    final_ds.attrs['description'] = '3-hourly precipitation data for Chongqing from ECMWF IFS model.'\n", "    final_ds.attrs['source'] = 'ECMWF Open Data'\n", "    \n", "    final_ds.to_netcdf(final_output_filename)\n", "    \n", "    print(f\"\\n成功！所有每日文件已合并至最终文件: {final_output_filename}\")\n", "    print(\"\\n最终数据集概览:\")\n", "    print(final_ds)\n", "else:\n", "    print(\"\\n在 daily_files 目录中没有找到任何nc文件可供合并。\")"]}], "metadata": {"kernelspec": {"display_name": "data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}