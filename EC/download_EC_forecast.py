import pandas as pd
import xarray as xr
from herbie import Herbie
from datetime import datetime
import os

# --- 1. 参数设置 ---
start_date = "2024-01-01"
end_date = datetime.now().strftime("%Y-%m-%d")
lat_slice = slice(32.2, 28.2)
lon_slice = slice(105.3, 110.2)

# 主输出目录
output_dir = "/home/<USER>/Flood_flow_prediction/EC"

daily_files_dir = os.path.join(output_dir, "daily_files")
os.makedirs(daily_files_dir, exist_ok=True)

final_output_filename = os.path.join(output_dir, f"ecmwf_ifs_3hourly_precip_{start_date}_to_{end_date}_FINAL.nc")

dates = pd.date_range(start=start_date, end=end_date, freq="D")

print(f"开始处理从 {start_date} 到 {end_date} 的数据... (每天保存一个文件)")

for date in dates:
    print(f"\n===== 正在处理日期: {date.strftime('%Y-%m-%d')} =====")
    
    daily_filename = os.path.join(daily_files_dir, f"precip_{date.strftime('%Y%m%d')}.nc")
    
    # 如果当天的文件已经存在，则跳过，实现断点续传
    if os.path.exists(daily_filename):
        print(f"  文件 {daily_filename} 已存在，跳过。")
        continue

    run_date = date.strftime("%Y-%m-%d 00:00")
    tp_prev = 0 
    precip_for_day = []
    
    fxx_steps = range(3, 25, 3)

    for fxx in fxx_steps:
        try:
            print(f"  正在获取 F{fxx:02d} ...")
            H_curr = Herbie(run_date, model='ifs', product='oper', fxx=fxx)
            ds_curr = H_curr.xarray(':tp:')
            tp_curr = ds_curr.sel(latitude=lat_slice, longitude=lon_slice).tp

            precip_3hr_m = tp_curr - tp_prev
            precip_3hr_mm = precip_3hr_m * 1000
            
            precip_3hr_mm.attrs['long_name'] = f'3-Hourly Total Precipitation (valid at {fxx:02d}z)'
            precip_3hr_mm.attrs['units'] = 'mm'
            precip_3hr_mm.name = "precip_3hr"

            precip_3hr_mm = precip_3hr_mm.expand_dims(
                time=[ds_curr.valid_time.values]
            )
            precip_for_day.append(precip_3hr_mm)
            tp_prev = tp_curr

        except Exception as e:
            print(f"  处理 {run_date} F{fxx:02d} 失败。错误: {e}")
            continue
            

    if precip_for_day:
        daily_ds = xr.concat(precip_for_day, dim="time")
        daily_ds.to_netcdf(daily_filename)
        print(f"完成日期: {date.strftime('%Y-%m-%d')}，数据已保存至 {daily_filename}")

print("\n===== 所有日期的下载任务已完成。开始合并所有每日文件... =====")

daily_files = [os.path.join(daily_files_dir, f) for f in os.listdir(daily_files_dir) if f.endswith('.nc')]
daily_files.sort() 

if daily_files:
    final_ds = xr.open_mfdataset(daily_files, combine='by_coords')
    
    final_ds.attrs['description'] = '3-hourly precipitation data for Chongqing from ECMWF IFS model.'
    final_ds.attrs['source'] = 'ECMWF Open Data'
    
    final_ds.to_netcdf(final_output_filename)
    
    print(f"\n成功！所有每日文件已合并至最终文件: {final_output_filename}")
    print("\n最终数据集概览:")
    print(final_ds)
else:
    print("\n在 daily_files 目录中没有找到任何nc文件可供合并。")