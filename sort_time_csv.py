#!/usr/bin/env python3
"""
Script to sort CSV file by time column
"""

import pandas as pd
import sys
from datetime import datetime

def sort_csv_by_time(input_file, output_file=None):
    """
    Sort CSV file by time column

    Args:
        input_file (str): Path to input CSV file
        output_file (str): Path to output CSV file (optional, defaults to input_file)
    """
    try:
        # Read the CSV file
        print(f"Reading CSV file: {input_file}")
        df = pd.read_csv(input_file)

        # Check if 'time' column exists
        if 'time' not in df.columns:
            print("Error: 'time' column not found in CSV file")
            return False

        print(f"Original data shape: {df.shape}")
        print(f"First few rows before sorting:")
        print(df.head())

        # Convert time column to datetime with explicit format
        print("Converting time column to datetime...")
        # Handle the format "2024/1/1 0:00" - note the single digit month/day and hour
        df['datetime'] = pd.to_datetime(df['time'], format='%Y/%m/%d %H:%M')

        # Sort by datetime
        print("Sorting by datetime...")
        df_sorted = df.sort_values('datetime').reset_index(drop=True)

        # Drop the temporary datetime column and keep original time format
        df_sorted = df_sorted.drop('datetime', axis=1)

        print(f"First few rows after sorting:")
        print(df_sorted.head())

        # Save to output file
        if output_file is None:
            output_file = input_file

        print(f"Saving sorted data to: {output_file}")
        df_sorted.to_csv(output_file, index=False)

        print(f"Successfully sorted and saved {len(df_sorted)} rows")
        return True

    except Exception as e:
        print(f"Error processing file: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python sort_time_csv.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = sort_csv_by_time(input_file, output_file)
    if not success:
        sys.exit(1)
