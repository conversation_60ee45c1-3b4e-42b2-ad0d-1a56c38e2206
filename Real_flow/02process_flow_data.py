import csv
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def parse_datetime(date_str):
    """解析日期时间字符串"""
    return datetime.strptime(date_str, '%Y-%m-%d %H:%M')

def format_datetime(dt):
    """格式化日期时间"""
    return dt.strftime('%Y-%m-%d %H:%M')

def format_significant_digits(value, digits=3):
    """保留指定位数的有效数字"""
    if value == 0:
        return 0
    import math
    return round(value, -int(math.floor(math.log10(abs(value)))) + (digits - 1))

def linear_interpolate(x1, y1, x2, y2, x):
    """线性插值"""
    if x2 == x1:
        return y1
    return y1 + (x - x1) * (y2 - y1) / (x2 - x1)

def process_csv_file(input_file, output_file):
    print(f"Processing {input_file}...")

    # 读取CSV文件 - 首先读取所有有效数据用于创建水位-流量对照表
    all_data = []
    hourly_data = []
    with open(input_file, 'r', encoding='utf-8-sig') as f:  # 使用utf-8-sig自动处理BOM
        reader = csv.DictReader(f)

        # 打印列名以便调试
        print(f"CSV columns: {reader.fieldnames}")

        for row in reader:
            try:
                # 处理可能的列名变化（去除BOM或其他特殊字符）
                date_key = None
                water_key = None
                flow_key = None

                for key in row.keys():
                    key_clean = key.strip().replace('\ufeff', '')  # 去除BOM和空格
                    if '日期时间' in key_clean:
                        date_key = key
                    elif '水位' in key_clean:
                        water_key = key
                    elif '流量' in key_clean:
                        flow_key = key

                if not all([date_key, water_key, flow_key]):
                    print(f"Could not find required columns in row: {row}")
                    continue

                dt = parse_datetime(row[date_key])
                water_level = float(row[water_key])
                flow_rate = float(row[flow_key])

                # 收集所有有效的数据点（包括非整点）用于水位-流量对照表
                all_data.append({
                    '日期时间': dt,
                    '水位(m)': water_level,
                    '流量(m³/s)': flow_rate
                })

                # 同时收集整点数据用于后续时间序列处理
                if dt.minute == 0:  # 只保留整点数据用于时间序列
                    hourly_data.append({
                        '日期时间': dt,
                        '水位(m)': water_level,
                        '流量(m³/s)': flow_rate
                    })
            except (ValueError, KeyError) as e:
                print(f"Skipping invalid row: {row}, error: {e}")

    # 立即创建水位-流量对照表（使用所有数据，包括非整点观测）
    print(f"Creating water level-flow rate lookup table from {len(all_data)} data points...")
    water_flow_pairs = []
    for row in all_data:
        water_level = row['水位(m)']
        flow = row['流量(m³/s)']
        # 去重
        pair = (water_level, flow)
        if pair not in water_flow_pairs:
            water_flow_pairs.append(pair)

    # 按水位升序排序
    water_flow_pairs.sort(key=lambda x: x[0])
    print(f"Created lookup table with {len(water_flow_pairs)} unique water level-flow rate pairs")

    # 按时间排序整点数据
    hourly_data.sort(key=lambda x: x['日期时间'])

    # 2. 创建完整的时间序列（从2024-01-01 08:00到2025-06-20 14:00，间隔1小时）
    start_time = datetime(2024, 1, 1, 8, 0)
    end_time = datetime(2025, 7, 31, 8, 0)

    # 创建完整时间序列
    complete_times = []
    current_time = start_time
    while current_time <= end_time:
        complete_times.append(current_time)
        current_time += timedelta(hours=1)

    # 创建时间到数据的映射
    time_data_map = {row['日期时间']: row for row in hourly_data}

    # 创建完整的数据集
    complete_data = []
    for time_point in complete_times:
        if time_point in time_data_map:
            complete_data.append(time_data_map[time_point])
        else:
            complete_data.append({
                '日期时间': time_point,
                '水位(m)': None,
                '流量(m³/s)': None
            })

    # 3. 对缺失的水位进行线性插值
    for i in range(len(complete_data)):
        if complete_data[i]['水位(m)'] is None:
            # 找到前后有效的水位数据
            prev_idx = i - 1
            next_idx = i + 1

            while prev_idx >= 0 and complete_data[prev_idx]['水位(m)'] is None:
                prev_idx -= 1
            while next_idx < len(complete_data) and complete_data[next_idx]['水位(m)'] is None:
                next_idx += 1

            if prev_idx >= 0 and next_idx < len(complete_data):
                # 线性插值
                prev_time = complete_data[prev_idx]['日期时间']
                next_time = complete_data[next_idx]['日期时间']
                current_time = complete_data[i]['日期时间']

                prev_water = complete_data[prev_idx]['水位(m)']
                next_water = complete_data[next_idx]['水位(m)']

                # 时间差（小时）
                total_hours = (next_time - prev_time).total_seconds() / 3600
                current_hours = (current_time - prev_time).total_seconds() / 3600

                interpolated_water = linear_interpolate(0, prev_water, total_hours, next_water, current_hours)
                complete_data[i]['水位(m)'] = round(interpolated_water, 2)  # 保留小数点后两位
            elif prev_idx >= 0:
                # 使用前一个有效值
                complete_data[i]['水位(m)'] = complete_data[prev_idx]['水位(m)']
            elif next_idx < len(complete_data):
                # 使用后一个有效值
                complete_data[i]['水位(m)'] = complete_data[next_idx]['水位(m)']

    # 4. 根据水位查找或插值计算流量
    # 使用之前创建的水位-流量对照表（已包含所有数据点，包括非整点观测）

    # 对于每个缺失的流量值，根据水位查找或插值计算
    for i in range(len(complete_data)):
        if complete_data[i]['流量(m³/s)'] is None:
            water_level = complete_data[i]['水位(m)']

            # 检查是否有完全匹配的水位
            exact_match = None
            for wl, flow in water_flow_pairs:
                if abs(wl - water_level) < 1e-6:  # 浮点数比较
                    exact_match = flow
                    break

            if exact_match is not None:
                complete_data[i]['流量(m³/s)'] = exact_match
            else:
                # 找到临近的水位进行插值
                lower_pair = None
                higher_pair = None

                for wl, flow in water_flow_pairs:
                    if wl < water_level:
                        lower_pair = (wl, flow)
                    elif wl > water_level and higher_pair is None:
                        higher_pair = (wl, flow)
                        break

                if lower_pair and higher_pair:
                    # 线性插值
                    lower_water, lower_flow = lower_pair
                    higher_water, higher_flow = higher_pair
                    interpolated_flow = linear_interpolate(lower_water, lower_flow, higher_water, higher_flow, water_level)
                    # 保留三位有效数字
                    interpolated_flow = format_significant_digits(interpolated_flow, 3)
                    complete_data[i]['流量(m³/s)'] = interpolated_flow
                elif lower_pair:
                    # 使用最接近的较小水位对应的流量
                    complete_data[i]['流量(m³/s)'] = lower_pair[1]
                elif higher_pair:
                    # 使用最接近的较大水位对应的流量
                    complete_data[i]['流量(m³/s)'] = higher_pair[1]

    # 保存处理后的数据
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['日期时间', '水位(m)', '流量(m³/s)'])
        for row in complete_data:
            writer.writerow([
                format_datetime(row['日期时间']),
                row['水位(m)'],
                row['流量(m³/s)']
            ])

    print(f"Processed data saved to {output_file}")
    print(f"Total records: {len(complete_data)}")

# 确保输出目录存在
os.makedirs('Real_flow/csv-use', exist_ok=True)

# 处理两个CSV文件
process_csv_file('Real_flow/csv/dongxi.csv', 'Real_flow/csv-use/dongxi_use.csv')
process_csv_file('Real_flow/csv/mituo.csv', 'Real_flow/csv-use/mituo_use.csv')

print("Data processing completed!")
