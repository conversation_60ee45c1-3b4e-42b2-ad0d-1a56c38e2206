#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理yantang.csv文件的数据
处理规则：
1. 填充缺失的流量值（根据水位查找相同水位的流量值，或使用水位-流量映射表）, 小于映射表中最小水位的流量值填充为0
2. 删除非整点时间数据
3. 补全缺失的时间点（使用缺失片段前后两个时间点的插值水位，然后通过水位查找相应的流量或使用水位-流量映射表查找，小于映射表中最小水位的流量值填充为0）
"""

import csv
import datetime
from datetime import timedelta
import pandas as pd
import numpy as np

def calculate_flow_with_extrapolation(water_level, mapping, existing_mapping):
    """
    根据水位计算流量，支持外推和插值，保留三位有效数字
    """
    # 先查找现有数据中相同水位的流量
    if water_level in existing_mapping:
        flow = existing_mapping[water_level]
        return format_flow_value(flow), "exact_match"

    # 再查找映射表中精确匹配的水位
    if water_level in mapping:
        flow = mapping[water_level]
        return format_flow_value(flow), "exact_match"

    if not mapping:
        return "0", "no_mapping"

    known_levels = sorted(mapping.keys())
    known_flows = [mapping[level] for level in known_levels]

    min_level = min(known_levels)
    max_level = max(known_levels)

    if water_level < min_level:
        # 小于最小水位，填充为0
        return "0", "low_extrapolation"
    elif water_level > max_level:
        # 高水位外推
        if len(known_levels) >= 2:
            # 使用最高两个点计算斜率
            level1, level2 = known_levels[-2], known_levels[-1]
            flow1, flow2 = known_flows[-2], known_flows[-1]
            slope = (flow2 - flow1) / (level2 - level1)

            # 线性外推
            extrapolated_flow = flow2 + slope * (water_level - level2)
            # 确保流量为正
            extrapolated_flow = max(0.001, extrapolated_flow)
            return format_flow_value(extrapolated_flow), "high_extrapolation"
        else:
            return format_flow_value(known_flows[-1]), "high_extrapolation"
    else:
        # 范围内插值
        interpolated_flow = np.interp(water_level, known_levels, known_flows)
        return format_flow_value(interpolated_flow), "interpolation"

def format_flow_value(flow):
    """格式化流量值，保留三位有效数字"""
    if flow == 0:
        return "0"
    else:
        # 计算有效数字
        import math
        magnitude = math.floor(math.log10(abs(flow)))
        factor = 10 ** (2 - magnitude)  # 保留3位有效数字
        rounded_flow = round(flow * factor) / factor
        return str(rounded_flow)

print("开始处理yantang.csv数据...")

# 读取原始数据
with open("Real_flow/csv-use/yantang.csv", 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    rows = list(reader)

print(f"原始数据行数：{len(rows)}")

# 步骤1：填充缺失的流量值
print("步骤1：填充缺失的流量值")

# 加载映射表（表头是中文的"水位"和"流量"）
mapping = {}
try:
    with open("Real_flow/q-z/Q-Z(yantang).csv", 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            water_level = float(row['水位'])
            flow_rate = float(row['流量'])
            mapping[water_level] = flow_rate
except (UnicodeDecodeError, KeyError):
    try:
        with open("Real_flow/q-z/Q-Z(yantang).csv", 'r', encoding='gbk') as f:
            reader = csv.DictReader(f)
            for row in reader:
                water_level = float(row['水位'])
                flow_rate = float(row['流量'])
                mapping[water_level] = flow_rate
    except (UnicodeDecodeError, KeyError):
        # 如果还是有问题，直接读取并手动解析
        print("使用手动解析方式读取映射表...")
        with open("Real_flow/q-z/Q-Z(yantang).csv", 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[1:]:  # 跳过标题行
                parts = line.strip().split(',')
                if len(parts) >= 2:
                    try:
                        water_level = float(parts[0])
                        flow_rate = float(parts[1])
                        mapping[water_level] = flow_rate
                    except ValueError:
                        continue

print(f"映射表记录数：{len(mapping)}")
if mapping:
    min_level = min(mapping.keys())
    max_level = max(mapping.keys())
    print(f"水位范围：{min_level} - {max_level}")

# 建立现有水位-流量映射
existing_mapping = {}
missing_count = 0

# 使用原始数据建立映射
header = rows[0]
for row in rows[1:]:
    if len(row) >= 3:
        try:
            if row[1].strip() and row[2].strip():
                water_level = float(row[1])
                flow_rate = float(row[2])
                existing_mapping[water_level] = flow_rate
            elif row[1].strip() and not row[2].strip():
                missing_count += 1
        except ValueError:
            pass

print(f"现有水位-流量对：{len(existing_mapping)}个")
print(f"缺失流量记录：{missing_count}个")

# 填充缺失流量
filled_count = 0
zero_count = 0

for row in rows[1:]:
    if len(row) >= 3 and row[1].strip() and not row[2].strip():
        try:
            water_level = float(row[1])
            
            # 先查找现有数据中相同水位的流量
            if water_level in existing_mapping:
                row[2] = str(existing_mapping[water_level])
                filled_count += 1
            else:
                # 查找映射表
                if water_level < min_level:
                    row[2] = "0"
                    zero_count += 1
                    filled_count += 1
                elif water_level in mapping:
                    row[2] = str(mapping[water_level])
                    filled_count += 1
                else:
                    # 线性插值
                    levels = sorted(mapping.keys())
                    for i in range(len(levels)-1):
                        if levels[i] <= water_level <= levels[i+1]:
                            x1, y1 = levels[i], mapping[levels[i]]
                            x2, y2 = levels[i+1], mapping[levels[i+1]]
                            if x2 != x1:
                                flow = y1 + (y2-y1) * (water_level-x1) / (x2-x1)
                                row[2] = f"{flow:.6f}"
                                filled_count += 1
                            break
        except ValueError:
            pass

print(f"填充流量：{filled_count}条")
print(f"填充为0：{zero_count}条")

# 步骤2：删除非整点时间数据
print("\n步骤2：删除非整点时间数据")

# 过滤整点时间
valid_rows = [header]
removed_count = 0

for row in rows[1:]:
    if len(row) >= 3:
        time_str = row[0].strip()
        try:
            parsed_time = datetime.datetime.strptime(time_str, '%Y-%m-%d %H:%M')
            if parsed_time.minute == 0:
                valid_rows.append(row)
            else:
                removed_count += 1
        except ValueError:
            removed_count += 1

print(f"删除非整点时间：{removed_count}条")
print(f"保留数据：{len(valid_rows)-1}条")

# 步骤3：补全时间序列
print("\n步骤3：补全时间序列")

# 转换为DataFrame以便使用pandas的插值功能
df_data = []
for row in valid_rows[1:]:
    if len(row) >= 3:
        try:
            time_obj = datetime.datetime.strptime(row[0], '%Y-%m-%d %H:%M')
            water_level = float(row[1]) if row[1].strip() else None
            flow_rate = float(row[2]) if row[2].strip() else None
            df_data.append({
                '时间': time_obj,
                '水位(m)': water_level,
                '流量(m3/s)': flow_rate
            })
        except (ValueError, TypeError):
            pass

df = pd.DataFrame(df_data)
print(f"现有数据点：{len(df)}个")

# 生成完整时间序列并使用pandas进行插值
start_time = datetime.datetime(2024, 1, 1, 8, 0)
end_time = datetime.datetime(2025, 5, 10, 17, 0)

# 创建完整的时间序列
complete_times = pd.date_range(start=start_time, end=end_time, freq='H')
print(f"完整时间序列应有 {len(complete_times)} 个时间点")

# 创建完整的DataFrame
complete_df = pd.DataFrame({'时间': complete_times})
df_merged = complete_df.merge(df, on='时间', how='left')

missing_count = df_merged['水位(m)'].isna().sum()
print(f"缺失的时间点数量: {missing_count}")

if missing_count > 0:
    print("正在使用线性插值补全缺失的水位...")

    # 对水位进行线性插值（渐变插值）
    df_merged['水位(m)'] = df_merged['水位(m)'].interpolate(method='linear')
    df_merged['水位(m)'] = df_merged['水位(m)'].round(2)  # 保留小数点后两位

    # 为新时间点计算流量
    new_stats = {
        'exact_match': 0,
        'low_extrapolation': 0,
        'high_extrapolation': 0,
        'interpolation': 0,
        'no_mapping': 0
    }

    filled_flow_count = 0
    for idx, row in df_merged.iterrows():
        if pd.isna(row['流量(m3/s)']) and not pd.isna(row['水位(m)']):
            water_level = row['水位(m)']
            flow_value, method = calculate_flow_with_extrapolation(water_level, mapping, existing_mapping)
            df_merged.at[idx, '流量(m3/s)'] = flow_value
            new_stats[method] += 1
            filled_flow_count += 1

    print(f"为新时间点计算流量值统计:")
    print(f"  精确匹配: {new_stats['exact_match']} 个")
    print(f"  低水位填充为0: {new_stats['low_extrapolation']} 个")
    print(f"  范围内插值: {new_stats['interpolation']} 个")
    print(f"  高水位外推: {new_stats['high_extrapolation']} 个")
    print(f"  总计: {filled_flow_count} 个新时间点")

# 转换回原来的格式
complete_data = []
for _, row in df_merged.iterrows():
    complete_data.append([
        row['时间'].strftime('%Y-%m-%d %H:%M'),
        str(row['水位(m)']) if not pd.isna(row['水位(m)']) else '',
        str(row['流量(m3/s)']) if not pd.isna(row['流量(m3/s)']) else ''
    ])

added_count = filled_flow_count if missing_count > 0 else 0

print(f"完整时间序列：{len(complete_data)}个点")
print(f"补充数据点：{added_count}个")

# 保存最终结果
output_file = "Real_flow/csv-use/yantang_processed.csv"
with open(output_file, 'w', encoding='utf-8', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(['时间', '水位(m)', '流量(m3/s)'])
    writer.writerows(complete_data)

print(f"\n处理完成！最终输出文件：{output_file}")

# 验证结果
expected_count = int((end_time - start_time).total_seconds() / 3600) + 1
print(f"\n验证结果：")
print(f"期望时间点数：{expected_count}")
print(f"实际时间点数：{len(complete_data)}")

# 检查前几行和后几行
print("\n前5行数据：")
for i, row in enumerate(complete_data[:5]):
    print(f"  {i+1}: {row}")

print("\n后5行数据：")
for i, row in enumerate(complete_data[-5:], len(complete_data)-4):
    print(f"  {i}: {row}")

print("\n=== 处理统计信息 ===")
print(f"原始数据行数：{len(rows) - 1}")
print(f"填充缺失流量：{filled_count}条")
print(f"删除非整点时间：{removed_count}条")
print(f"保留整点时间：{len(valid_rows) - 1}条")
print(f"补充时间点：{added_count}条")
print(f"最终数据行数：{len(complete_data)}条")
print(f"数据完整率：{(len(valid_rows) - 1) / len(complete_data) * 100:.1f}%")

print("\n数据处理完成！")
