#!/usr/bin/env python3
"""
两河流域的流量数据处理pipeline
"""

import pandas as pd
from datetime import datetime, timedelta
import os
import numpy as np

def interpolate_missing_flow(df):
    """
    通过插值计算缺失的流量值
    """
    # 获取有效的水位-流量数据用于插值
    valid_data = df.dropna(subset=['water_level', 'flow_rate'])

    if len(valid_data) < 2:
        print(f"      ❌ 有效数据点不足，无法进行插值")
        return df

    # 按水位排序，去除重复水位（保留第一个）
    valid_data_sorted = valid_data.drop_duplicates(subset=['water_level']).sort_values('water_level')

    water_levels = valid_data_sorted['water_level'].values
    flow_rates = valid_data_sorted['flow_rate'].values

    print(f"      用于插值的有效数据点: {len(valid_data_sorted)}")
    print(f"      水位范围: {water_levels.min():.3f} - {water_levels.max():.3f} m")

    try:
        # 找出缺失值并进行插值
        missing_mask = df['flow_rate'].isna()
        missing_data = df[missing_mask]

        print(f"      插值结果:")

        for idx, row in missing_data.iterrows():
            water_level = row['water_level']

            # 进行线性插值计算
            interpolated_flow = linear_interpolate(water_level, water_levels, flow_rates)

            # 保留三位有效数字
            if interpolated_flow != 0:
                magnitude = int(np.floor(np.log10(abs(interpolated_flow))))
                rounded_flow = round(interpolated_flow, 2 - magnitude)
            else:
                rounded_flow = 0.000

            # 更新数据
            df.loc[idx, 'flow_rate'] = rounded_flow

            print(f"        时间: {row['time']}, 水位: {water_level:.3f}m -> 流量: {rounded_flow} m³/s")

            # 检查是否为外推
            if water_level < water_levels.min() or water_level > water_levels.max():
                print(f"          ⚠️  使用外推计算")

        return df

    except Exception as e:
        print(f"      ❌ 插值计算失败: {e}")
        return df

def linear_interpolate(x, x_points, y_points):
    """
    简单的线性插值函数
    """
    # 如果x在范围内，进行插值
    if x <= x_points.min():
        # 外推：使用最小的两个点
        if len(x_points) >= 2:
            x1, x2 = x_points[0], x_points[1]
            y1, y2 = y_points[0], y_points[1]
        else:
            return y_points[0]
    elif x >= x_points.max():
        # 外推：使用最大的两个点
        if len(x_points) >= 2:
            x1, x2 = x_points[-2], x_points[-1]
            y1, y2 = y_points[-2], y_points[-1]
        else:
            return y_points[-1]
    else:
        # 内插：找到x所在的区间
        for i in range(len(x_points) - 1):
            if x_points[i] <= x <= x_points[i + 1]:
                x1, x2 = x_points[i], x_points[i + 1]
                y1, y2 = y_points[i], y_points[i + 1]
                break

    # 线性插值公式: y = y1 + (y2 - y1) * (x - x1) / (x2 - x1)
    if x2 - x1 == 0:
        return y1
    else:
        return y1 + (y2 - y1) * (x - x1) / (x2 - x1)

def process_flow_data(file_path, start_time, end_time, output_path=None):
    """
    处理流量数据文件，包括：
    1. 删除非整点时间数据
    2. 填充缺失的流量值（通过相同水位查找）
    3. 补全缺失的时间点（使用前一个时间点的数据）
    """
    print("=" * 80)
    print(f"处理文件: {file_path}")
    print(f"时间范围: {start_time} 到 {end_time}")
    print("=" * 80)

    # 读取CSV文件
    try:
        df = pd.read_csv(file_path)
        print(f"✅ 成功读取文件，共 {len(df)} 行数据")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

    # 检查列名并标准化
    print(f"📋 原始列名: {list(df.columns)}")

    # 重命名列以便统一处理
    if '时间' in df.columns:
        df = df.rename(columns={'时间': 'time', '水位(m)': 'water_level', '流量(m3/s)': 'flow_rate'})
    elif 'time' not in df.columns:
        # 假设第一列是时间，第二列是水位，第三列是流量
        df.columns = ['time', 'water_level', 'flow_rate']

    print(f"📋 标准化后列名: {list(df.columns)}")

    # 转换时间列
    try:
        df['time'] = pd.to_datetime(df['time'])
        print(f"✅ 时间列转换成功")
    except Exception as e:
        print(f"❌ 时间列转换失败: {e}")
        return None

    # 转换数值列
    try:
        df['water_level'] = pd.to_numeric(df['water_level'], errors='coerce')
        df['flow_rate'] = pd.to_numeric(df['flow_rate'], errors='coerce')
        print(f"✅ 数值列转换成功")
    except Exception as e:
        print(f"❌ 数值列转换失败: {e}")
        return None

    print(f"\n📊 原始数据统计:")
    print(f"   总行数: {len(df)}")
    print(f"   水位缺失值: {df['water_level'].isna().sum()}")
    print(f"   流量缺失值: {df['flow_rate'].isna().sum()}")
    print(f"   时间范围: {df['time'].min()} 到 {df['time'].max()}")

    # 步骤1: 删除非整点时间数据
    print(f"\n🔧 步骤1: 删除非整点时间数据...")
    before_filter = len(df)

    # 只保留整点数据（分钟和秒都为0）
    df = df[df['time'].dt.minute == 0]
    df = df[df['time'].dt.second == 0]

    after_filter = len(df)
    removed_count = before_filter - after_filter
    print(f"   删除了 {removed_count} 个非整点时间数据")
    print(f"   剩余数据: {after_filter} 行")

    # 步骤2: 填充缺失的流量值
    print(f"\n🔧 步骤2: 填充缺失的流量值...")
    flow_missing_before = df['flow_rate'].isna().sum()

    if flow_missing_before > 0:
        # 创建水位-流量映射表（使用非缺失数据）
        valid_data = df.dropna(subset=['water_level', 'flow_rate'])
        water_flow_map = valid_data.groupby('water_level')['flow_rate'].first().to_dict()

        # 填充缺失的流量值
        mask = df['flow_rate'].isna() & df['water_level'].notna()
        df.loc[mask, 'flow_rate'] = df.loc[mask, 'water_level'].map(water_flow_map)

        flow_missing_after = df['flow_rate'].isna().sum()
        filled_count = flow_missing_before - flow_missing_after
        print(f"   通过水位映射填充了 {filled_count} 个流量值")
        print(f"   剩余流量缺失值: {flow_missing_after}")

        # 输出剩余缺失值的详细信息并进行插值
        if flow_missing_after > 0:
            missing_flow_data = df[df['flow_rate'].isna()]
            print(f"\n   ⚠️  剩余 {flow_missing_after} 个无法通过水位映射填充的流量缺失值:")
            for idx, row in missing_flow_data.iterrows():
                print(f"      时间: {row['time']}, 水位: {row['water_level']}")

            # 通过插值计算缺失的流量值
            print(f"\n   🔧 通过插值计算缺失的流量值...")
            df = interpolate_missing_flow(df)

            # 重新统计缺失值
            flow_missing_final = df['flow_rate'].isna().sum()
            interpolated_count = flow_missing_after - flow_missing_final
            print(f"   通过插值填充了 {interpolated_count} 个流量值")
            print(f"   最终剩余流量缺失值: {flow_missing_final}")
    else:
        print(f"   无流量缺失值需要填充")

    # 步骤3: 生成完整的时间序列并补全缺失数据
    print(f"\n🔧 步骤3: 补全缺失的时间点...")

    # 生成期望的完整时间序列
    start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M")
    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M")

    expected_times = []
    current_time = start_dt
    while current_time <= end_dt:
        expected_times.append(current_time)
        current_time += timedelta(hours=1)

    expected_df = pd.DataFrame({'time': expected_times})
    print(f"   期望时间点数量: {len(expected_times)}")

    # 合并数据，保留所有期望的时间点
    df_complete = expected_df.merge(df, on='time', how='left')

    # 只对新增的缺失时间点使用前向填充补全数据（不填充原有的缺失值）
    # 先标记原有的缺失值位置
    original_missing_mask = df_complete['time'].isin(df[df['flow_rate'].isna()]['time'])

    # 使用前向填充补全缺失数据
    df_complete['water_level'] = df_complete['water_level'].ffill()
    df_complete['flow_rate'] = df_complete['flow_rate'].ffill()

    # 如果开始时间点没有数据，使用后向填充
    df_complete['water_level'] = df_complete['water_level'].bfill()
    df_complete['flow_rate'] = df_complete['flow_rate'].bfill()

    # 将原有的缺失值重新设置为NaN（不进行填充）
    df_complete.loc[original_missing_mask, 'flow_rate'] = float('nan')

    missing_filled = len(expected_times) - after_filter
    print(f"   补全了 {missing_filled} 个缺失时间点")

    # 最终统计
    print(f"\n📊 处理后数据统计:")
    print(f"   总行数: {len(df_complete)}")
    print(f"   水位缺失值: {df_complete['water_level'].isna().sum()}")
    print(f"   流量缺失值: {df_complete['flow_rate'].isna().sum()}")
    print(f"   时间范围: {df_complete['time'].min()} 到 {df_complete['time'].max()}")

    # 重命名列回原始格式
    df_complete = df_complete.rename(columns={'time': '时间', 'water_level': '水位(m)', 'flow_rate': '流量(m3/s)'})

    # 保存处理后的数据
    if output_path is None:
        output_path = file_path.replace('.csv', '_processed.csv')

    try:
        df_complete.to_csv(output_path, index=False)
        print(f"✅ 处理后的数据已保存到: {output_path}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return None

    return df_complete

def main():
    """
    主函数：处理lianghe2024.csv和lianghe2025.csv文件
    """
    files_to_process = [
        {
            'file_path': 'Real_flow/csv/lianghe2024.csv',
            'start_time': '2024-01-01 08:00',
            'end_time': '2025-01-01 07:00'
        },
        {
            'file_path': 'Real_flow/csv/lianghe2025.csv',
            'start_time': '2025-01-01 08:00',
            'end_time': '2025-05-10 17:00'
        }
    ]

    for file_info in files_to_process:
        if os.path.exists(file_info['file_path']):
            print(f"\n{'='*100}")
            print(f"开始处理: {file_info['file_path']}")
            print(f"{'='*100}")

            result = process_flow_data(
                file_info['file_path'],
                file_info['start_time'],
                file_info['end_time']
            )

            if result is not None:
                print(f"✅ {file_info['file_path']} 处理完成")
            else:
                print(f"❌ {file_info['file_path']} 处理失败")
        else:
            print(f"⚠️  文件不存在: {file_info['file_path']}")

if __name__ == "__main__":
    main()
