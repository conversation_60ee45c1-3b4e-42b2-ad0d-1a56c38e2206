#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析yinhe.csv文件中的时间点问题
1. 检查不规则（非整点）的时间点统计个数并给出范围
2. 检查2024-01-01 08:00到2025-05-10 17:00以1小时为时间间隔记录一次数据，是否有缺失的时间点，统计个数并给出范围
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re

def analyze_yinhe_csv():
    try:
        # 读取CSV文件
        print("正在读取CSV文件...")
        df = pd.read_csv('Real_flow/csv-use/yinhe.csv')

        print("=== yinhe.csv 时间点分析报告 ===\n")
        print(f"数据总行数: {len(df)}")
        print(f"时间范围: {df['时间'].iloc[0]} 到 {df['时间'].iloc[-1]}\n")
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 1. 检查不规则（非整点）的时间点
    print("1. 检查不规则（非整点）的时间点")
    print("-" * 50)
    
    # 将时间列转换为datetime格式
    df['datetime'] = pd.to_datetime(df['时间'])
    
    # 检查非整点时间（分钟和秒不为0的时间）
    irregular_times = []
    for idx, dt in enumerate(df['datetime']):
        if dt.minute != 0 or dt.second != 0:
            irregular_times.append({
                'index': idx + 2,  # CSV行号（包含表头）
                'time': df['时间'].iloc[idx],
                'datetime': dt
            })
    
    if irregular_times:
        print(f"发现 {len(irregular_times)} 个不规则（非整点）时间点:")
        for item in irregular_times[:10]:  # 只显示前10个
            print(f"  行 {item['index']}: {item['time']}")
        if len(irregular_times) > 10:
            print(f"  ... 还有 {len(irregular_times) - 10} 个")
        
        # 统计范围
        first_irregular = irregular_times[0]['time']
        last_irregular = irregular_times[-1]['time']
        print(f"\n不规则时间点范围: {first_irregular} 到 {last_irregular}")
    else:
        print("未发现不规则（非整点）时间点，所有时间都是整点时间。")
    
    # 2. 检查缺失的时间点
    print("\n2. 检查缺失的时间点")
    print("-" * 50)
    
    # 定义期望的时间范围
    start_time = datetime(2024, 1, 1, 8, 0)
    end_time = datetime(2025, 5, 10, 17, 0)
    
    print(f"期望时间范围: {start_time} 到 {end_time}")
    
    # 生成期望的完整时间序列（每小时一次）
    expected_times = []
    current_time = start_time
    while current_time <= end_time:
        expected_times.append(current_time)
        current_time += timedelta(hours=1)
    
    print(f"期望时间点总数: {len(expected_times)}")
    
    # 获取实际存在的时间点
    actual_times = set(df['datetime'])
    expected_times_set = set(expected_times)
    
    # 找出缺失的时间点
    missing_times = expected_times_set - actual_times
    missing_times_list = sorted(list(missing_times))
    
    # 找出多余的时间点（在期望范围外的）
    extra_times = actual_times - expected_times_set
    extra_times_list = sorted(list(extra_times))
    
    print(f"实际时间点总数: {len(actual_times)}")
    print(f"缺失时间点数量: {len(missing_times_list)}")
    print(f"多余时间点数量: {len(extra_times_list)}")
    
    if missing_times_list:
        print(f"\n缺失时间点范围: {missing_times_list[0]} 到 {missing_times_list[-1]}")
        print("前10个缺失时间点:")
        for i, missing_time in enumerate(missing_times_list[:10]):
            print(f"  {missing_time}")
        if len(missing_times_list) > 10:
            print(f"  ... 还有 {len(missing_times_list) - 10} 个")
    else:
        print("在期望范围内未发现缺失的时间点。")
    
    if extra_times_list:
        print(f"\n多余时间点（期望范围外）:")
        print(f"多余时间点范围: {extra_times_list[0]} 到 {extra_times_list[-1]}")
        print("前10个多余时间点:")
        for i, extra_time in enumerate(extra_times_list[:10]):
            print(f"  {extra_time}")
        if len(extra_times_list) > 10:
            print(f"  ... 还有 {len(extra_times_list) - 10} 个")
    
    # 3. 检查时间序列的连续性
    print("\n3. 时间序列连续性分析")
    print("-" * 50)
    
    # 按时间排序
    df_sorted = df.sort_values('datetime')
    
    # 计算时间间隔
    time_diffs = df_sorted['datetime'].diff()
    
    # 找出非1小时间隔的地方
    expected_interval = timedelta(hours=1)
    irregular_intervals = []
    
    for idx, diff in enumerate(time_diffs):
        if pd.notna(diff) and diff != expected_interval:
            irregular_intervals.append({
                'index': idx,
                'prev_time': df_sorted['datetime'].iloc[idx-1],
                'curr_time': df_sorted['datetime'].iloc[idx],
                'interval': diff
            })
    
    if irregular_intervals:
        print(f"发现 {len(irregular_intervals)} 个非1小时间隔:")
        for item in irregular_intervals[:10]:
            print(f"  {item['prev_time']} -> {item['curr_time']} (间隔: {item['interval']})")
        if len(irregular_intervals) > 10:
            print(f"  ... 还有 {len(irregular_intervals) - 10} 个")
    else:
        print("所有相邻时间点间隔都是1小时，时间序列连续。")
    
    # 4. 检查流量缺失值及对应的水位范围
    print("\n4. 流量缺失值分析")
    print("-" * 50)

    # 检查流量列中的缺失值、空值、异常值
    flow_col = '流量(m3/s)'
    water_level_col = '水位(m)'

    # 转换为数值类型，无法转换的会变成NaN
    df['flow_numeric'] = pd.to_numeric(df[flow_col], errors='coerce')
    df['water_level_numeric'] = pd.to_numeric(df[water_level_col], errors='coerce')

    # 找出流量缺失的行
    flow_missing_mask = df['flow_numeric'].isna()
    flow_missing_count = flow_missing_mask.sum()

    print(f"流量缺失值数量: {flow_missing_count}")

    if flow_missing_count > 0:
        # 获取流量缺失时对应的水位值
        missing_water_levels = df.loc[flow_missing_mask, 'water_level_numeric'].dropna()

        if len(missing_water_levels) > 0:
            min_water_level = missing_water_levels.min()
            max_water_level = missing_water_levels.max()
            mean_water_level = missing_water_levels.mean()

            print(f"流量缺失时对应的水位范围: {min_water_level:.3f}m - {max_water_level:.3f}m")
            print(f"流量缺失时对应的水位平均值: {mean_water_level:.3f}m")

            # 显示前几个流量缺失的记录
            missing_records = df[flow_missing_mask][['时间', water_level_col, flow_col]].head(10)
            print("\n前10个流量缺失的记录:")
            for idx, row in missing_records.iterrows():
                print(f"  {row['时间']}: 水位={row[water_level_col]}, 流量={row[flow_col]}")
        else:
            print("流量缺失的记录中水位也都缺失")
    else:
        print("未发现流量缺失值")

    # 检查水位缺失值
    water_level_missing_count = df['water_level_numeric'].isna().sum()
    print(f"\n水位缺失值数量: {water_level_missing_count}")

    # 5. 总结
    print("\n5. 总结")
    print("-" * 50)
    print(f"• 不规则时间点数量: {len(irregular_times)}")
    print(f"• 缺失时间点数量: {len(missing_times_list)}")
    print(f"• 多余时间点数量: {len(extra_times_list)}")
    print(f"• 非1小时间隔数量: {len(irregular_intervals)}")
    print(f"• 流量缺失值数量: {flow_missing_count}")
    print(f"• 水位缺失值数量: {water_level_missing_count}")

    if len(irregular_times) == 0 and len(missing_times_list) == 0 and len(irregular_intervals) == 0:
        print("\n✅ 数据时间序列完整且规范！")
    else:
        print("\n⚠️  数据时间序列存在问题，需要进一步处理。")

    if flow_missing_count > 0 or water_level_missing_count > 0:
        print("⚠️  数据中存在缺失值，需要进一步处理。")

if __name__ == "__main__":
    analyze_yinhe_csv()
