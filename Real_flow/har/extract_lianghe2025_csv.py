import os
import json
import csv

def main():
    # locate the HAR file
    base_dir = os.path.dirname(__file__)
    har_path = os.path.join(base_dir, 'lianghe2025.har')
    # read and clean comments
    with open(har_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    clean_text = ''.join(line for line in lines if not line.strip().startswith('//'))
    har = json.loads(clean_text)
    # find the relevant entry
    entries = har.get('log', {}).get('entries', [])
    for entry in entries:
        url = entry.get('request', {}).get('url', '')
        if 'waterRegimeprocChart.do' in url:
            content_text = entry.get('response', {}).get('content', {}).get('text', '')
            data_obj = json.loads(content_text)
            data = data_obj.get('data', [])
            # write CSV
            csv_path = os.path.join(base_dir, 'lianghe2025.csv')
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['时间', '水位(m)', '流量(m3/s)'])
                for d in data:
                    writer.writerow([d.get('TM'), d.get('Z'), d.get('Q')])
            print(f'CSV written to {csv_path}')
            break

if __name__ == '__main__':
    main()
