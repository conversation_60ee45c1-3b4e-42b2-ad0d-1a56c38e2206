#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR文件批量处理脚本
用于提取洪水流量预测数据并转换为CSV格式
"""

import os
import json
import csv
import glob
from datetime import datetime

def extract_har_to_csv(har_file_path, output_csv_path):
    """
    从HAR文件中提取水位和流量数据，保存为CSV文件
    
    Args:
        har_file_path (str): HAR文件路径
        output_csv_path (str): 输出CSV文件路径
    
    Returns:
        bool: 是否成功提取数据
    """
    try:
        # 读取HAR文件并清理注释
        with open(har_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        clean_text = ''.join(line for line in lines if not line.strip().startswith('//'))
        har = json.loads(clean_text)
        
        # 查找相关的API调用
        entries = har.get('log', {}).get('entries', [])
        data_extracted = False
        
        for entry in entries:
            url = entry.get('request', {}).get('url', '')
            # 查找包含水位流量数据的API调用
            if 'waterRegimeprocChart.do' in url or 'Chart.do' in url:
                content_text = entry.get('response', {}).get('content', {}).get('text', '')
                if content_text:
                    try:
                        data_obj = json.loads(content_text)
                        data = data_obj.get('data', [])
                        
                        if data:
                            # 写入CSV文件
                            with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                                writer = csv.writer(csvfile)
                                writer.writerow(['时间', '水位(m)', '流量(m3/s)'])
                                
                                for d in data:
                                    time_str = d.get('TM', '')
                                    water_level = d.get('Z', '')
                                    flow_rate = d.get('Q', '')
                                    writer.writerow([time_str, water_level, flow_rate])
                            
                            print(f"✓ 成功提取 {len(data)} 条数据记录到 {output_csv_path}")
                            data_extracted = True
                            break
                    except json.JSONDecodeError:
                        continue
        
        if not data_extracted:
            print(f"✗ 在 {har_file_path} 中未找到有效的水位流量数据")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ 处理 {har_file_path} 时出错: {str(e)}")
        return False

def analyze_csv_data(csv_file_path):
    """
    分析CSV文件中的数据统计信息
    
    Args:
        csv_file_path (str): CSV文件路径
    """
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            data = list(reader)
        
        if not data:
            print(f"CSV文件 {csv_file_path} 为空")
            return
        
        # 统计信息
        water_levels = [float(row['水位(m)']) for row in data if row['水位(m)']]
        flow_rates = [float(row['流量(m3/s)']) for row in data if row['流量(m3/s)']]
        
        print(f"\n📊 数据分析 - {os.path.basename(csv_file_path)}:")
        print(f"   记录总数: {len(data)}")
        print(f"   时间范围: {data[0]['时间']} 至 {data[-1]['时间']}")
        
        if water_levels:
            print(f"   水位范围: {min(water_levels):.2f}m - {max(water_levels):.2f}m")
            print(f"   平均水位: {sum(water_levels)/len(water_levels):.2f}m")
        
        if flow_rates:
            print(f"   流量范围: {min(flow_rates):.2f}m³/s - {max(flow_rates):.2f}m³/s")
            print(f"   平均流量: {sum(flow_rates)/len(flow_rates):.2f}m³/s")
            
    except Exception as e:
        print(f"分析CSV文件 {csv_file_path} 时出错: {str(e)}")

def main():
    """主函数：批量处理所有HAR文件"""
    # 获取当前目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 查找所有HAR文件
    har_files = glob.glob(os.path.join(base_dir, '*.har'))
    
    if not har_files:
        print("未找到HAR文件")
        return
    
    print(f"发现 {len(har_files)} 个HAR文件，开始处理...")
    print("=" * 50)
    
    successful_extractions = 0
    
    for har_file in sorted(har_files):
        har_name = os.path.basename(har_file)
        csv_name = har_name.replace('.har', '.csv')
        csv_path = os.path.join(base_dir, csv_name)
        
        print(f"\n处理: {har_name}")
        
        if extract_har_to_csv(har_file, csv_path):
            successful_extractions += 1
            analyze_csv_data(csv_path)
        
        print("-" * 30)
    
    print(f"\n🎉 处理完成！成功提取 {successful_extractions}/{len(har_files)} 个文件的数据")

if __name__ == '__main__':
    print("开始运行HAR文件处理脚本...")
    main()
    print("脚本执行完成！")
