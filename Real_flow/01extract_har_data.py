#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从HAR文件中提取东溪、弥陀水文站的水情数据并保存为CSV文件
"""

import json
import csv
import re
from datetime import datetime

def extract_water_data_from_har(har_file_path, output_csv_path):
    """
    从HAR文件中提取水情数据并保存为CSV
    
    Args:
        har_file_path: HAR文件路径
        output_csv_path: 输出CSV文件路径
    """
    try:
        # 读取HAR文件
        with open(har_file_path, 'r', encoding='utf-8') as f:
            har_data = json.load(f)
        
        # 查找包含水情数据的响应内容
        water_data = None
        for entry in har_data['log']['entries']:
            if 'response' in entry and 'content' in entry['response']:
                content = entry['response']['content']
                if 'text' in content and '--水情过程线' in content['text']:
                    # 解析响应内容中的JSON数据
                    response_text = content['text']
                    try:
                        water_data = json.loads(response_text)
                        break
                    except json.JSONDecodeError:
                        continue
        
        if not water_data or 'data' not in water_data:
            print("未找到有效的水情数据")
            return False
        
        # 提取数据并写入CSV
        with open(output_csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['日期时间', '水位(m)', '流量(m³/s)']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入数据
            data_count = 0
            for record in water_data['data']:
                # 提取时间、水位、流量
                timestamp = record.get('TM', '')
                water_level = record.get('Z', '')
                flow_rate = record.get('Q', '')
                
                # 写入CSV行
                writer.writerow({
                    '日期时间': timestamp,
                    '水位(m)': water_level,
                    '流量(m³/s)': flow_rate
                })
                data_count += 1
            
            print(f"成功提取 {data_count} 条水情数据")
            print(f"数据已保存到: {output_csv_path}")
            
            # 显示数据统计信息
            if data_count > 0:
                print(f"\n数据概览:")
                print(f"时间范围: {water_data['data'][0].get('TM', '')} 到 {water_data['data'][-1].get('TM', '')}")
                
                # 统计有效数据
                valid_water_levels = [float(r['Z']) for r in water_data['data'] if r.get('Z') != '' and r.get('Z') is not None]
                valid_flow_rates = [float(r['Q']) for r in water_data['data'] if r.get('Q') != '' and r.get('Q') is not None]
                
                if valid_water_levels:
                    print(f"水位范围: {min(valid_water_levels):.2f}m 到 {max(valid_water_levels):.2f}m")
                if valid_flow_rates:
                    print(f"流量范围: {min(valid_flow_rates):.2f}m³/s 到 {max(valid_flow_rates):.2f}m³/s")
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {har_file_path}")
        return False
    except json.JSONDecodeError as e:
        print(f"错误: HAR文件格式不正确 - {e}")
        return False
    except Exception as e:
        print(f"错误: {e}")
        return False

def main():
    """主函数"""
    # 定义要处理的文件列表
    files_to_process = [
        {
            "har_file": "Real_flow/har/东溪2024.har",
            "csv_file": "东溪2024年水情数据.csv",
            "description": "东溪水文站2024年"
        },
        {
            "har_file": "Real_flow/har/东溪2025.har",
            "csv_file": "东溪2025年水情数据.csv",
            "description": "东溪水文站2025年"
        },
        {
            "har_file": "Real_flow/har/弥陀2024.har",
            "csv_file": "弥陀2024年水情数据.csv",
            "description": "弥陀水文站2024年"
        },
        {
            "har_file": "Real_flow/har/弥陀2025.har",
            "csv_file": "弥陀2025年水情数据.csv",
            "description": "弥陀水文站2025年"
        }
    ]

    print("开始批量提取水情数据...")
    print("=" * 50)

    success_count = 0
    total_count = len(files_to_process)

    for i, file_info in enumerate(files_to_process, 1):
        print(f"\n[{i}/{total_count}] 正在处理: {file_info['description']}水情数据...")
        success = extract_water_data_from_har(file_info['har_file'], file_info['csv_file'])

        if success:
            success_count += 1
            print(f"✓ {file_info['description']}数据提取成功!")
        else:
            print(f"✗ {file_info['description']}数据提取失败!")

        print("-" * 30)

    print(f"\n批量提取完成!")
    print(f"成功提取: {success_count}/{total_count} 个文件")

    if success_count == total_count:
        print("🎉 所有文件都提取成功!")
    elif success_count > 0:
        print("⚠️  部分文件提取成功")
    else:
        print("❌ 所有文件提取失败!")

if __name__ == "__main__":
    main()
