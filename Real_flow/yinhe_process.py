#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理yinhe.csv文件的脚本
1. 填充缺失的流量值，通过查找相同水位对应的流量值进行填充
2. 剩余未找到相同水位的缺失值，通过水位临近的流量进行插值计算缺失的流量值（保留三位有效数字）
3. 删除所有不规则的非整点数据
4. 检查2024-01-01 08:00到2025-05-10 17:00以1小时为时间间隔记录一次数据，是否有缺失的时间点
5. 补全缺失的时间点，补全规则使用缺失片段前后两个时间点的插值水位（保留小数点后两位）
6. 然后通过水位查找相应的流量，如未找到匹配水位则通过水位临近的流量进行插值计算缺失的流量值（保留三位有效数字）

主要改进：当水位小于或大于已知流量水位范围时，进行合理的外推
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def analyze_water_level_flow_relationship(df):
    """分析水位-流量关系"""
    print("\n=== 分析水位-流量关系 ===")
    
    # 获取有效数据
    valid_data = df.dropna(subset=['流量(m3/s)', '水位(m)'])
    
    if len(valid_data) == 0:
        print("没有有效的水位-流量数据")
        return {}
    
    print(f"有效数据点数量: {len(valid_data)}")
    print(f"水位范围: {valid_data['水位(m)'].min():.2f}m - {valid_data['水位(m)'].max():.2f}m")
    print(f"流量范围: {valid_data['流量(m3/s)'].min():.3f} - {valid_data['流量(m3/s)'].max():.3f} m³/s")
    
    # 创建水位到流量的映射
    water_level_to_flow = {}
    for _, row in valid_data.iterrows():
        water_level = round(row['水位(m)'], 2)
        flow = row['流量(m3/s)']
        
        if water_level not in water_level_to_flow:
            water_level_to_flow[water_level] = []
        water_level_to_flow[water_level].append(flow)
    
    # 计算每个水位的平均流量
    for water_level in water_level_to_flow:
        water_level_to_flow[water_level] = np.mean(water_level_to_flow[water_level])
    
    print(f"建立了 {len(water_level_to_flow)} 个水位-流量映射关系")
    
    # 显示一些关键点
    sorted_levels = sorted(water_level_to_flow.keys())
    print(f"\n关键水位-流量点:")
    for i in [0, len(sorted_levels)//4, len(sorted_levels)//2, 3*len(sorted_levels)//4, -1]:
        if i < len(sorted_levels):
            level = sorted_levels[i]
            flow = water_level_to_flow[level]
            print(f"  水位 {level:.2f}m -> 流量 {flow:.3f} m³/s")
    
    return water_level_to_flow

def calculate_flow_with_extrapolation(water_level, water_level_to_flow):
    """
    根据水位计算流量，支持外推
    """
    known_levels = sorted(water_level_to_flow.keys())
    known_flows = [water_level_to_flow[level] for level in known_levels]
    
    min_level = min(known_levels)
    max_level = max(known_levels)
    
    # 首先尝试精确匹配
    water_level_rounded = round(water_level, 2)
    if water_level_rounded in water_level_to_flow:
        return water_level_to_flow[water_level_rounded], "exact_match"
    
    if water_level < min_level:
        # 低水位外推
        if len(known_levels) >= 2:
            # 使用最低两个点计算斜率
            level1, level2 = known_levels[0], known_levels[1]
            flow1, flow2 = known_flows[0], known_flows[1]
            slope = (flow2 - flow1) / (level2 - level1)
            
            # 线性外推
            extrapolated_flow = flow1 + slope * (water_level - level1)
            # 确保流量不为负，设置最小值
            extrapolated_flow = max(0.001, extrapolated_flow)
            return extrapolated_flow, "low_extrapolation"
        else:
            return known_flows[0], "low_extrapolation"
    
    elif water_level > max_level:
        # 高水位外推
        if len(known_levels) >= 2:
            # 使用最高两个点计算斜率
            level1, level2 = known_levels[-2], known_levels[-1]
            flow1, flow2 = known_flows[-2], known_flows[-1]
            slope = (flow2 - flow1) / (level2 - level1)
            
            # 线性外推
            extrapolated_flow = flow2 + slope * (water_level - level2)
            # 确保流量为正
            extrapolated_flow = max(0.001, extrapolated_flow)
            return extrapolated_flow, "high_extrapolation"
        else:
            return known_flows[-1], "high_extrapolation"
    
    else:
        # 范围内插值
        interpolated_flow = np.interp(water_level, known_levels, known_flows)
        return interpolated_flow, "interpolation"

def process_yinhe_improved():
    """改进版处理函数"""
    print("=== 开始处理yinhe.csv文件（改进版） ===")
    
    # 1. 加载数据
    print("\n正在加载数据...")
    df = pd.read_csv('Real_flow/csv-use/yinhe.csv', encoding='utf-8')
    print(f"原始数据行数: {len(df)}")
    
    # 转换数据类型
    df['时间'] = pd.to_datetime(df['时间'])
    df['水位(m)'] = pd.to_numeric(df['水位(m)'], errors='coerce')
    df['流量(m3/s)'] = pd.to_numeric(df['流量(m3/s)'], errors='coerce')
    
    print(f"缺失流量值的行数: {df['流量(m3/s)'].isna().sum()}")
    print(f"缺失水位值的行数: {df['水位(m)'].isna().sum()}")
    
    # 2. 分析水位-流量关系
    water_level_to_flow = analyze_water_level_flow_relationship(df)
    
    # 3. 填充缺失的流量值
    print(f"\n=== 填充缺失的流量值 ===")
    
    stats = {
        'exact_match': 0,
        'low_extrapolation': 0,
        'high_extrapolation': 0,
        'interpolation': 0
    }
    
    filled_count = 0
    for idx, row in df.iterrows():
        if pd.isna(row['流量(m3/s)']) and not pd.isna(row['水位(m)']):
            water_level = row['水位(m)']
            flow, method = calculate_flow_with_extrapolation(water_level, water_level_to_flow)
            df.at[idx, '流量(m3/s)'] = round(flow, 3)
            stats[method] += 1
            filled_count += 1
    
    print(f"填充统计:")
    print(f"  精确匹配: {stats['exact_match']} 个")
    print(f"  低水位外推: {stats['low_extrapolation']} 个")
    print(f"  范围内插值: {stats['interpolation']} 个")
    print(f"  高水位外推: {stats['high_extrapolation']} 个")
    print(f"  总计填充: {filled_count} 个缺失值")
    
    # 4. 删除非整点数据
    print(f"\n=== 删除非整点数据 ===")
    before_count = len(df)
    irregular_mask = (df['时间'].dt.minute != 0) | (df['时间'].dt.second != 0)
    irregular_count = irregular_mask.sum()
    
    if irregular_count > 0:
        print(f"发现 {irregular_count} 条非整点数据，正在删除...")
        df = df[~irregular_mask].copy()
        print(f"删除后数据行数: {len(df)}")
    else:
        print("未发现非整点数据")
    
    # 5. 补全时间序列
    print(f"\n=== 补全时间序列 ===")
    start_time = datetime(2024, 1, 1, 8, 0)
    end_time = datetime(2025, 5, 10, 17, 0)
    
    complete_times = pd.date_range(start=start_time, end=end_time, freq='H')
    print(f"完整时间序列应有 {len(complete_times)} 个时间点")
    
    complete_df = pd.DataFrame({'时间': complete_times})
    df_merged = complete_df.merge(df, on='时间', how='left')
    
    missing_count = df_merged['水位(m)'].isna().sum()
    print(f"缺失的时间点数量: {missing_count}")
    
    if missing_count > 0:
        print("正在补全缺失的时间点...")
        
        # 对水位进行线性插值
        df_merged['水位(m)'] = df_merged['水位(m)'].interpolate(method='linear')
        df_merged['水位(m)'] = df_merged['水位(m)'].round(2)
        
        # 为新时间点计算流量
        new_stats = {
            'exact_match': 0,
            'low_extrapolation': 0,
            'high_extrapolation': 0,
            'interpolation': 0
        }
        
        filled_flow_count = 0
        for idx, row in df_merged.iterrows():
            if pd.isna(row['流量(m3/s)']) and not pd.isna(row['水位(m)']):
                water_level = row['水位(m)']
                flow, method = calculate_flow_with_extrapolation(water_level, water_level_to_flow)
                df_merged.at[idx, '流量(m3/s)'] = round(flow, 3)
                new_stats[method] += 1
                filled_flow_count += 1
        
        print(f"为新时间点计算流量值统计:")
        print(f"  精确匹配: {new_stats['exact_match']} 个")
        print(f"  低水位外推: {new_stats['low_extrapolation']} 个")
        print(f"  范围内插值: {new_stats['interpolation']} 个")
        print(f"  高水位外推: {new_stats['high_extrapolation']} 个")
        print(f"  总计: {filled_flow_count} 个新时间点")
    
    # 6. 保存结果
    output_file = 'Real_flow/csv-use/yinhe_processed.csv'
    print(f"\n正在保存处理后的数据到 {output_file}...")
    df_merged.to_csv(output_file, index=False, encoding='utf-8')
    
    # 7. 最终统计
    print(f"\n=== 处理完成 ===")
    print(f"最终数据统计:")
    print(f"- 总行数: {len(df_merged)}")
    print(f"- 时间范围: {df_merged['时间'].min()} 到 {df_merged['时间'].max()}")
    print(f"- 水位范围: {df_merged['水位(m)'].min():.2f} 到 {df_merged['水位(m)'].max():.2f}")
    print(f"- 流量范围: {df_merged['流量(m3/s)'].min():.3f} 到 {df_merged['流量(m3/s)'].max():.3f}")
    print(f"- 缺失值: 水位 {df_merged['水位(m)'].isna().sum()}, 流量 {df_merged['流量(m3/s)'].isna().sum()}")
    
    return df_merged

if __name__ == "__main__":
    result = process_yinhe_improved()
    print(f"\n🎉 yinhe.csv 改进版处理完成！")
