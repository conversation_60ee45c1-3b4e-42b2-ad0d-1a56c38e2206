# 城口和国家站数据处理报告

## 处理时间
2025-07-15

## 处理文件
- 输入文件：
  - Real_flow/csv/chengkou.csv
  - Real_flow/csv/guojia.csv
- 输出文件：
  - Real_flow/csv-use/chengkou_use.csv
  - Real_flow/csv-use/guojia_use.csv

## 数据处理步骤

### 1. 删除非整点数据
- 删除所有分钟不为0的记录，只保留整点数据
- 确保所有时间戳都是整点（如08:00, 09:00等）

### 2. 时间序列完整性检查
- 时间范围：2024-01-01 08:00 到 2025-05-10 17:00
- 时间间隔：1小时
- 预期记录数：11,890条

### 3. 缺失时间点补全
- 检查并补全缺失的时间点
- 对缺失的水位数据使用线性插值方法补全
- 水位数据保留小数点后两位

### 4. 流量数据补全
- 基于水位-流量关系表进行流量补全
- 优先查找完全匹配的水位对应的流量
- 如无完全匹配，使用临近水位进行线性插值
- 流量数据保留三位有效数字

## 处理结果

### 城口站 (chengkou.csv)
- 原始记录数：11,029条
- 处理后记录数：11,890条
- 补全记录数：861条
- 时间范围：2024-01-01 08:00 - 2025-05-10 17:00
- 数据完整性：100%

### 国家站 (guojia.csv)
- 原始记录数：12,730条
- 处理后记录数：11,890条
- 删除记录数：840条（超出时间范围或非整点数据）
- 时间范围：2024-01-01 08:00 - 2025-05-10 17:00
- 数据完整性：100%

## 数据质量检查
- ✅ 无缺失时间点
- ✅ 无非整点数据
- ✅ 无缺失水位数据
- ✅ 无缺失流量数据
- ✅ 时间序列连续完整

## 数据格式
- 时间格式：YYYY-MM-DD HH:MM
- 水位精度：小数点后两位
- 流量精度：三位有效数字

## 处理方法说明
1. **线性插值公式**：
   ```
   y = y1 + (x - x1) * (y2 - y1) / (x2 - x1)
   ```
   其中 (x1, y1) 和 (x2, y2) 是已知的两个数据点，x 是需要插值的位置

2. **有效数字保留**：
   使用科学计数法保留指定位数的有效数字

3. **水位-流量关系**：
   从原始数据中提取唯一的水位-流量对，按水位排序建立对照表

## 文件位置
处理后的数据文件保存在 `Real_flow/csv-use/` 目录下：
- chengkou_use.csv
- guojia_use.csv
