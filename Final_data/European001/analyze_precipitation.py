import os
import csv
import math
from datetime import datetime

# 尝试导入matplotlib进行绘图；如果缺失，将在运行时给出友好提示
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    HAS_MPL = True
except Exception:
    HAS_MPL = False

DATA_DIR = os.path.dirname(__file__)
RESULT_DIR = os.path.join(DATA_DIR, 'result')

WATERSHED_FILES = {
    # 'chengkou': 'chengkou001.csv',
    # 'guojia': 'guojia001.csv',
    # 'lianghe': 'lianghe001.csv',
    # 'yantang': 'yantang001.csv',
    'mituo': 'mituo001.csv',
    'dongxi': 'dongxi001.csv',
}


def read_series(csv_path):
    """读取一个CSV文件，返回 (times, forecast, observed)。
    CSV格式：valid_time,tp,real_tp
    时间格式示例：2024/3/28 9:00
    """
    times = []
    forecast = []
    observed = []
    with open(csv_path, 'r', newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader, None)
        for row in reader:
            if not row or len(row) < 3:
                continue
            ts_str, tp_str, real_str = row[0].strip(), row[1].strip(), row[2].strip()
            # 解析时间
            try:
                dt = datetime.strptime(ts_str, '%Y/%m/%d %H:%M')
            except ValueError:
                # 尝试支持无前导零的月份/日期/小时
                try:
                    parts = ts_str.split(' ')
                    date_p = parts[0].split('/')
                    time_p = parts[1].split(':')
                    dt = datetime(int(date_p[0]), int(date_p[1]), int(date_p[2]), int(time_p[0]), int(time_p[1]))
                except Exception:
                    continue
            # 数值
            try:
                tp = float(tp_str)
            except Exception:
                tp = float('nan')
            try:
                real = float(real_str)
            except Exception:
                real = float('nan')
            times.append(dt)
            forecast.append(tp)
            observed.append(real)
    return times, forecast, observed


def finite_pairs(a, b):
    """返回同时为有限数的成对样本索引集合。"""
    out_a, out_b = [], []
    for x, y in zip(a, b):
        if isinstance(x, (int, float)) and isinstance(y, (int, float)) and math.isfinite(x) and math.isfinite(y):
            out_a.append(x)
            out_b.append(y)
    return out_a, out_b


def mean(values):
    if not values:
        return float('nan')
    return sum(values) / float(len(values))


def std(values):
    # 使用总体标准差（与numpy std ddof=0一致）
    if not values:
        return float('nan')
    m = mean(values)
    var = sum((x - m) ** 2 for x in values) / float(len(values))
    return math.sqrt(var)


def pearson_corr(x, y):
    x, y = finite_pairs(x, y)
    n = len(x)
    if n < 2:
        return float('nan')
    mx, my = mean(x), mean(y)
    sx, sy = std(x), std(y)
    if sx == 0 or sy == 0 or not math.isfinite(sx) or not math.isfinite(sy):
        return float('nan')
    cov = sum((xi - mx) * (yi - my) for xi, yi in zip(x, y)) / float(n)
    return cov / (sx * sy)


def rmse(x, y):
    x, y = finite_pairs(x, y)
    n = len(x)
    if n == 0:
        return float('nan')
    return math.sqrt(sum((xi - yi) ** 2 for xi, yi in zip(x, y)) / float(n))


def mae(x, y):
    x, y = finite_pairs(x, y)
    n = len(x)
    if n == 0:
        return float('nan')
    return sum(abs(xi - yi) for xi, yi in zip(x, y)) / float(n)


def write_results_csv(results):
    out_path = os.path.join(RESULT_DIR, 'precipitation_statistics.csv')
    os.makedirs(RESULT_DIR, exist_ok=True)
    with open(out_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([
            'watershed', 'forecast_mean', 'forecast_std',
            'observed_mean', 'observed_std', 'correlation', 'rmse', 'mae'
        ])
        for ws, r in results.items():
            writer.writerow([
                ws,
                f"{r['forecast_mean']:.6f}" if math.isfinite(r['forecast_mean']) else 'nan',
                f"{r['forecast_std']:.6f}" if math.isfinite(r['forecast_std']) else 'nan',
                f"{r['observed_mean']:.6f}" if math.isfinite(r['observed_mean']) else 'nan',
                f"{r['observed_std']:.6f}" if math.isfinite(r['observed_std']) else 'nan',
                f"{r['correlation']:.6f}" if math.isfinite(r['correlation']) else 'nan',
                f"{r['rmse']:.6f}" if math.isfinite(r['rmse']) else 'nan',
                f"{r['mae']:.6f}" if math.isfinite(r['mae']) else 'nan',
            ])
    return out_path


def plot_series(times, forecast, observed, watershed):
    if not HAS_MPL:
        return None
    os.makedirs(RESULT_DIR, exist_ok=True)
    fig, ax = plt.subplots(figsize=(15, 6))
    ax.plot(times, forecast, color='blue', label='tp', linewidth=1)
    ax.plot(times, observed, color='red', label='real_tp', linewidth=1)
    ax.set_xlabel('Time')
    ax.set_ylabel('Precipitation')
    ax.set_title(f'{watershed} precipitation comparison')
    ax.legend()
    ax.grid(alpha=0.3)
    # Date formatting: keep numeric format, avoid month names
    locator = mdates.AutoDateLocator()
    formatter = mdates.DateFormatter('%Y/%m/%d %H:%M')
    ax.xaxis.set_major_locator(locator)
    ax.xaxis.set_major_formatter(formatter)
    fig.autofmt_xdate()
    out_png = os.path.join(RESULT_DIR, f'{watershed}_precipitation_comparison.png')
    fig.tight_layout()
    fig.savefig(out_png, dpi=200, bbox_inches='tight')
    plt.close(fig)
    return out_png


def main():
    results = {}
    for ws, csv_name in WATERSHED_FILES.items():
        csv_path = os.path.join(DATA_DIR, csv_name)
        if not os.path.exists(csv_path):
            print(f'[WARN] File not found: {csv_path}')
            continue
        print(f'Processing {ws} -> {csv_name}')
        times, forecast, observed = read_series(csv_path)
        # 计算统计量（对各自序列的有限值计算）
        f_fin, _ = finite_pairs(forecast, forecast)
        o_fin, _ = finite_pairs(observed, observed)
        stats = {
            'forecast_mean': mean(f_fin),
            'forecast_std': std(f_fin),
            'observed_mean': mean(o_fin),
            'observed_std': std(o_fin),
            'correlation': pearson_corr(forecast, observed),
            'rmse': rmse(forecast, observed),
            'mae': mae(forecast, observed),
        }
        results[ws] = stats
        # 绘图
        png = plot_series(times, forecast, observed, ws)
        if png:
            print(f'Saved figure: {png}')
        else:
            if not HAS_MPL:
                print('matplotlib is not installed; skipping plotting. Install matplotlib to enable plots.')

    out_csv = write_results_csv(results)
    print(f'Statistics saved: {out_csv}')


if __name__ == '__main__':
    main()

