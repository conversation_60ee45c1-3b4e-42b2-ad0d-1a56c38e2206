#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合三个流域的最终数据
时间范围：2024-03-28 9:00 到 2025-05-10 17:00
"""

import pandas as pd
import os
from datetime import datetime

def integrate_watershed_data(watershed_name):
    """
    整合单个流域的数据
    
    Args:
        watershed_name: 流域名称 (lian<PERSON><PERSON>, yantang, yin<PERSON>)
    """
    print(f"正在处理 {watershed_name} 流域数据...")
    
    # 文件路径
    base_file = f"Final_data/product/{watershed_name}.csv"
    tp_file = f"Final_data/tp/{watershed_name}.csv"
    flow_file = f"Final_data/flow/{watershed_name}_use.csv"
    output_file = f"Final_data/{watershed_name}_final.csv"
    
    # 检查文件是否存在
    for file_path in [base_file, tp_file, flow_file]:
        if not os.path.exists(file_path):
            print(f"错误：文件 {file_path} 不存在")
            return False
    
    try:
        # 读取基础数据（前四列）
        print(f"  读取基础数据: {base_file}")
        base_df = pd.read_csv(base_file)
        base_df['time'] = pd.to_datetime(base_df['time'])
        
        # 读取降水数据（tp列）
        print(f"  读取降水数据: {tp_file}")
        tp_df = pd.read_csv(tp_file)
        tp_df['时间'] = pd.to_datetime(tp_df['时间'])
        # 获取最后一列（面上雨量）
        tp_column = tp_df.columns[-1]  # 最后一列
        tp_df = tp_df[['时间', tp_column]].rename(columns={'时间': 'time', tp_column: 'tp'})
        
        # 读取流量数据
        print(f"  读取流量数据: {flow_file}")
        flow_df = pd.read_csv(flow_file)
        flow_df['时间'] = pd.to_datetime(flow_df['时间'])
        # 获取最后一列（流量）
        flow_column = flow_df.columns[-1]  # 最后一列
        flow_df = flow_df[['时间', flow_column]].rename(columns={'时间': 'time', flow_column: 'flow'})
        
        # 时间范围过滤
        start_time = pd.to_datetime('2024-03-28 09:00:00')
        end_time = pd.to_datetime('2025-05-10 17:00:00')
        
        print(f"  过滤时间范围: {start_time} 到 {end_time}")
        
        # 过滤各数据框的时间范围
        base_df = base_df[(base_df['time'] >= start_time) & (base_df['time'] <= end_time)]
        tp_df = tp_df[(tp_df['time'] >= start_time) & (tp_df['time'] <= end_time)]
        flow_df = flow_df[(flow_df['time'] >= start_time) & (flow_df['time'] <= end_time)]
        
        print(f"  基础数据行数: {len(base_df)}")
        print(f"  降水数据行数: {len(tp_df)}")
        print(f"  流量数据行数: {len(flow_df)}")
        
        # 合并数据
        print("  合并数据...")
        # 先合并基础数据和降水数据
        merged_df = pd.merge(base_df, tp_df, on='time', how='left')
        
        # 再合并流量数据
        final_df = pd.merge(merged_df, flow_df, on='time', how='left')
        
        # 重新排列列顺序：time, 001, 002, 003, tp, flow
        column_order = ['time', '001', '002', '003', 'tp', 'flow']
        final_df = final_df[column_order]

        # 保存结果
        print(f"  保存到: {output_file}")
        final_df.to_csv(output_file, index=False)
        
        print(f"  {watershed_name} 流域数据整合完成，共 {len(final_df)} 行数据")
        print(f"  时间范围: {final_df['time'].min()} 到 {final_df['time'].max()}")
        
        # 显示前几行数据作为验证
        print("  前5行数据预览:")
        print(final_df.head())
        print()
        
        return True
        
    except Exception as e:
        print(f"处理 {watershed_name} 流域数据时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始整合三个流域的最终数据...")
    print("=" * 60)
    
    # 流域列表
    watersheds = ['lianghe', 'yantang', 'yinhe']
    
    success_count = 0
    
    for watershed in watersheds:
        if integrate_watershed_data(watershed):
            success_count += 1
        print("-" * 60)
    
    print(f"数据整合完成！成功处理 {success_count}/{len(watersheds)} 个流域")
    
    if success_count == len(watersheds):
        print("所有流域数据整合成功！")
        print("\n生成的文件:")
        for watershed in watersheds:
            output_file = f"Final_data/{watershed}_final.csv"
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / 1024  # KB
                print(f"  - {output_file} ({file_size:.1f} KB)")
    else:
        print("部分流域数据整合失败，请检查错误信息。")

if __name__ == "__main__":
    main()
