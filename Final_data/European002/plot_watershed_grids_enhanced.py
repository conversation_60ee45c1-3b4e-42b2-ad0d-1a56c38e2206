#!/usr/bin/env python3
"""
绘制四个流域的边界和完整的0.25°×0.25°栅格网络图
CSV文件中的latitude,longitude是网格的中心点
"""

import pandas as pd
import numpy as np
import os

# 设置matplotlib使用非交互式后端
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

try:
    import geopandas as gpd
    HAS_GEOPANDAS = True
except ImportError:
    print("Warning: geopandas not available, will skip shapefile plotting")
    HAS_GEOPANDAS = False

def load_csv_data(csv_path):
    """加载CSV数据并提取唯一的经纬度坐标"""
    df = pd.read_csv(csv_path)
    # 获取唯一的经纬度组合
    coords = df[['latitude', 'longitude']].drop_duplicates()
    return coords

def load_shapefile(shp_path):
    """加载shapefile边界数据"""
    if not HAS_GEOPANDAS:
        print("Geopandas not available, skipping shapefile loading")
        return None
    
    if os.path.exists(shp_path):
        try:
            return gpd.read_file(shp_path)
        except Exception as e:
            print(f"Error loading shapefile {shp_path}: {e}")
            return None
    else:
        print(f"Warning: Shapefile {shp_path} not found")
        return None

def plot_watershed_grid_enhanced(csv_path, shp_path, title, ax):
    """绘制单个流域的边界和完整的0.25°×0.25°栅格网络图"""
    
    # 加载栅格数据
    coords = load_csv_data(csv_path)
    
    # 加载边界数据
    boundary = load_shapefile(shp_path)
    
    if coords.empty:
        print(f"No data found in {csv_path}")
        return
    
    # 栅格大小为0.25度
    grid_size = 0.25
    
    print(f"Processing {title}: {len(coords)} grid cells")
    
    # 绘制栅格 (蓝色底色)
    for _, row in coords.iterrows():
        lat_center, lon_center = row['latitude'], row['longitude']
        # 计算网格的左下角坐标（中心点减去半个网格大小）
        lon_left = lon_center - grid_size/2
        lat_bottom = lat_center - grid_size/2
        
        # 创建栅格矩形
        rect = Rectangle(
            (lon_left, lat_bottom), 
            grid_size, grid_size,
            facecolor='lightblue', 
            edgecolor='navy', 
            alpha=0.8,
            linewidth=1.0
        )
        ax.add_patch(rect)
        
        # 在网格中心添加小点标记（显示数据点位置）
        ax.plot(lon_center, lat_center, 'ko', markersize=2, alpha=0.8)
    
    # 绘制完整的网格线
    if not coords.empty:
        # 获取数据范围
        lat_min, lat_max = coords['latitude'].min(), coords['latitude'].max()
        lon_min, lon_max = coords['longitude'].min(), coords['longitude'].max()
        
        # 计算网格边界
        lat_grid_min = lat_min - grid_size/2
        lat_grid_max = lat_max + grid_size/2
        lon_grid_min = lon_min - grid_size/2
        lon_grid_max = lon_max + grid_size/2
        
        # 生成网格线位置
        lat_lines = np.arange(lat_grid_min, lat_grid_max + grid_size/2, grid_size)
        lon_lines = np.arange(lon_grid_min, lon_grid_max + grid_size/2, grid_size)
        
        # 绘制经线（垂直线）
        for lon in lon_lines:
            ax.axvline(x=lon, color='darkblue', linewidth=1.2, alpha=0.9)
        
        # 绘制纬线（水平线）
        for lat in lat_lines:
            ax.axhline(y=lat, color='darkblue', linewidth=1.2, alpha=0.9)
    
    # 绘制流域边界 (红色线条)
    if boundary is not None:
        boundary.boundary.plot(ax=ax, color='red', linewidth=3, alpha=0.9)
    
    # 设置图表属性
    ax.set_title(f'{title}\n(0.25° × 0.25° Grid)', fontsize=14, fontweight='bold')
    ax.set_xlabel('Longitude (°)', fontsize=12)
    ax.set_ylabel('Latitude (°)', fontsize=12)
    
    # 设置坐标轴范围
    if not coords.empty:
        lat_min, lat_max = coords['latitude'].min(), coords['latitude'].max()
        lon_min, lon_max = coords['longitude'].min(), coords['longitude'].max()
        
        # 添加边距，确保显示完整网格
        lat_range = max(lat_max - lat_min, 0.25)
        lon_range = max(lon_max - lon_min, 0.25)
        
        lat_margin = max(lat_range * 0.15, grid_size)
        lon_margin = max(lon_range * 0.15, grid_size)
        
        ax.set_xlim(lon_min - lon_margin, lon_max + lon_margin)
        ax.set_ylim(lat_min - lat_margin, lat_max + lat_margin)
    
    # 设置坐标轴刻度
    ax.tick_params(axis='both', which='major', labelsize=10)
    
    # 添加网格信息文本
    grid_count = len(coords)
    ax.text(0.02, 0.98, f'Grid cells: {grid_count}', 
            transform=ax.transAxes, fontsize=10, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

def main():
    """主函数：绘制四个流域的图表"""
    
    # 定义文件路径
    watersheds = [
        # {
        #     'name': 'chengkou',
        #     'csv': 'Final_data/European002/chengkou002.csv',
        #     'shp': 'boundary/chengkou.shp',
        #     'title': 'Chengkou Watershed'
        # },
        # {
        #     'name': 'guojia',
        #     'csv': 'Final_data/European002/guojia002.csv',
        #     'shp': 'boundary/guojia.shp',
        #     'title': 'Guojia Watershed'
        # },
        # {
        #     'name': 'lianghe',
        #     'csv': 'Final_data/European002/lianghe002.csv',
        #     'shp': 'boundary/lianghe.shp',
        #     'title': 'Lianghe Watershed'
        # },
        # {
        #     'name': 'yantang',
        #     'csv': 'Final_data/European002/yantang002.csv',
        #     'shp': 'boundary/yantang.shp',
        #     'title': 'Yantang Watershed'
        # }
        {
            'name': 'mituo',
            'csv': 'Final_data/European002/mituo002.csv',
            'shp': 'boundary/mituo.shp',
            'title': 'Mituo Watershed'
        },
        {
            'name': 'dongxi',
            'csv': 'Final_data/European002/dongxi002.csv',
            'shp': 'boundary/dongxi.shp',
            'title': 'Dongxi Watershed'
        },
    ]
    
    # 创建2x2的子图布局
    fig, axes = plt.subplots(2, 2, figsize=(18, 14))
    axes = axes.flatten()
    
    # 绘制每个流域
    for i, watershed in enumerate(watersheds):
        print(f"Processing {watershed['name']}...")
        plot_watershed_grid_enhanced(
            watershed['csv'], 
            watershed['shp'], 
            watershed['title'], 
            axes[i]
        )
    
    # 添加总标题
    fig.suptitle('Watershed Grid Networks (0.25° × 0.25° Resolution)', 
                 fontsize=16, fontweight='bold', y=0.95)
    
    # 添加图例
    legend_elements = [
        mpatches.Rectangle((0, 0), 1, 1, facecolor='lightblue', edgecolor='navy', 
                          alpha=0.8, label='0.25° × 0.25° Grid Cell'),
        mpatches.Patch(color='red', label='Watershed Boundary'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='k', 
                   markersize=6, label='Grid Center Point')
    ]
    fig.legend(handles=legend_elements, loc='lower center', ncol=3, 
               bbox_to_anchor=(0.5, 0.02), fontsize=12)
    
    # 调整子图间距
    plt.tight_layout(rect=[0, 0.08, 1, 0.92])
    
    # 保存图片
    output_path = 'Final_data/European002/watershed_grids_enhanced.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Enhanced plot saved to: {output_path}")
    
    print("Enhanced plot generation completed.")

if __name__ == "__main__":
    main()
