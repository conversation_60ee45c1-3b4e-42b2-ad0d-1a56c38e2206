import pandas as pd
import os
from pathlib import Path

def process_csv_files():
    """
    分别处理European002文件夹下的四个CSV文件，对每个文件内相同valid_time的不同经纬度的tp值进行求和
    """
    # 定义文件路径
    base_path = Path("Final_data/European002")
    output_path = base_path / "tp"

    # 确保输出目录存在
    output_path.mkdir(exist_ok=True)

    # 定义要处理的CSV文件
    csv_files = [
        "chengkou002.csv",
        "guojia002.csv",
        "lianghe002.csv",
        "yantang002.csv"
    ]

    print("开始分别处理每个CSV文件...")

    # 分别处理每个CSV文件
    for csv_file in csv_files:
        file_path = base_path / csv_file
        if file_path.exists():
            print(f"\n正在处理: {csv_file}")

            # 读取CSV文件
            df = pd.read_csv(file_path)
            print(f"  - 读取了 {len(df)} 行数据")

            # 只保留需要的列
            df_filtered = df[['valid_time', 'tp']].copy()

            # 按valid_time分组，对该文件内的tp值求和
            result_df = df_filtered.groupby('valid_time')['tp'].sum().reset_index()
            print(f"  - 处理后有 {len(result_df)} 个时间点")
            print(f"  - 时间范围: {result_df['valid_time'].min()} 到 {result_df['valid_time'].max()}")
            print(f"  - tp值范围: {result_df['tp'].min():.6f} 到 {result_df['tp'].max():.6f}")

            # 生成输出文件名
            output_filename = csv_file.replace('.csv', '_tp_sum.csv')
            output_file_path = output_path / output_filename

            # 保存结果
            result_df.to_csv(output_file_path, index=False)
            print(f"  - 保存结果到: {output_file_path}")

        else:
            print(f"警告: 文件 {csv_file} 不存在")

    print(f"\n所有文件处理完成!")

    return True

if __name__ == "__main__":
    # 先清空tp目录中的旧文件
    tp_path = Path("Final_data/European002/tp")
    if tp_path.exists():
        for file in tp_path.glob("*.csv"):
            file.unlink()
            print(f"删除旧文件: {file}")

    result = process_csv_files()

    if result:
        print("\n验证处理结果...")
        # 显示每个输出文件的前几行作为验证
        for csv_file in ["chengkou002.csv", "guojia002.csv", "lianghe002.csv", "yantang002.csv"]:
            output_file = tp_path / csv_file.replace('.csv', '_tp_sum.csv')
            if output_file.exists():
                df = pd.read_csv(output_file)
                print(f"\n{csv_file.replace('.csv', '_tp_sum.csv')} 前5行:")
                print(df.head())
