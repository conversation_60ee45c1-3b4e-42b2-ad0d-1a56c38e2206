import os
from pathlib import Path
import math

# Third-party libs (commonly available). If missing, the run will fail and we'll report back.
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt


def compute_metrics(df: pd.DataFrame):
    tp = pd.to_numeric(df["tp"], errors="coerce")
    real = pd.to_numeric(df["real_tp"], errors="coerce")

    # Drop rows where both are NaN for fair stats
    valid = (~tp.isna()) | (~real.isna())
    tp = tp[valid]
    real = real[valid]

    stats = {
        "n": int(len(df)),
        "tp_mean": float(tp.mean()),
        "tp_std": float(tp.std(ddof=1)),
        "real_tp_mean": float(real.mean()),
        "real_tp_std": float(real.std(ddof=1)),
        "corr": float(tp.corr(real)) if tp.notna().any() and real.notna().any() else float("nan"),
        "rmse": float(np.sqrt(np.nanmean((tp - real) ** 2))),
        "mae": float(np.nanmean(np.abs(tp - real))),
    }
    return stats


def plot_series(df: pd.DataFrame, basin_name: str, out_path: Path):
    # Ensure datetime index
    time = pd.to_datetime(df["valid_time"], errors="coerce")
    tp = pd.to_numeric(df["tp"], errors="coerce")
    real = pd.to_numeric(df["real_tp"], errors="coerce")

    plt.figure(figsize=(14, 4))
    plt.plot(time, tp, color="blue", label="tp", linewidth=1.0)
    plt.plot(time, real, color="red", label="real_tp", linewidth=1.0)
    plt.xlabel("Time")
    plt.ylabel("Precipitation")
    plt.title(f"{basin_name} basin: Forecast (tp) vs Observed (real_tp)")
    plt.legend()
    plt.tight_layout()
    plt.grid(True, alpha=0.3)
    plt.savefig(out_path, dpi=150)
    plt.close()


def main():
    base_dir = Path(__file__).parent
    csv_paths = sorted(base_dir.glob("E2_*.csv"))

    if not csv_paths:
        print(f"No CSV files found in {base_dir}")
        return

    rows = []

    for csv_path in csv_paths:
        basin = csv_path.stem.replace("E2_", "")
        df = pd.read_csv(csv_path)
        # Basic column presence check
        required_cols = {"valid_time", "tp", "real_tp"}
        if not required_cols.issubset(df.columns):
            print(f"Skip {csv_path.name}: missing required columns {required_cols}")
            continue

        metrics = compute_metrics(df)
        metrics_row = {
            "basin": basin,
            **metrics,
            "csv_file": csv_path.name,
        }
        rows.append(metrics_row)

        # Plot
        plot_out = base_dir / f"{csv_path.stem}_timeseries.png"
        try:
            plot_series(df, basin, plot_out)
            print(f"Saved plot: {plot_out}")
        except Exception as e:
            print(f"Failed to plot {csv_path.name}: {e}")

    if rows:
        summary_df = pd.DataFrame(rows, columns=[
            "basin", "csv_file", "n", "tp_mean", "tp_std", "real_tp_mean", "real_tp_std", "corr", "rmse", "mae"
        ])
        # Round for readability
        summary_rounded = summary_df.copy()
        for col in ["tp_mean", "tp_std", "real_tp_mean", "real_tp_std", "corr", "rmse", "mae"]:
            summary_rounded[col] = summary_rounded[col].astype(float).round(6)
        out_csv = base_dir / "summary_stats.csv"
        summary_rounded.to_csv(out_csv, index=False)
        print(f"Saved summary: {out_csv}")
    else:
        print("No valid rows to summarize.")


if __name__ == "__main__":
    main()

