#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NOAA GFS Watershed Precipitation Data Analysis Script
Analyze precipitation forecast vs actual data for chengkou, guojia, lianghe, yantang watersheds
"""

import csv
import math
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import os

# Set font for better display
plt.rcParams['font.size'] = 12
plt.rcParams['axes.unicode_minus'] = False

def calculate_stats(data):
    """Calculate mean and standard deviation"""
    n = len(data)
    if n == 0:
        return 0, 0
    
    mean_val = sum(data) / n
    variance = sum((x - mean_val) ** 2 for x in data) / n
    std_val = math.sqrt(variance)
    return mean_val, std_val

def calculate_correlation(x, y):
    """Calculate Pearson correlation coefficient"""
    n = len(x)
    if n == 0:
        return 0
    
    mean_x = sum(x) / n
    mean_y = sum(y) / n
    
    numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
    sum_sq_x = sum((x[i] - mean_x) ** 2 for i in range(n))
    sum_sq_y = sum((y[i] - mean_y) ** 2 for i in range(n))
    
    denominator = math.sqrt(sum_sq_x * sum_sq_y)
    if denominator == 0:
        return 0
    
    return numerator / denominator

def calculate_rmse(actual, predicted):
    """Calculate RMSE"""
    n = len(actual)
    if n == 0:
        return 0
    mse = sum((actual[i] - predicted[i]) ** 2 for i in range(n)) / n
    return math.sqrt(mse)

def calculate_mae(actual, predicted):
    """Calculate MAE"""
    n = len(actual)
    if n == 0:
        return 0
    return sum(abs(actual[i] - predicted[i]) for i in range(n)) / n

def load_and_analyze_watershed(file_path, watershed_name, output_dir):
    """
    Load and analyze single watershed data
    """
    print(f"\n=== Analyzing {watershed_name} Watershed (NOAA GFS Data) ===")
    
    # Read data
    times = []
    tp_values = []
    real_tp_values = []
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different time column names
            time_col = 'valid_time' if 'valid_time' in row else 'time'
            times.append(datetime.strptime(row[time_col], '%Y/%m/%d %H:%M'))
            tp_values.append(float(row['tp']))
            real_tp_values.append(float(row['real_tp']))
    
    print(f"Data points: {len(tp_values)}")
    
    # Basic statistics
    tp_mean, tp_std = calculate_stats(tp_values)
    real_tp_mean, real_tp_std = calculate_stats(real_tp_values)
    
    # Calculate correlation coefficient
    correlation = calculate_correlation(tp_values, real_tp_values)
    
    # Calculate RMSE and MAE
    rmse = calculate_rmse(real_tp_values, tp_values)
    mae = calculate_mae(real_tp_values, tp_values)
    
    # Print statistical results
    print(f"Forecast precipitation (tp):")
    print(f"  Mean: {tp_mean:.4f}")
    print(f"  Std Dev: {tp_std:.4f}")
    print(f"Actual precipitation (real_tp):")
    print(f"  Mean: {real_tp_mean:.4f}")
    print(f"  Std Dev: {real_tp_std:.4f}")
    print(f"Correlation coefficient: {correlation:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    
    # Create time series plot
    plt.figure(figsize=(15, 8))
    plt.plot(times, tp_values, color='blue', label='Forecast precipitation (tp)', linewidth=1, alpha=0.8)
    plt.plot(times, real_tp_values, color='red', label='Actual precipitation (real_tp)', linewidth=1, alpha=0.8)
    
    plt.title(f'{watershed_name.title()} Watershed - NOAA GFS Forecast vs Actual Precipitation', fontsize=16, fontweight='bold')
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Precipitation', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # Format x-axis dates
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=30))
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    
    # Save plot to specified directory
    output_file = os.path.join(output_dir, f'{watershed_name}_noaa_gfs_precipitation_comparison_en.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Plot saved: {output_file}")
    plt.show()
    
    # Return statistical results
    return {
        'watershed': watershed_name,
        'tp_mean': tp_mean,
        'tp_std': tp_std,
        'real_tp_mean': real_tp_mean,
        'real_tp_std': real_tp_std,
        'correlation': correlation,
        'rmse': rmse,
        'mae': mae,
        'data_points': len(tp_values)
    }

def main():
    """
    Main function: analyze all watershed data
    """
    # Define input and output directories
    input_dir = 'Final_data/NOAAGFS/final(插值+时间偏移)'
    output_dir = 'Final_data/NOAAGFS/result'
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Define file paths and watershed names
    watersheds = {
        # 'chengkou': os.path.join(input_dir, 'chengkou_gfs_final.csv'),
        # 'guojia': os.path.join(input_dir, 'guojia_gfs_final.csv'),
        # 'lianghe': os.path.join(input_dir, 'lianghe_gfs_final.csv'),
        # 'yantang': os.path.join(input_dir, 'yantang_gfs_final.csv')
        'mituo': os.path.join(input_dir, 'mituo_gfs_final.csv'),
        'dongxi': os.path.join(input_dir, 'dongxi_gfs_final.csv')
    }
    
    # Store all results
    all_results = []
    
    # Analyze each watershed
    for watershed_name, file_path in watersheds.items():
        if os.path.exists(file_path):
            result = load_and_analyze_watershed(file_path, watershed_name, output_dir)
            all_results.append(result)
        else:
            print(f"Warning: File {file_path} does not exist")
    
    # Generate summary table
    print("\n" + "="*100)
    print("NOAA GFS Data - Summary of All Watersheds Statistical Results")
    print("="*100)
    
    # Print summary table
    print(f"{'Watershed':<12} {'Forecast':<10} {'Forecast':<12} {'Actual':<10} {'Actual':<12} {'Correlation':<12} {'RMSE':<10} {'MAE':<10}")
    print(f"{'Name':<12} {'Mean':<10} {'Std Dev':<12} {'Mean':<10} {'Std Dev':<12} {'Coefficient':<12} {'':<10} {'':<10}")
    print("-" * 100)
    
    for result in all_results:
        print(f"{result['watershed']:<12} {result['tp_mean']:<10.4f} {result['tp_std']:<12.4f} "
              f"{result['real_tp_mean']:<10.4f} {result['real_tp_std']:<12.4f} "
              f"{result['correlation']:<12.4f} {result['rmse']:<10.4f} {result['mae']:<10.4f}")
    
    # Save summary results to CSV
    summary_file = os.path.join(output_dir, 'noaa_gfs_watershed_analysis_summary_en.csv')
    with open(summary_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['watershed', 'tp_mean', 'tp_std', 'real_tp_mean', 'real_tp_std', 
                     'correlation', 'rmse', 'mae', 'data_points']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for result in all_results:
            writer.writerow(result)
    
    print(f"\nSummary results saved to: {summary_file}")

if __name__ == "__main__":
    main()
