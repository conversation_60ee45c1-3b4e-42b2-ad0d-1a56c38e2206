#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GFS降水数据处理脚本
对不同站点的tp列进行相应的倍数转换
- 城口站: tp列乘以900
- 郭家、梁河、盐塘站: tp列乘以3600
"""

import csv
import os
import pandas as pd

def process_gfs_data():
    """处理GFS降水数据，对tp列进行转换"""

    # 定义输入文件路径和对应的倍数
    file_configs = [
        # {
        #     'input_path': '/home/<USER>/Flood_flow_prediction/GFS_chengkou_data/chengkou_gfs_precipitation_2024-03-28_to_2025-08-05_complete.csv',
        #     'multiplier': 900,
        #     'output_name': 'chengkou_gfs_precipitation_processed.csv'
        # },
        # {
        #     'input_path': '/home/<USER>/Flood_flow_prediction/GFS_guojia_data/guojia_gfs_precipitation_2024-03-28_to_2025-08-05_complete.csv',
        #     'multiplier': 3600,
        #     'output_name': 'guojia_gfs_precipitation_processed.csv'
        # },
        # {
        #     'input_path': '/home/<USER>/Flood_flow_prediction/GFS_lianghe_data/lianghe_gfs_precipitation_2024-03-28_to_2025-08-05_complete.csv',
        #     'multiplier': 3600,
        #     'output_name': 'lianghe_gfs_precipitation_processed.csv'
        # },
        # {
        #     'input_path': '/home/<USER>/Flood_flow_prediction/GFS_yantang_data/yantang_gfs_precipitation_2024-03-28_to_2025-08-05_complete.csv',
        #     'multiplier': 3600,
        #     'output_name': 'yantang_gfs_precipitation_processed.csv'
        # }
        {
            'input_path': '/home/<USER>/Flood_flow_prediction/GFS_mituo_data/mituo_gfs_precipitation_2024-03-28_to_2025-08-24_complete.csv',
            'multiplier': 3600,
            'output_name': 'mituo_gfs_precipitation_processed.csv'
        },
        # {
        #     'input_path': '/home/<USER>/Flood_flow_prediction/GFS_dongxi_data/dongxi_gfs_precipitation_2024-03-28_to_2025-08-25_complete.csv',
        #     'multiplier': 720,
        #     'output_name': 'dongxi_gfs_precipitation_processed.csv'
        # },
    ]

    # 创建输出目录
    output_dir = '/home/<USER>/Flood_flow_prediction/Final_data/NOAAGFS'
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个文件
    for config in file_configs:
        input_path = config['input_path']
        multiplier = config['multiplier']
        output_name = config['output_name']
        output_path = os.path.join(output_dir, output_name)

        print(f"正在处理: {os.path.basename(input_path)}")

        try:
            # 读取CSV文件
            df = pd.read_csv(input_path)

            # 检查是否存在tp列
            if 'tp' not in df.columns:
                print(f"警告: {os.path.basename(input_path)} 中未找到tp列")
                print(f"可用列: {list(df.columns)}")
                continue

            # 复制数据框以避免修改原始数据
            df_processed = df.copy()

            # 对tp列进行转换
            df_processed['tp'] = df_processed['tp'] * multiplier

            # 保存处理后的数据
            df_processed.to_csv(output_path, index=False)

            print(f"✓ 处理完成: {output_name}")
            print(f"  - tp列乘以倍数: {multiplier}")
            print(f"  - 输出路径: {output_path}")
            print(f"  - 数据行数: {len(df_processed)}")
            print(f"  - tp列范围: {df_processed['tp'].min():.6f} ~ {df_processed['tp'].max():.6f}")
            print()

        except FileNotFoundError:
            print(f"错误: 文件不存在 - {input_path}")
        except Exception as e:
            print(f"错误: 处理文件时出现异常 - {input_path}")
            print(f"异常信息: {str(e)}")

    print("所有文件处理完成!")
    print(f"输出目录: {output_dir}")

if __name__ == "__main__":
    process_gfs_data()
