#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GFS降水数据与实际降水数据对比分析
计算统计指标（NSE、相关系数、RMSE、MAE）并绘制对比图
"""

import csv
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime
import matplotlib.dates as mdates

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_nse(observed, predicted):
    """计算纳什效率系数 (Nash-Sutcliffe Efficiency)"""
    if len(observed) == 0:
        return np.nan
    
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    # 计算观测值的平均值
    obs_mean = np.mean(observed)
    
    # 计算NSE
    numerator = np.sum((observed - predicted) ** 2)
    denominator = np.sum((observed - obs_mean) ** 2)
    
    if denominator == 0:
        return np.nan
    
    nse = 1 - (numerator / denominator)
    return nse

def calculate_correlation(x, y):
    """计算相关系数"""
    if len(x) == 0 or len(y) == 0:
        return np.nan
    
    x = np.array(x)
    y = np.array(y)
    
    # 计算皮尔逊相关系数
    correlation_matrix = np.corrcoef(x, y)
    return correlation_matrix[0, 1]

def calculate_rmse(observed, predicted):
    """计算均方根误差 (Root Mean Square Error)"""
    if len(observed) == 0:
        return np.nan
    
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    mse = np.mean((observed - predicted) ** 2)
    rmse = np.sqrt(mse)
    return rmse

def calculate_mae(observed, predicted):
    """计算平均绝对误差 (Mean Absolute Error)"""
    if len(observed) == 0:
        return np.nan
    
    observed = np.array(observed)
    predicted = np.array(predicted)
    
    mae = np.mean(np.abs(observed - predicted))
    return mae

def parse_timestamp(timestamp_str):
    """解析时间戳"""
    try:
        # 尝试解析格式: 2024/3/28 9:00
        return datetime.strptime(timestamp_str, '%Y/%m/%d %H:%M')
    except ValueError:
        try:
            # 尝试解析格式: 2024-03-28 09:00:00
            return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                # 尝试解析格式: 2024-03-28 09:00
                return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M')
            except ValueError:
                return None

def analyze_file(file_path, station_name):
    """分析单个文件的数据"""
    print(f"\n分析站点: {station_name}")
    print("=" * 50)
    
    try:
        timestamps = []
        tp_values = []
        real_tp_values = []
        
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                # 解析时间戳
                timestamp = parse_timestamp(row['valid_time'])
                if timestamp is None:
                    continue
                
                # 解析数值
                try:
                    tp = float(row['tp'])
                    real_tp = float(row['real_tp'])
                    
                    timestamps.append(timestamp)
                    tp_values.append(tp)
                    real_tp_values.append(real_tp)
                except ValueError:
                    continue
        
        if len(tp_values) == 0:
            print("❌ 未找到有效数据")
            return None
        
        print(f"✓ 有效数据点数: {len(tp_values)}")
        print(f"✓ 时间范围: {timestamps[0]} 到 {timestamps[-1]}")
        
        # 计算统计指标
        nse = calculate_nse(real_tp_values, tp_values)
        correlation = calculate_correlation(real_tp_values, tp_values)
        rmse = calculate_rmse(real_tp_values, tp_values)
        mae = calculate_mae(real_tp_values, tp_values)
        
        # 打印统计结果
        print(f"\n统计指标:")
        print(f"  NSE (纳什效率系数): {nse:.4f}")
        print(f"  相关系数 (r): {correlation:.4f}")
        print(f"  RMSE (均方根误差): {rmse:.4f}")
        print(f"  MAE (平均绝对误差): {mae:.4f}")
        
        # 数据描述性统计
        tp_array = np.array(tp_values)
        real_tp_array = np.array(real_tp_values)
        
        print(f"\nGFS降水 (tp) 统计:")
        print(f"  平均值: {np.mean(tp_array):.4f}")
        print(f"  标准差: {np.std(tp_array):.4f}")
        print(f"  最大值: {np.max(tp_array):.4f}")
        print(f"  最小值: {np.min(tp_array):.4f}")
        
        print(f"\n实际降水 (real_tp) 统计:")
        print(f"  平均值: {np.mean(real_tp_array):.4f}")
        print(f"  标准差: {np.std(real_tp_array):.4f}")
        print(f"  最大值: {np.max(real_tp_array):.4f}")
        print(f"  最小值: {np.min(real_tp_array):.4f}")
        
        return {
            'station': station_name,
            'timestamps': timestamps,
            'tp': tp_values,
            'real_tp': real_tp_values,
            'nse': nse,
            'correlation': correlation,
            'rmse': rmse,
            'mae': mae,
            'data_count': len(tp_values)
        }
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
        return None
    except Exception as e:
        print(f"❌ 处理文件时出现错误: {str(e)}")
        return None

def plot_comparison(results_list, output_dir):
    """绘制对比图"""
    if not results_list:
        print("❌ 没有数据可绘制")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 为每个站点创建单独的图
    for results in results_list:
        if results is None:
            continue
            
        station = results['station']
        timestamps = results['timestamps']
        tp = results['tp']
        real_tp = results['real_tp']
        
        # 创建图形
        plt.figure(figsize=(15, 8))
        
        # 绘制时间序列
        plt.plot(timestamps, real_tp, label='实际降水 (real_tp)', color='blue', linewidth=1, alpha=0.7)
        plt.plot(timestamps, tp, label='GFS降水 (tp)', color='red', linewidth=1, alpha=0.7)
        
        plt.title(f'{station}站 GFS降水与实际降水对比\n'
                 f'NSE={results["nse"]:.3f}, r={results["correlation"]:.3f}, '
                 f'RMSE={results["rmse"]:.3f}, MAE={results["mae"]:.3f}', 
                 fontsize=14)
        plt.xlabel('时间', fontsize=12)
        plt.ylabel('降水量 (mm)', fontsize=12)
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        
        # 设置x轴时间格式
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # 保存图形
        output_path = os.path.join(output_dir, f'{station}_precipitation_comparison.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"✓ 保存图形: {output_path}")
        plt.close()
        
        # 创建散点图
        plt.figure(figsize=(10, 8))
        
        max_val = max(max(tp), max(real_tp))
        
        plt.scatter(real_tp, tp, alpha=0.5, s=10)
        plt.plot([0, max_val], [0, max_val], 'r--', label='1:1线')
        
        plt.title(f'{station}站 GFS降水 vs 实际降水散点图\n'
                 f'NSE={results["nse"]:.3f}, r={results["correlation"]:.3f}', 
                 fontsize=14)
        plt.xlabel('实际降水 (real_tp) [mm]', fontsize=12)
        plt.ylabel('GFS降水 (tp) [mm]', fontsize=12)
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        
        # 设置坐标轴相等
        plt.axis('equal')
        plt.xlim(0, max_val * 1.05)
        plt.ylim(0, max_val * 1.05)
        
        plt.tight_layout()
        
        # 保存散点图
        scatter_path = os.path.join(output_dir, f'{station}_precipitation_scatter.png')
        plt.savefig(scatter_path, dpi=300, bbox_inches='tight')
        print(f"✓ 保存散点图: {scatter_path}")
        plt.close()

def main():
    """主函数"""
    print("GFS降水数据与实际降水数据对比分析")
    print("=" * 60)
    
    # 文件配置
    base_dir = '/home/<USER>/Flood_flow_prediction/Final_data/NOAAGFS/final'
    output_dir = '/home/<USER>/Flood_flow_prediction/Final_data/NOAAGFS/analysis_results'
    
    file_configs = [
        {'file': 'chengkou_gfs_final.csv', 'station': '城口'},
        {'file': 'guojia_gfs_final.csv', 'station': '郭家'},
        {'file': 'lianghe_gfs_final.csv', 'station': '梁河'},
        {'file': 'yantang_gfs_final.csv', 'station': '盐塘'}
    ]
    
    # 分析所有文件
    results_list = []
    summary_data = []
    
    for config in file_configs:
        file_path = os.path.join(base_dir, config['file'])
        station_name = config['station']
        
        results = analyze_file(file_path, station_name)
        if results:
            results_list.append(results)
            summary_data.append(results)
    
    # 绘制对比图
    if results_list:
        print(f"\n正在生成图形...")
        plot_comparison(results_list, output_dir)
    
    # 生成汇总报告
    if summary_data:
        print(f"\n{'='*60}")
        print("汇总报告")
        print("="*60)
        print(f"{'站点':<8} {'NSE':<8} {'相关系数':<8} {'RMSE':<8} {'MAE':<8} {'数据点数':<8}")
        print("-"*60)
        
        for data in summary_data:
            print(f"{data['station']:<8} {data['nse']:<8.4f} {data['correlation']:<8.4f} "
                  f"{data['rmse']:<8.4f} {data['mae']:<8.4f} {data['data_count']:<8}")
        
        # 计算平均值
        avg_nse = np.mean([d['nse'] for d in summary_data if not np.isnan(d['nse'])])
        avg_corr = np.mean([d['correlation'] for d in summary_data if not np.isnan(d['correlation'])])
        avg_rmse = np.mean([d['rmse'] for d in summary_data if not np.isnan(d['rmse'])])
        avg_mae = np.mean([d['mae'] for d in summary_data if not np.isnan(d['mae'])])
        
        print("-"*60)
        print(f"{'平均值':<8} {avg_nse:<8.4f} {avg_corr:<8.4f} {avg_rmse:<8.4f} {avg_mae:<8.4f}")
    
    print(f"\n分析完成！结果保存在: {output_dir}")

if __name__ == "__main__":
    main()
