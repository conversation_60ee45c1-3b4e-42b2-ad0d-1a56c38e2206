# GFS降水数据与实际降水数据对比分析报告

> **重要更新**：本报告基于2025年8月16日修正后的数据重新生成，数据完整率从之前的91.5%-99.3%提升至100%。

## 分析概况
本报告对比分析了四个水文站的GFS模型降水预报数据(tp)与实际观测降水数据(real_tp)，评估了GFS模型在这些站点的降水预报精度。经过数据修正，所有站点数据完整率均达到100%。

## 数据统计

| 站点 | 数据点数 | 时间范围开始 | 时间范围结束 | 数据完整率 | 时间跨度(天) |
|------|----------|--------------|--------------|------------|--------------|
| 城口 | 11,101 | 2024-03-28 09:00 | 2025-07-03 21:00 | 100.0% | 462 |
| 郭家 | 10,783 | 2024-03-28 09:00 | 2025-06-20 15:00 | 100.0% | 449 |
| 梁河 | 10,236 | 2024-03-28 09:00 | 2025-05-28 20:00 | 100.0% | 426 |
| 盐塘 | 10,237 | 2024-03-28 09:00 | 2025-05-28 21:00 | 100.0% | 426 |

## 统计指标评估

### 主要评估指标

| 站点 | NSE | 相关系数(r) | RMSE(mm) | MAE(mm) | 评估等级 |
|------|-----|-------------|----------|---------|----------|
| 城口 | -0.3075 | 0.3965 | 0.6942 | 0.1968 | 一般 |
| 郭家 | -0.0524 | 0.3887 | 0.8198 | 0.2239 | 一般 |
| 梁河 | 0.1307 | 0.4056 | 0.8694 | 0.1882 | 较好 |
| 盐塘 | -0.2048 | 0.3817 | 0.8659 | 0.2037 | 一般 |
| **平均** | **-0.1085** | **0.3931** | **0.8123** | **0.2032** | **一般** |

### 指标解释

**NSE (Nash-Sutcliffe Efficiency)**
- 范围: -∞ to 1，1表示完美预测
- 梁河站表现最好(0.1307)，城口站表现最差(-0.3075)
- 负值表明模型预测效果不如使用观测平均值

**相关系数 (r)**
- 范围: -1 to 1，1表示完全正相关
- 所有站点都显示中等程度正相关(0.38-0.41)
- 梁河站相关性最强(0.4056)

**RMSE (均方根误差)**
- 单位: mm，值越小越好
- 城口站误差最小(0.6942mm)，梁河站误差最大(0.8694mm)

**MAE (平均绝对误差)**
- 单位: mm，值越小越好
- 梁河站表现最好(0.1882mm)，郭家站表现最差(0.2239mm)

## 降水统计特征

### GFS降水(tp)统计

| 站点 | 平均值(mm) | 标准差(mm) | 最大值(mm) | 最小值(mm) |
|------|------------|------------|------------|------------|
| 城口 | 0.1952 | 0.6470 | 12.0026 | 0.0000 |
| 郭家 | 0.2038 | 0.6681 | 11.3708 | 0.0000 |
| 梁河 | 0.1573 | 0.5488 | 12.1948 | 0.0000 |
| 盐塘 | 0.1929 | 0.7655 | 29.2236 | 0.0000 |

### 实际降水(real_tp)统计

| 站点 | 平均值(mm) | 标准差(mm) | 最大值(mm) | 最小值(mm) |
|------|------------|------------|------------|------------|
| 城口 | 0.1165 | 0.6071 | 15.4261 | 0.0000 |
| 郭家 | 0.1571 | 0.7991 | 17.8163 | 0.0000 |
| 梁河 | 0.1399 | 0.9324 | 38.7200 | 0.0000 |
| 盐塘 | 0.1432 | 0.7889 | 27.8170 | 0.0000 |

## 分析结论

### 1. 数据质量
- **数据完整率达到100%**：经过修正后，所有四个站点的数据都无缺失
- 时间跨度从426天（梁河、盐塘）到462天（城口）不等
- 数据时间分辨率为1小时，满足精细化分析要求

### 2. 整体表现
- GFS模型在这四个站点的降水预报精度为**中等水平**
- 平均相关系数0.39表明模型能够捕捉到降水的基本趋势
- 平均NSE为负值(-0.11)说明模型系统性误差较大

### 3. 站点差异
- **梁河站**表现最佳：NSE为正值(0.13)，相关系数最高(0.41)
- **城口站**表现最差：NSE最低(-0.31)，但RMSE最小
- **郭家站**和**盐塘站**表现中等

### 4. 系统性偏差
- GFS模型总体上**高估**了降水量（平均预报值普遍高于观测值）
- 模型在极端降水事件的预报上存在局限性
- 小降水事件的预报精度相对较好

### 5. 建议
1. **模型校正**：建议对GFS降水预报进行偏差校正
2. **多模式集成**：结合其他数值模式提高预报精度
3. **本地化处理**：针对不同站点特点进行本地化调整
4. **阈值优化**：对不同量级降水采用不同的处理策略

## 图表说明

本分析生成了8个图表文件：
- 4个时间序列对比图：展示GFS预报与实际观测的时间变化
- 4个散点图：展示两者之间的相关关系和1:1对比线

所有图表保存在：`/home/<USER>/Flood_flow_prediction/Final_data/NOAAGFS/analysis_results/`

---
*分析时间：2025年8月16日（基于修正后数据重新分析）*
*数据来源：GFS模型预报数据与实地观测数据*
*数据修正时间：2025年8月16日 01:00*
