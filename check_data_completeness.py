import csv
from datetime import datetime, timedelta

def check_csv_file(file_path):
    print(f"Checking {file_path}...")
    
    # 读取CSV文件
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data.append(row)
    
    # 检查时间点是否连续
    expected_time = datetime.strptime('2024-01-01 08:00', '%Y-%m-%d %H:%M')
    end_time = datetime.strptime('2025-05-10 17:00', '%Y-%m-%d %H:%M')
    expected_count = int((end_time - expected_time).total_seconds() / 3600) + 1
    
    print(f"Expected number of records: {expected_count}")
    print(f"Actual number of records: {len(data)}")
    
    if len(data) != expected_count:
        print("WARNING: Record count mismatch!")
    
    # 检查是否有缺失的时间点
    time_points = set()
    for row in data:
        time_points.add(row['日期时间'])
    
    missing_times = []
    current_time = expected_time
    while current_time <= end_time:
        time_str = current_time.strftime('%Y-%m-%d %H:%M')
        if time_str not in time_points:
            missing_times.append(time_str)
        current_time += timedelta(hours=1)
    
    if missing_times:
        print(f"Missing time points: {len(missing_times)}")
        print("First 10 missing times:", missing_times[:10])
    else:
        print("No missing time points!")
    
    # 检查是否有非整点数据
    non_hourly = []
    for row in data:
        dt = datetime.strptime(row['日期时间'], '%Y-%m-%d %H:%M')
        if dt.minute != 0:
            non_hourly.append(row['日期时间'])
    
    if non_hourly:
        print(f"Non-hourly data points: {len(non_hourly)}")
        print("First 10 non-hourly times:", non_hourly[:10])
    else:
        print("No non-hourly data points!")
    
    # 检查是否有缺失的水位或流量
    missing_water = []
    missing_flow = []
    for row in data:
        if not row['水位(m)'] or row['水位(m)'] == '':
            missing_water.append(row['日期时间'])
        if not row['流量(m³/s)'] or row['流量(m³/s)'] == '':
            missing_flow.append(row['日期时间'])
    
    if missing_water:
        print(f"Records with missing water level: {len(missing_water)}")
        print("First 10 times with missing water level:", missing_water[:10])
    else:
        print("No missing water level data!")
    
    if missing_flow:
        print(f"Records with missing flow: {len(missing_flow)}")
        print("First 10 times with missing flow:", missing_flow[:10])
    else:
        print("No missing flow data!")
    
    print("Check completed!\n")

# 检查处理后的文件
check_csv_file('Real_flow/csv-use/chengkou_use.csv')
check_csv_file('Real_flow/csv-use/guojia_use.csv')
