export CUDA_VISIBLE_DEVICES=1




# python3 -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path guojia.csv \
#   --model_id guojia_168_12 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --loss RMSE \
#   --batch_size 4 \
#   --seq_len 168 \
#   --label_len 96 \
#   --pred_len 12 \
#   --e_layers 1 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 1 \
#   --des 'Exp' \
#   --d_model 512 \
#   --d_ff 1024 \
#   --patch_len 4 \
#   --itr 1




model_name1=Autoformer
model_name2=PatchTST
model_name3=iTransformer
model_name4=TimesNet
model_name5=DLinear
model_name6=TimeMixer
model_name7=Crossformer
model_name8=Mamba
model_name9=Transformer
model_name10=Koopa
model_name11=MICN
model_name12=Pyraformer
learning_rate=0.0001


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name1 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name2 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name3 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name4 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name5 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name6 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 2 \
#     --down_sampling_layers 3 \
#     --down_sampling_method avg \
#     --down_sampling_window 2
# done


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name7 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name8 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path guojia.csv \
#     --model_id guojia_168_$pred_len \
#     --data custom \
#     --model $model_name9 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path guojia.csv \
    --model_id guojia_168_$pred_len \
    --data custom \
    --model $model_name10 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 512 \
    --d_ff 1024 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done


for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path guojia.csv \
    --model_id guojia_168_$pred_len \
    --data custom \
    --model $model_name11 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 512 \
    --d_ff 1024 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done


for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path guojia.csv \
    --model_id guojia_168_$pred_len \
    --data custom \
    --model $model_name12 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 512 \
    --d_ff 1024 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done