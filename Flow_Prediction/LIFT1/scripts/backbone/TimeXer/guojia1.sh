# itr=1
# seq_len=168
# data=guojia
# model_name=TimeXer

# for pred_len in 12
# do
# for learning_rate in 0.0001
# do
#   python -u run_longExp.py \
#     --dataset $data --model $model_name --seq_len $seq_len --pred_len $pred_len --features MS --itr $itr \
#     --learning_rate $learning_rate > logs/backbone/$model_name'_'$data'_'$pred_len'_lr'$learning_rate.log 2>&1
# done
# done



model_name=TimeXer
data=guojia
learning_rate=0.0001


# for pred_len in 12
for pred_len in 6 12 24
do
  feature=S
  python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 512 \
    --d_ff 1024 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate
    # > logs/backbone/$model_name'_'$data'_'$pred_len'_lr'$learning_rate.log 2>&1
done