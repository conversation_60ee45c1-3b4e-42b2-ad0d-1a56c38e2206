export CUDA_VISIBLE_DEVICES=1
model_name=TimeXer
data=lianghe
learning_rate=0.0001




# for d_model in 16 32 64 128 256 512 1024
# do
# for pred_len in 12
# do
#   feature=MS
#   python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model $d_model \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate
# done
# done



# for d_ff in 128 256 512 1024 2048
# do
# for pred_len in 12
# do
#   feature=MS
#   python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 256 \
#     --d_ff $d_ff \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate
# done
# done





for patch_len in 4 8 16
do
for pred_len in 12
do
  feature=MS
  python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 1024 \
    --d_ff 1024 \
    --patch_len $patch_len \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate
done
done