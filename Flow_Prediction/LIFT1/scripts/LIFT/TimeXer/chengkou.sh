model_name=TimeXer
data=chengkou
leader_num=1
# state_num=4   #变量状态数参数 可调
tau=1.0
learning_rate=0.0001
feature=MS
# sh scripts/backbone/TimeXer/chengkou.sh


for pred_len in 6 12 24
do
  feature=MS
  python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 128 \
    --d_ff 256 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate
done



# 修改测试集范围为0-num_test
# for pred_len in 12 24
# for state_num in 1 2 4 8 12 16
# do
# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 16 \
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --checkpoints "" \
#     --lift \
#     --pretrain --freeze \
#     --leader_num $leader_num \
#     --state_num $state_num \
#     --temperature $tau \
#     --learning_rate $learning_rate \
#     --en_seq_len 168 \
#     --ex_seq_len $((168+$pred_len))
# done
# done



# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 16 \
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --checkpoints "" \
#     --lift \
#     --pretrain --freeze \
#     --leader_num $leader_num \
#     --state_num $state_num \
#     --temperature $tau \
#     --learning_rate $learning_rate \
#     --en_seq_len 168 \
#     --ex_seq_len $((168+$pred_len))
# done


# for state_num in 1 2 4 8 12 16
# do
# python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len 6 \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 128 \
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --checkpoints "" \
#     --lift \
#     --pretrain --freeze \
#     --leader_num $leader_num \
#     --state_num $state_num \
#     --temperature $tau \
#     --learning_rate $learning_rate \
#     --en_seq_len 168 \
#     --ex_seq_len 174
# done


# for state_num in 1 2 4 8 12 16
# do
# python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len 12 \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 128 \
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --checkpoints "" \
#     --lift \
#     --pretrain --freeze \
#     --leader_num $leader_num \
#     --state_num $state_num \
#     --temperature $tau \
#     --learning_rate $learning_rate \
#     --en_seq_len 168 \
#     --ex_seq_len 180
# done


# for state_num in 1 2 4 8 12 16
# do
# python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len 24 \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 128 \
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --checkpoints "" \
#     --lift \
#     --pretrain --freeze \
#     --leader_num $leader_num \
#     --state_num $state_num \
#     --temperature $tau \
#     --learning_rate $learning_rate \
#     --en_seq_len 168 \
#     --ex_seq_len 192
# done




python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len 6 \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 128 \
    --d_ff 256 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --checkpoints "" \
    --lift \
    --pretrain --freeze \
    --leader_num $leader_num \
    --state_num 16 \
    --temperature $tau \
    --learning_rate $learning_rate


python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len 12 \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 128 \
    --d_ff 256 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --checkpoints "" \
    --lift \
    --pretrain --freeze \
    --leader_num $leader_num \
    --state_num 2 \
    --temperature $tau \
    --learning_rate $learning_rate



python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len 24 \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 128 \
    --d_ff 256 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --checkpoints "" \
    --lift \
    --pretrain --freeze \
    --leader_num $leader_num \
    --state_num 2 \
    --temperature $tau \
    --learning_rate $learning_rate