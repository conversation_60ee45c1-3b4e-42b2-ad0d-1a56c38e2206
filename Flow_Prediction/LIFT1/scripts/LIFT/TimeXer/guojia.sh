model_name=TimeXer
data=guojia
leader_num=1
# state_num=12
tau=1.0
learning_rate=0.0001

# sh scripts/backbone/TimeXer/guojia.sh

# for state_num in 1 2 4 8 12 16
# do
# for pred_len in 6 12 24
# # for pred_len in 24  
# do
#   feature=MS
#   python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --checkpoints "" \
#     --lift \
#     --leader_num $leader_num \
#     --state_num $state_num \
#     --temperature $tau \
#     --pretrain --freeze \
#     --learning_rate $learning_rate \
#     --en_seq_len 168 \
#     --ex_seq_len $((168+$pred_len)) 
#     # > logs/Lead/${model_name}_${data}_${feature}_${pred_len}_K${leader_num}_tau${tau}_state${state_num}_lr${learning_rate}.log 2>&1
# done
# done

feature=MS
python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len 6 \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 512 \
    --d_ff 1024 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --checkpoints "" \
    --lift \
    --leader_num $leader_num \
    --state_num 16 \
    --temperature $tau \
    --pretrain --freeze \
    --learning_rate $learning_rate \
    --en_seq_len 168 \
    --ex_seq_len 174


python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len 12 \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 512 \
    --d_ff 1024 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --checkpoints "" \
    --lift \
    --leader_num $leader_num \
    --state_num 2 \
    --temperature $tau \
    --pretrain --freeze \
    --learning_rate $learning_rate \
    --en_seq_len 168 \
    --ex_seq_len 180


python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len 24 \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 512 \
    --d_ff 1024 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --checkpoints "" \
    --lift \
    --leader_num $leader_num \
    --state_num 2 \
    --temperature $tau \
    --pretrain --freeze \
    --learning_rate $learning_rate \
    --en_seq_len 168 \
    --ex_seq_len 192




# for pred_len in 6 12 24
# # for pred_len in 24  
# do
#   feature=MS
#   python -u run_longExp.py \
#     --dataset $data \
#     --model $model_name \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 512 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --checkpoints "" \
#     --lift \
#     --leader_num $leader_num \
#     --state_num $state_num \
#     --temperature $tau \
#     --pretrain --freeze \
#     --learning_rate $learning_rate \
#     --en_seq_len 168 \
#     --ex_seq_len $((168+$pred_len)) 
# done