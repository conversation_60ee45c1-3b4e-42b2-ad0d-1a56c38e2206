export CUDA_VISIBLE_DEVICES=1

# model_name=PatchTST

# python -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path lianghe.csv \
#   --model_id lianghe_168_24 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --seq_len 168 \
#   --label_len 48 \
#   --pred_len 24 \
#   --e_layers 2 \
#   --d_layers 1 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 1 \
#   --d_model 512 \
#   --d_ff 512 \
#   --top_k 5 \
#   --des 'Exp' \
#   --batch_size 16 \
#   --itr 1



model_name1=Autoformer
model_name2=PatchTST
model_name3=iTransformer
model_name4=TimesNet
model_name5=DLinear
model_name6=TimeMixer
model_name7=Crossformer
model_name8=Mamba
model_name9=Transformer
model_name10=Koopa
model_name11=MICN
model_name12=Pyraformer
learning_rate=0.0001

# 修改测试集范围为0-num_test
# for pred_len in 12 24
for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path lianghe.csv \
    --model_id lianghe_168_$pred_len \
    --data custom \
    --model $model_name1 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 1024 \
    --d_ff 1024 \
    --patch_len 8 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done


for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path lianghe.csv \
    --model_id lianghe_168_$pred_len \
    --data custom \
    --model $model_name2 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 1024 \
    --d_ff 1024 \
    --patch_len 8 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done

for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path lianghe.csv \
    --model_id lianghe_168_$pred_len \
    --data custom \
    --model $model_name3 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 1024 \
    --d_ff 1024 \
    --patch_len 8 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done

# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path lianghe.csv \
#     --model_id lianghe_168_$pred_len \
#     --data custom \
#     --model $model_name4 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 256 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path lianghe.csv \
    --model_id lianghe_168_$pred_len \
    --data custom \
    --model $model_name5 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 1024 \
    --d_ff 1024 \
    --patch_len 8 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done

for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path lianghe.csv \
    --model_id lianghe_168_$pred_len \
    --data custom \
    --model $model_name6 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 1024 \
    --d_ff 1024 \
    --patch_len 8 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 2 \
    --down_sampling_layers 3 \
    --down_sampling_method avg \
    --down_sampling_window 2
done


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path lianghe.csv \
#     --model_id lianghe_168_$pred_len \
#     --data custom \
#     --model $model_name7 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 256 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path lianghe.csv \
#     --model_id lianghe_168_$pred_len \
#     --data custom \
#     --model $model_name8 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 256 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done



# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path lianghe.csv \
#     --model_id lianghe_168_$pred_len \
#     --data custom \
#     --model $model_name9 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 1024 \
#     --d_ff 1024 \
#     --patch_len 8 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

for pred_len in 6 12 24
do
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path lianghe.csv \
    --model_id lianghe_168_$pred_len \
    --data custom \
    --model $model_name10 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 1024 \
    --d_ff 1024 \
    --patch_len 8 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1
done


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path lianghe.csv \
#     --model_id lianghe_168_$pred_len \
#     --data custom \
#     --model $model_name11 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 256 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done


# for pred_len in 6 12 24
# do
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path lianghe.csv \
#     --model_id lianghe_168_$pred_len \
#     --data custom \
#     --model $model_name12 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 256 \
#     --d_ff 1024 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done