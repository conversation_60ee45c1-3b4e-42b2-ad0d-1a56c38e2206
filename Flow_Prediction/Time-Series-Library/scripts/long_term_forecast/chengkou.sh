model_name1=Autoformer
model_name2=PatchTST
model_name3=iTransformer
model_name4=TimesNet
model_name5=DLinear
model_name6=TimeMixer
data=chengkou
learning_rate=0.0001

# 修改测试集范围为0-num_test
# for pred_len in 12 24
# for pred_len in 6 12 24
# do 
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path chengkou.csv \
#     --model_id chengkou_196_$pred_len \
#     --data custom \
#     --model $model_name1 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 16\
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do 
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path chengkou.csv \
#     --model_id chengkou_196_$pred_len \
#     --data custom \
#     --model $model_name2 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 16\
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do 
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path chengkou.csv \
#     --model_id chengkou_196_$pred_len \
#     --data custom \
#     --model $model_name3 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 16\
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do 
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path chengkou.csv \
#     --model_id chengkou_196_$pred_len \
#     --data custom \
#     --model $model_name4 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 16\
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

# for pred_len in 6 12 24
# do 
#   feature=MS
#   python -u run.py \
#     --task_name long_term_forecast \
#     --is_training 1 \
#     --root_path ./dataset/flow/ \
#     --data_path chengkou.csv \
#     --model_id chengkou_196_$pred_len \
#     --data custom \
#     --model $model_name5 \
#     --seq_len 168 \
#     --label_len 96 \
#     --pred_len $pred_len \
#     --batch_size 4 \
#     --e_layers 1 \
#     --d_model 16\
#     --d_ff 256 \
#     --patch_len 4 \
#     --loss RMSE \
#     --features $feature \
#     --itr 1 \
#     --learning_rate $learning_rate \
#     --des 'Exp' \
#     --factor 3 \
#     --enc_in 2 \
#     --dec_in 2 \
#     --c_out 1
# done

for pred_len in 6 12 24
do 
  feature=MS
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/flow/ \
    --data_path chengkou.csv \
    --model_id chengkou_196_$pred_len \
    --data custom \
    --model $model_name6 \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 16\
    --d_ff 256 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate \
    --des 'Exp' \
    --factor 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 2 \
    --down_sampling_layers 3 \
    --down_sampling_method avg \
    --down_sampling_window 2

done