import csv
import re
from pathlib import Path

TXT_PATH = Path('Time-Series-Library/guojia.txt')
OUT_CSV_PATH = Path('Time-Series-Library/guojia_summary.csv')

# Desired ordering
MODEL_ORDER = [
    'Autoformer', 'PatchTST', 'iTransformer', 'TimesNet', 'DLinear', 'TimeMixer'
]
WINDOW_ORDER = ['6', '12', '24']

header = [
    'Window', 'Model', 'guojia_NSE', 'guojia_CORR', 'guojia_RMSE', 'guojia_MAE'
]

# Regex to parse header lines
# Example: long_term_forecast_guojia_168_6_Autoformer_custom_...
HEADER_RE = re.compile(r'^long_term_forecast_guojia_\d+_(\d+)_(Autoformer|PatchTST|iTransformer|TimesNet|DLinear|TimeMixer)_')

# Regex to parse metrics line
# Example: RMSE:0.1221, NSE:0.4706, CORR:0.7325, MAE:0.0650
METRICS_RE = re.compile(r'RMSE:([0-9eE+\-\.]+),\s*NSE:([0-9eE+\-\.]+),\s*CORR:([0-9eE+\-\.]+),\s*MAE:([0-9eE+\-\.]+)')


def parse_file(txt_path: Path):
    """Parse the text file and return a dict keyed by (window, model) -> metrics."""
    text = txt_path.read_text(encoding='utf-8', errors='ignore').splitlines()

    # Data structure: data[(window, model)] = {'NSE': float, 'CORR': float, 'RMSE': float, 'MAE': float}
    data = {}

    i = 0
    while i < len(text):
        line = text[i].strip()
        i += 1
        if not line:
            continue

        m = HEADER_RE.match(line)
        if not m:
            continue

        window, model = m.group(1), m.group(2)

        # Next non-empty line should be the metrics
        metrics_line = ''
        while i < len(text):
            metrics_line = text[i].strip()
            i += 1
            if metrics_line:
                break
        mm = METRICS_RE.search(metrics_line)
        if not mm:
            # If metrics line not matched, skip safely
            continue

        rmse, nse, corr, mae = map(float, mm.groups())

        key = (window, model)
        data[key] = {
            'NSE': nse,
            'CORR': corr,
            'RMSE': rmse,
            'MAE': mae,
        }

    return data


def write_csv(data, out_path: Path):
    out_path.parent.mkdir(parents=True, exist_ok=True)
    with out_path.open('w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(header)

        # Sort by desired window and model order
        def window_key(w):
            try:
                return WINDOW_ORDER.index(w)
            except ValueError:
                return len(WINDOW_ORDER)

        def model_key(m):
            try:
                return MODEL_ORDER.index(m)
            except ValueError:
                return len(MODEL_ORDER)

        for window in sorted({k[0] for k in data.keys()}, key=window_key):
            # For each window, iterate models in preferred order
            models_in_window = sorted({k[1] for k in data.keys() if k[0] == window}, key=model_key)
            for model in models_in_window:
                key = (window, model)
                if key in data:
                    metrics = data[key]
                    row = [
                        window + 'h', 
                        model,
                        f"{metrics['NSE']:.6f}",
                        f"{metrics['CORR']:.6f}",
                        f"{metrics['RMSE']:.6f}",
                        f"{metrics['MAE']:.6f}",
                    ]
                    writer.writerow(row)


def main():
    data = parse_file(TXT_PATH)
    write_csv(data, OUT_CSV_PATH)
    print(f"Wrote CSV to {OUT_CSV_PATH}")


if __name__ == '__main__':
    main()
