#!/usr/bin/env python3
"""
Visualization script for comparing true vs predicted flow values
from TimeXer model results.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import os

def load_and_visualize_predictions():
    """
    Load true and predicted values from numpy files and create comparison visualization.
    """
    # Define the results directory path
    results_dir = "/home/<USER>/Flood_flow_prediction/Flow_Prediction/LIFT/result822/dongxi_168_6_TimeXer_custom_ftMS_sl168_ll96_pl6_lr0.0001_dm1024_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift"
    
    # Load the numpy arrays
    true_path = os.path.join(results_dir, "true.npy")
    pred_path = os.path.join(results_dir, "pred.npy")
    
    print(f"Loading true values from: {true_path}")
    print(f"Loading predicted values from: {pred_path}")
    
    try:
        true_values = np.load(true_path)
        pred_values = np.load(pred_path)
        
        print(f"True values shape: {true_values.shape}")
        print(f"Predicted values shape: {pred_values.shape}")
        print(f"True values range: [{np.min(true_values):.3f}, {np.max(true_values):.3f}]")
        print(f"Predicted values range: [{np.min(pred_values):.3f}, {np.max(pred_values):.3f}]")
        
    except Exception as e:
        print(f"Error loading numpy files: {e}")
        return
    
    # Handle different array dimensions
    # For TimeXer model output: shape is (n_samples, prediction_length)
    # We want to flatten all predictions into a continuous time series
    if len(true_values.shape) > 1:
        print(f"Original data shape - True: {true_values.shape}, Pred: {pred_values.shape}")

        if len(true_values.shape) == 2 and true_values.shape == pred_values.shape:
            # Shape is (n_samples, prediction_steps)
            # Flatten to create continuous time series: (n_samples * prediction_steps,)
            print(f"Flattening data: {true_values.shape[0]} samples × {true_values.shape[1]} time steps = {true_values.size} total points")
            true_values = true_values.flatten()
            pred_values = pred_values.flatten()
            print(f"After flattening - True: {true_values.shape}, Pred: {pred_values.shape}")
        elif len(true_values.shape) == 3:
            # Shape might be (samples, time_steps, features)
            print("Handling 3D data by flattening first two dimensions")
            true_values = true_values.reshape(-1, true_values.shape[-1])
            pred_values = pred_values.reshape(-1, pred_values.shape[-1])
            # If multiple features, take the first one
            if true_values.shape[-1] > 1:
                print(f"Multiple features detected ({true_values.shape[-1]}). Using first feature for visualization.")
                true_values = true_values[:, 0]
                pred_values = pred_values[:, 0]
            else:
                true_values = true_values.flatten()
                pred_values = pred_values.flatten()
        else:
            # Fallback: flatten everything
            true_values = true_values.flatten()
            pred_values = pred_values.flatten()
    
    # Ensure both arrays have the same length
    min_length = min(len(true_values), len(pred_values))
    true_values = true_values[:min_length]
    pred_values = pred_values[:min_length]
    
    print(f"Final data length: {min_length} time steps")

    # Create time axis
    # Data represents flattened predictions: n_samples × prediction_length points
    time_steps = np.arange(min_length)

    # Calculate original data structure for better labeling
    n_samples = min_length // 12 if min_length % 12 == 0 else min_length // 12 + 1
    prediction_length = 12
    print(f"Data structure: ~{n_samples} samples × {prediction_length} prediction steps = {min_length} total points")
    
    # Create the visualization
    plt.style.use('default')
    fig, ax = plt.subplots(figsize=(15, 8))
    
    # Plot true and predicted values
    ax.plot(time_steps, true_values,
            color='blue', linewidth=2, label='Observed Flow', alpha=0.8, linestyle='-')
    ax.plot(time_steps, pred_values,
            color='red', linewidth=2, label='Predicted Flow', alpha=0.8, linestyle='-')
    
    # Customize the plot
    ax.set_xlabel('Time Steps (Flattened Prediction Sequences)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Flow Rate (m³/s)', fontsize=12, fontweight='bold')
    ax.set_title(f'Flow Prediction Comparison: TimeXer Model Results\n'
                f'All Prediction Sequences ({min_length} total time points)',
                fontsize=14, fontweight='bold', pad=20)
    
    # Add grid for better readability
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # Add legend
    ax.legend(loc='upper right', fontsize=11, framealpha=0.9)
    
    # Calculate and display performance metrics
    mse = np.mean((true_values - pred_values) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(true_values - pred_values))
    
    # Calculate correlation coefficient
    correlation = np.corrcoef(true_values, pred_values)[0, 1]
    
    # Calculate Nash-Sutcliffe Efficiency (NSE)
    nse = 1 - (np.sum((true_values - pred_values) ** 2) / 
               np.sum((true_values - np.mean(true_values)) ** 2))
    
    # Add performance metrics as text box
    metrics_text = f'Performance Metrics:\n' \
                  f'RMSE: {rmse:.3f}\n' \
                  f'MAE: {mae:.3f}\n' \
                  f'Correlation: {correlation:.3f}\n' \
                  f'NSE: {nse:.3f}'
    
    ax.text(0.02, 0.98, metrics_text, transform=ax.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', 
            facecolor='wheat', alpha=0.8), fontsize=10)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the plot
    output_path = os.path.join(results_dir, "flow_prediction_comparison.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"Plot saved to: {output_path}")
    
    # Also save as PDF for high quality
    pdf_path = os.path.join(results_dir, "flow_prediction_comparison.pdf")
    plt.savefig(pdf_path, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"High-quality PDF saved to: {pdf_path}")
    
    # Show the plot
    plt.show()
    
    # Create a scatter plot for additional analysis
    create_scatter_plot(true_values, pred_values, results_dir)

def create_scatter_plot(true_values, pred_values, results_dir):
    """
    Create a scatter plot to show the correlation between true and predicted values.
    """
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create scatter plot
    ax.scatter(true_values, pred_values, alpha=0.6, color='#4169E1', s=20)
    
    # Add perfect prediction line (y=x)
    min_val = min(np.min(true_values), np.min(pred_values))
    max_val = max(np.max(true_values), np.max(pred_values))
    ax.plot([min_val, max_val], [min_val, max_val], 
            'r--', linewidth=2, label='Perfect Prediction (y=x)')
    
    # Customize the plot
    ax.set_xlabel('Observed Flow (m³/s)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Predicted Flow (m³/s)', fontsize=12, fontweight='bold')
    ax.set_title('Predicted vs Observed Flow: Scatter Plot Analysis', 
                fontsize=14, fontweight='bold', pad=20)
    
    # Add grid
    ax.grid(True, alpha=0.3)
    
    # Add legend
    ax.legend(fontsize=11)
    
    # Make axes equal for better comparison
    ax.set_aspect('equal', adjustable='box')
    
    # Calculate R²
    correlation = np.corrcoef(true_values, pred_values)[0, 1]
    r_squared = correlation ** 2
    
    # Add R² value
    ax.text(0.05, 0.95, f'R² = {r_squared:.3f}', transform=ax.transAxes,
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
            fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    
    # Save scatter plot
    scatter_path = os.path.join(results_dir, "flow_prediction_scatter.png")
    plt.savefig(scatter_path, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"Scatter plot saved to: {scatter_path}")
    
    plt.show()

if __name__ == "__main__":
    print("Starting flow prediction visualization...")
    load_and_visualize_predictions()
    print("Visualization complete!")
