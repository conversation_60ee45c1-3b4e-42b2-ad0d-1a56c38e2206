if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/Lead" ]; then
    mkdir ./logs/Lead
fi

itr=1
seq_len=336
tag=_max
tau=1.0
data=wind
model_name=PatchTST
train_epochs=100
pct_start=0.3

learning_rate=0.0005
for pred_len in 24 48 192
do
  leader_num=2
  state_num=4
  python -u run_longExp.py \
    --dataset $data --model $model_name --lift --seq_len $seq_len --pred_len $pred_len --itr $itr --checkpoints "" \
    --leader_num $leader_num --state_num $state_num --temperature $tau \
    --patience 10 --pct_start $pct_start --train_epochs $train_epochs \
    --learning_rate $learning_rate > logs/Lead/$model_name'_LIFT_'$data'_'$pred_len'_K'$leader_num'_tau'$tau'_state'$state_num'_lr'$learning_rate'_epoch'$train_epochs'_pct0.3'.log 2>&1
done
for pred_len in 96
do
  leader_num=4
  state_num=4
  python -u run_longExp.py \
    --dataset $data --model $model_name --lift --seq_len $seq_len --pred_len $pred_len --itr $itr --checkpoints "" \
    --leader_num $leader_num --state_num $state_num --temperature $tau \
    --patience 10 --pct_start $pct_start --train_epochs $train_epochs \
    --learning_rate $learning_rate > logs/Lead/$model_name'_LIFT_'$data'_'$pred_len'_K'$leader_num'_tau'$tau'_state'$state_num'_lr'$learning_rate'_epoch'$train_epochs'_pct0.3'.log 2>&1
done
for pred_len in 336 720
do
  leader_num=4
  state_num=2
  python -u run_longExp.py \
    --dataset $data --model $model_name --lift --seq_len $seq_len --pred_len $pred_len --itr $itr --checkpoints "" \
    --leader_num $leader_num --state_num $state_num --temperature $tau \
    --patience 10 --pct_start $pct_start --train_epochs $train_epochs \
    --learning_rate $learning_rate > logs/Lead/$model_name'_LIFT_'$data'_'$pred_len'_K'$leader_num'_tau'$tau'_state'$state_num'_lr'$learning_rate'_epoch'$train_epochs'_pct0.3'.log 2>&1
done