model_name=TimeXer
data=chengkou
learning_rate=0.0001

# 修改测试集范围为0-num_test
# for pred_len in 12 24
for pred_len in 6 12 24
do
  feature=S
  python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len 168 \
    --label_len 96 \
    --pred_len $pred_len \
    --batch_size 4 \
    --e_layers 1 \
    --d_model 16\
    --d_ff 256 \
    --patch_len 4 \
    --loss RMSE \
    --features $feature \
    --itr 1 \
    --learning_rate $learning_rate
done