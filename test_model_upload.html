<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型上传测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .form-text {
            margin-top: 5px;
            font-size: 0.85em;
            color: #6c757d;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 模型上传测试</h1>
        <p>测试模型文件和模型描述都为可选的上传功能</p>
        
        <form id="modelUploadForm">
            <div class="form-group">
                <label for="watershedSelect">流域选择:</label>
                <select id="watershedSelect" class="form-control" required>
                    <option value="两河">两河流域</option>
                    <option value="沿塘">沿塘流域</option>
                    <option value="银河">银河流域</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="modelName">模型名称:</label>
                <input type="text" id="modelName" class="form-control" placeholder="请输入模型名称" required>
            </div>
            
            <div class="form-group">
                <label for="modelFile">模型文件 (可选):</label>
                <input type="file" id="modelFile" class="form-control" accept=".pkl,.h5,.pt,.pth,.joblib,.model">
                <small class="form-text">支持的文件格式: .pkl, .h5, .pt, .pth, .joblib, .model（可不上传文件，仅创建模型记录）</small>
            </div>
            
            <div class="form-group">
                <label for="modelDescription">模型描述 (可选):</label>
                <textarea id="modelDescription" class="form-control" rows="3" placeholder="请输入模型描述（可选）"></textarea>
            </div>
            
            <div style="display: flex; gap: 10px;">
                <button type="button" class="btn btn-primary" onclick="uploadModel()">上传模型</button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">清空表单</button>
            </div>
        </form>
        
        <div id="result" class="result"></div>
        
        <div id="modelList" style="margin-top: 30px;">
            <h3>已上传的模型:</h3>
            <div id="models"></div>
        </div>
    </div>

    <script>
        let modelCounter = 0;
        
        function uploadModel() {
            const watershed = document.getElementById('watershedSelect').value;
            const modelName = document.getElementById('modelName').value;
            const modelFile = document.getElementById('modelFile').files[0];
            const modelDescription = document.getElementById('modelDescription').value;
            
            // 验证必填字段
            if (!modelName.trim()) {
                showResult('请填写模型名称', 'error');
                return;
            }
            
            // 模拟上传过程
            showResult('正在上传模型...', 'success');
            
            setTimeout(() => {
                modelCounter++;
                const successMessage = modelFile ? '模型上传成功！' : '模型记录创建成功！';
                showResult(successMessage, 'success');
                
                // 添加到模型列表
                addModelToList({
                    id: modelCounter,
                    watershed: watershed,
                    name: modelName,
                    description: modelDescription || '暂无描述',
                    fileName: modelFile ? modelFile.name : '无文件',
                    uploadTime: new Date().toLocaleString()
                });
                
                // 清空表单
                clearForm();
            }, 1000);
        }
        
        function clearForm() {
            document.getElementById('modelUploadForm').reset();
            hideResult();
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
        
        function hideResult() {
            const result = document.getElementById('result');
            result.style.display = 'none';
        }
        
        function addModelToList(model) {
            const modelsContainer = document.getElementById('models');
            const modelDiv = document.createElement('div');
            modelDiv.style.cssText = `
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
                border-left: 4px solid #007bff;
            `;

            const hasFile = model.fileName && model.fileName !== '无文件';
            const downloadButton = hasFile
                ? `<button style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-right: 5px;">📥 下载</button>`
                : `<button style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-right: 5px;">📥 下载</button>`;

            modelDiv.innerHTML = `
                <h4 style="margin: 0 0 10px 0; color: #2c3e50;">${model.name}</h4>
                <p style="margin: 5px 0; color: #666;"><strong>流域:</strong> ${model.watershed}</p>
                <p style="margin: 5px 0; color: #666;">${model.description}</p>
                <div style="margin: 10px 0; padding: 5px 10px; background: #e8f5e8; color: #2e7d32; border-radius: 20px; font-size: 0.9em; display: inline-block;">状态: 已启用</div>
                <div style="margin-top: 15px;">
                    <button style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-right: 5px;">查看详情</button>
                    ${downloadButton}
                    <button style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px;">🗑️ 删除</button>
                </div>
            `;

            modelsContainer.appendChild(modelDiv);
        }
    </script>
</body>
</html>
