#!/usr/bin/env python3
"""
计算三个流域（lianghe、yantang、yin<PERSON>）的实际面积
使用shapefile边界文件进行精确计算
"""

import geopandas as gpd
import pandas as pd
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def calculate_watershed_area(shapefile_path, watershed_name):
    """
    计算单个流域的面积
    
    Parameters:
    -----------
    shapefile_path : str
        shapefile文件路径
    watershed_name : str
        流域名称
    
    Returns:
    --------
    dict : 包含面积信息的字典
    """
    try:
        # 读取shapefile
        print(f"正在读取 {watershed_name} 流域边界文件...")
        gdf = gpd.read_file(shapefile_path)
        
        # 检查坐标系
        print(f"原始坐标系: {gdf.crs}")
        
        # 如果没有坐标系，设置为WGS84
        if gdf.crs is None:
            print("未检测到坐标系，设置为 EPSG:4326 (WGS84)")
            gdf = gdf.set_crs("EPSG:4326")
        
        # 转换到适合面积计算的投影坐标系（中国区域推荐使用Albers等积投影）
        # 使用中国Albers等积圆锥投影 (EPSG:3395 或自定义)
        # 这里使用适合中国的等积投影
        target_crs = "+proj=aea +lat_1=25 +lat_2=47 +lat_0=36 +lon_0=105 +x_0=0 +y_0=0 +datum=WGS84 +units=m +no_defs"
        
        print(f"转换到等积投影坐标系进行面积计算...")
        gdf_projected = gdf.to_crs(target_crs)
        
        # 计算面积（平方米）
        areas_m2 = gdf_projected.geometry.area
        total_area_m2 = areas_m2.sum()
        
        # 转换为平方公里
        total_area_km2 = total_area_m2 / 1_000_000
        
        # 获取边界框信息
        bounds = gdf.total_bounds  # [minx, miny, maxx, maxy]
        
        result = {
            'watershed_name': watershed_name,
            'area_m2': total_area_m2,
            'area_km2': total_area_km2,
            'polygon_count': len(gdf),
            'bounds': {
                'min_longitude': bounds[0],
                'min_latitude': bounds[1], 
                'max_longitude': bounds[2],
                'max_latitude': bounds[3]
            },
            'center': {
                'longitude': (bounds[0] + bounds[2]) / 2,
                'latitude': (bounds[1] + bounds[3]) / 2
            }
        }
        
        print(f"✅ {watershed_name} 流域面积计算完成")
        return result
        
    except Exception as e:
        print(f"❌ 计算 {watershed_name} 流域面积时出错: {str(e)}")
        return None

def calculate_all_watershed_areas():
    """
    计算所有三个流域的面积
    """
    # 定义流域和对应的shapefile路径
    watersheds = {
        # 'lianghe': 'boundary/lianghe.shp',
        # 'yantang': 'boundary/yantang.shp', 
        # 'yinhe': 'boundary/yinhe.shp',
        # 'chengkou': 'boundary/chengkou.shp',
        # 'guojia': 'boundary/guojia.shp'
        'dongxi': 'boundary/dongxi.shp',
        'mituo': 'boundary/mituo.shp'
    }
    
    results = []
    
    print("=" * 80)
    print("开始计算三个流域的实际面积")
    print("=" * 80)
    
    for watershed_name, shapefile_path in watersheds.items():
        print(f"\n📍 正在处理 {watershed_name} 流域...")
        print("-" * 60)
        
        # 检查文件是否存在
        if not Path(shapefile_path).exists():
            print(f"❌ 错误：文件 {shapefile_path} 不存在！")
            continue
            
        # 计算面积
        result = calculate_watershed_area(shapefile_path, watershed_name)
        
        if result:
            results.append(result)
            
            # 打印结果
            print(f"流域名称: {result['watershed_name']}")
            print(f"面积: {result['area_km2']:.2f} 平方公里")
            print(f"面积: {result['area_m2']:.0f} 平方米")
            print(f"多边形数量: {result['polygon_count']}")
            print(f"经度范围: {result['bounds']['min_longitude']:.4f} ~ {result['bounds']['max_longitude']:.4f}")
            print(f"纬度范围: {result['bounds']['min_latitude']:.4f} ~ {result['bounds']['max_latitude']:.4f}")
            print(f"中心点: ({result['center']['latitude']:.4f}°N, {result['center']['longitude']:.4f}°E)")
    
    # 汇总结果
    if results:
        print("\n" + "=" * 80)
        print("流域面积汇总")
        print("=" * 80)
        
        total_area = 0
        for result in results:
            print(f"{result['watershed_name']:>10s}: {result['area_km2']:>10.2f} 平方公里")
            total_area += result['area_km2']
        
        print("-" * 40)
        print(f"{'总计':>10s}: {total_area:>10.2f} 平方公里")
        
        # 保存结果到CSV
        df = pd.DataFrame(results)
        output_file = 'watershed_areas_summary.csv'
        df.to_csv(output_file, index=False)
        print(f"\n📄 详细结果已保存到: {output_file}")
        
        # 创建简化的汇总表
        summary_data = []
        for result in results:
            summary_data.append({
                '流域名称': result['watershed_name'],
                '面积(平方公里)': round(result['area_km2'], 2),
                '面积(平方米)': int(result['area_m2']),
                '中心经度': round(result['center']['longitude'], 4),
                '中心纬度': round(result['center']['latitude'], 4)
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_file = 'watershed_areas_simple.csv'
        summary_df.to_csv(summary_file, index=False)
        print(f"📄 简化汇总已保存到: {summary_file}")
        
    else:
        print("❌ 没有成功计算任何流域面积")
    
    return results

def compare_with_grid_calculation():
    """
    与栅格计算方法进行对比（如果有栅格数据的话）
    """
    print("\n" + "=" * 80)
    print("说明：流域面积计算方法")
    print("=" * 80)
    print("1. 使用shapefile边界进行精确计算")
    print("2. 采用Albers等积圆锥投影，适合中国区域的面积计算")
    print("3. 投影参数针对中国地区优化（中央经线105°E，标准纬线25°N和47°N）")
    print("4. 结果精度高，适合科学研究和工程应用")
    print("=" * 80)

if __name__ == "__main__":
    # 计算所有流域面积
    results = calculate_all_watershed_areas()
    
    # 显示计算方法说明
    compare_with_grid_calculation()
    
    print("\n🎉 流域面积计算完成！")
