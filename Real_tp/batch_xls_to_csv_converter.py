#!/usr/bin/env python3
"""
批量转换xls文件为csv文件
将Real_tp文件夹下的lianghe1、ya<PERSON><PERSON>、y<PERSON><PERSON>文件夹中origin_data下的所有xls文件转换为csv
"""

import pandas as pd
import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xls_to_csv_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class XlsToCsvConverter:
    """XLS到CSV转换器"""
    
    def __init__(self, base_dir="/home/<USER>/Flood_flow_prediction/Real_tp"):
        self.base_dir = Path(base_dir)
        self.stations = ["dongxi", "mituo"]
        self.conversion_stats = {
            'total_files': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def convert_single_file(self, xls_file_path, csv_file_path):
        """
        转换单个xls文件为csv
        
        Args:
            xls_file_path (Path): xls文件路径
            csv_file_path (Path): csv文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 检查csv文件是否已存在
            if csv_file_path.exists():
                logging.info(f"CSV文件已存在，跳过: {csv_file_path.name}")
                self.conversion_stats['skipped'] += 1
                return True
            
            # 读取xls文件
            logging.info(f"正在转换: {xls_file_path.name}")
            
            # 尝试读取xls文件，可能需要指定引擎
            try:
                df = pd.read_excel(xls_file_path, engine='xlrd')
            except Exception as e:
                # 如果xlrd失败，尝试openpyxl
                logging.warning(f"xlrd引擎失败，尝试openpyxl: {e}")
                df = pd.read_excel(xls_file_path, engine='openpyxl')
            
            # 保存为csv文件
            df.to_csv(csv_file_path, index=False, encoding='utf-8')
            
            logging.info(f"✅ 转换成功: {xls_file_path.name} -> {csv_file_path.name}")
            logging.info(f"   数据形状: {df.shape}")
            
            self.conversion_stats['successful'] += 1
            return True
            
        except Exception as e:
            logging.error(f"❌ 转换失败: {xls_file_path.name}")
            logging.error(f"   错误信息: {str(e)}")
            self.conversion_stats['failed'] += 1
            return False
    
    def convert_station_files(self, station_name):
        """
        转换单个站点的所有xls文件
        
        Args:
            station_name (str): 站点名称
        """
        logging.info(f"\n{'='*60}")
        logging.info(f"开始处理站点: {station_name}")
        logging.info(f"{'='*60}")
        
        # 构建路径
        origin_data_dir = self.base_dir / station_name / "origin_data"
        
        if not origin_data_dir.exists():
            logging.warning(f"目录不存在: {origin_data_dir}")
            return
        
        # 查找所有xls文件
        xls_files = list(origin_data_dir.glob("*.xls"))
        
        if not xls_files:
            logging.warning(f"在 {origin_data_dir} 中未找到xls文件")
            return
        
        logging.info(f"找到 {len(xls_files)} 个xls文件")
        
        # 转换每个文件
        for xls_file in sorted(xls_files):
            self.conversion_stats['total_files'] += 1
            
            # 生成csv文件名
            csv_file = origin_data_dir / (xls_file.stem + ".csv")
            
            # 转换文件
            self.convert_single_file(xls_file, csv_file)
    
    def convert_all_stations(self):
        """转换所有站点的xls文件"""
        logging.info("开始批量转换xls文件为csv文件")
        logging.info(f"基础目录: {self.base_dir.absolute()}")
        logging.info(f"目标站点: {', '.join(self.stations)}")
        
        # 检查基础目录是否存在
        if not self.base_dir.exists():
            logging.error(f"基础目录不存在: {self.base_dir}")
            return False
        
        # 转换每个站点
        for station in self.stations:
            self.convert_station_files(station)
        
        # 输出统计信息
        self.print_conversion_summary()
        
        return True
    
    def print_conversion_summary(self):
        """打印转换统计信息"""
        logging.info(f"\n{'='*60}")
        logging.info("转换完成统计")
        logging.info(f"{'='*60}")
        logging.info(f"总文件数: {self.conversion_stats['total_files']}")
        logging.info(f"成功转换: {self.conversion_stats['successful']}")
        logging.info(f"转换失败: {self.conversion_stats['failed']}")
        logging.info(f"跳过文件: {self.conversion_stats['skipped']}")
        
        if self.conversion_stats['total_files'] > 0:
            success_rate = (self.conversion_stats['successful'] / self.conversion_stats['total_files']) * 100
            logging.info(f"成功率: {success_rate:.1f}%")
        
        if self.conversion_stats['failed'] > 0:
            logging.warning(f"有 {self.conversion_stats['failed']} 个文件转换失败，请检查日志")

def main():
    """主函数"""
    print("批量XLS到CSV转换器")
    print("=" * 50)
    
    # 创建转换器实例
    converter = XlsToCsvConverter()
    
    # 执行转换
    success = converter.convert_all_stations()
    
    if success:
        print("\n转换任务完成！详细信息请查看日志文件: xls_to_csv_conversion.log")
    else:
        print("\n转换任务失败！请检查错误信息")

if __name__ == "__main__":
    main()
