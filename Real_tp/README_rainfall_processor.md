# 两河流域雨量数据处理工具

## 概述

这个工具将原来的四个独立Python脚本的功能封装成一个统一的处理器，用于处理两河流域的雨量数据。

## 原始脚本功能

1. **process_csv_files.py** - 处理原始CSV文件（删除前三行和第一列）
2. **merge_and_sort_csv.py** - 合并和排序各站点数据
3. **fill_missing_timestamps.py** - 补全缺失时间点
4. **aggregate_rainfall_data.py** - 汇总计算面上雨量

## 封装后的功能

### 主要类：`RainfallDataProcessor`

#### 初始化参数

```python
processor = RainfallDataProcessor(
    origin_dir="origin",                    # 原始数据目录
    merge_dir="merge",                      # 合并数据输出目录
    weight_file="weight.csv",               # 权重文件路径
    output_file="两河流域雨量汇总.csv"        # 最终输出文件
)
```

#### 主要方法


- `preprocess_all_csv_file()` - 步骤0(需手动调用)：处理origin_dir目录下的所有CSV文件：删除前三行以及第一列。调用RainfallDataProcessor类的全流程处理函数process_rainfall_data时不会自动调用此函数，如果原文件包含无用表头信息和序号列，需要手动调用该函数，然后使用全流程处理函数

- `process_origin_files()` - 步骤1：处理原始CSV文件
- `merge_and_sort_data()` - 步骤2：合并和排序数据
- `fill_missing_timestamps()` - 步骤3：补全缺失时间点
- `aggregate_rainfall_data()` - 步骤4：汇总计算面上雨量
- `process_rainfall_data()` - 执行完整的处理流程

## 使用方法

### 方法1：使用默认参数

```python
from rainfall_data_processor import RainfallDataProcessor

# 使用默认参数创建处理器
processor = RainfallDataProcessor()

# 执行完整处理流程
success = processor.process_rainfall_data()
```

### 方法2：自定义参数

```python
from rainfall_data_processor import RainfallDataProcessor

# 自定义参数
processor = RainfallDataProcessor(
    origin_dir="my_origin_data",
    merge_dir="my_merge_data", 
    weight_file="my_weights.csv",
    output_file="my_rainfall_summary.csv"
)

# 执行完整处理流程
success = processor.process_rainfall_data()
```

### 方法3：分步执行

```python
from rainfall_data_processor import RainfallDataProcessor

processor = RainfallDataProcessor()

# 分步执行，可以在每步之间检查结果
if processor.process_origin_files():
    print("步骤1完成")
    
    if processor.merge_and_sort_data():
        print("步骤2完成")
        
        if processor.fill_missing_timestamps():
            print("步骤3完成")
            
            if processor.aggregate_rainfall_data():
                print("步骤4完成，处理成功！")
```

### 方法4：直接运行脚本

```bash
cd Real_tp/lianghe
python rainfall_data_processor.py
```

## 目录结构要求

```
工作目录/
├── rainfall_data_processor.py    # 主处理脚本
├── weight.csv                    # 权重文件
├── origin/                       # 原始数据目录
│   ├── 站点名2024下半年.csv
│   ├── 站点名2025.csv
│   └── ...
└── merge/                        # 合并数据目录（自动创建）
    ├── 站点名_merged.csv
    └── ...
```

## 输入文件格式

### 原始CSV文件
- 文件名格式：`{站点名}2024下半年.csv` 和 `{站点名}2025.csv`
- 前三行为标题信息（会被删除）
- 第一列为序号（会被删除）
- 第二列为时间（格式：`月-日 小时`）
- 第三列为时段雨量

### 权重文件 (weight.csv)
```csv
站名,权重
两河(二),0.06
苏家场,0.06
永丰,0.06
...
```

## 输出文件

### 中间文件
- `merge/{站点名}_merged.csv` - 合并后的站点数据
  - 时间格式：`YYYY-MM-DD HH:MM:SS`
  - 包含完整的小时时间序列

### 最终输出
- `两河流域雨量汇总.csv` - 汇总的面上雨量数据
  - 包含所有站点的时间序列数据
  - 最后一列为加权计算的面上雨量

## 处理步骤详解

1. **步骤1：处理原始文件**
   - 删除每个CSV文件的前三行
   - 删除第一列（序号列）
   - 添加标准标题行：`['时间', '时段雨量']`

2. **步骤2：合并和排序**
   - 解析时间格式从`月-日 小时`转换为`YYYY-MM-DD HH:MM:SS`
   - 合并每个站点的2024下半年和2025年数据
   - 按时间升序排序

3. **步骤3：补全时间点**
   - 检查时间序列的完整性
   - 补全缺失的小时时间点
   - 缺失时间点的雨量值用0填充

4. **步骤4：汇总计算**
   - 按时间戳对齐所有站点数据
   - 根据权重文件计算加权平均面上雨量
   - 生成最终汇总文件

## 注意事项

1. 确保权重文件中的站点名称与数据文件中的站点名称一致
2. 原始数据文件必须包含2024下半年和2025年两个时间段
3. 处理过程中会自动创建merge目录
4. 如果某个步骤失败，整个处理流程会终止
5. 面上雨量计算结果保留3位小数

## 错误处理

- 文件不存在或格式错误时会显示详细错误信息
- 每个步骤都有成功/失败统计
- 支持部分站点数据缺失的情况
- 权重文件中的站点如果没有对应数据文件会给出警告

## 性能说明

- 处理速度取决于数据文件大小和站点数量
- 内存使用量与时间序列长度成正比
- 建议在处理大量数据时监控系统资源使用情况
