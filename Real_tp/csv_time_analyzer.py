#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件时间分析工具
功能：分析CSV文件的时间范围和缺失情况
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import numpy as np

class CSVTimeAnalyzer:
    def __init__(self):
        pass
        
    def analyze_csv_file(self, file_path):
        """
        分析单个CSV文件的时间范围和缺失情况
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 假设第一列是时间，第二列是雨量数据
            time_col = df.columns[0]
            rain_col = df.columns[1]
            
            # 解析时间格式 (假设格式为 "MM-DD HH")
            # 由于没有年份，我们需要推断年份
            time_strings = df[time_col].astype(str)
            
            # 尝试解析时间，处理可能的年份问题
            parsed_times = []
            for time_str in time_strings:
                try:
                    # 先尝试2024年
                    dt = pd.to_datetime('2024-' + time_str, format='%Y-%m-%d %H')
                    parsed_times.append(dt)
                except:
                    try:
                        # 如果失败，尝试2025年
                        dt = pd.to_datetime('2025-' + time_str, format='%Y-%m-%d %H')
                        parsed_times.append(dt)
                    except:
                        parsed_times.append(pd.NaT)
            
            df['datetime'] = parsed_times
            
            # 删除无法解析的时间行
            df = df.dropna(subset=['datetime'])
            
            if len(df) == 0:
                return {
                    'file': os.path.basename(file_path),
                    'error': '无法解析任何时间数据'
                }
            
            # 按时间排序
            df = df.sort_values('datetime')
            
            # 获取起止时间
            start_time = df['datetime'].min()
            end_time = df['datetime'].max()
            
            # 创建完整的小时时间序列
            full_time_range = pd.date_range(start=start_time, end=end_time, freq='H')
            
            # 找出实际存在的时间点
            actual_times = set(df['datetime'])
            expected_times = set(full_time_range)
            
            # 找出缺失的时间点
            missing_times = expected_times - actual_times
            missing_times = sorted(list(missing_times))
            
            # 分析缺失的时间范围
            missing_ranges = []
            long_missing_ranges = []  # 超过10天的连续缺失范围

            if missing_times:
                current_start = missing_times[0]
                current_end = missing_times[0]

                for i in range(1, len(missing_times)):
                    if missing_times[i] == current_end + timedelta(hours=1):
                        current_end = missing_times[i]
                    else:
                        # 处理当前范围
                        if current_start == current_end:
                            missing_ranges.append(current_start.strftime('%Y-%m-%d %H:00'))
                        else:
                            range_str = f"{current_start.strftime('%Y-%m-%d %H:00')} 到 {current_end.strftime('%Y-%m-%d %H:00')}"
                            missing_ranges.append(range_str)

                            # 检查是否超过10天
                            duration = current_end - current_start
                            if duration.total_seconds() > 10 * 24 * 3600:  # 10天
                                long_missing_ranges.append({
                                    'start': current_start.strftime('%Y-%m-%d %H:00'),
                                    'end': current_end.strftime('%Y-%m-%d %H:00'),
                                    'duration_days': duration.total_seconds() / (24 * 3600),
                                    'duration_hours': int(duration.total_seconds() / 3600)
                                })

                        current_start = missing_times[i]
                        current_end = missing_times[i]

                # 添加最后一个范围
                if current_start == current_end:
                    missing_ranges.append(current_start.strftime('%Y-%m-%d %H:00'))
                else:
                    range_str = f"{current_start.strftime('%Y-%m-%d %H:00')} 到 {current_end.strftime('%Y-%m-%d %H:00')}"
                    missing_ranges.append(range_str)

                    # 检查最后一个范围是否超过10天
                    duration = current_end - current_start
                    if duration.total_seconds() > 10 * 24 * 3600:  # 10天
                        long_missing_ranges.append({
                            'start': current_start.strftime('%Y-%m-%d %H:00'),
                            'end': current_end.strftime('%Y-%m-%d %H:00'),
                            'duration_days': duration.total_seconds() / (24 * 3600),
                            'duration_hours': int(duration.total_seconds() / 3600)
                        })
            
            return {
                'file': os.path.basename(file_path),
                'start_time': start_time.strftime('%Y-%m-%d %H:00'),
                'end_time': end_time.strftime('%Y-%m-%d %H:00'),
                'total_records': len(df),
                'expected_records': len(full_time_range),
                'missing_count': len(missing_times),
                'missing_percentage': (len(missing_times) / len(full_time_range)) * 100,
                'missing_ranges': missing_ranges,
                'long_missing_ranges': long_missing_ranges
            }
            
        except Exception as e:
            return {
                'file': os.path.basename(file_path),
                'error': f"处理文件时出错: {str(e)}"
            }
    
    def analyze_directory(self, directory_path):
        """
        分析目录中的所有CSV文件
        """
        results = []
        
        # 遍历目录中的所有CSV文件
        for filename in os.listdir(directory_path):
            if filename.endswith('.csv'):
                file_path = os.path.join(directory_path, filename)
                print(f"正在分析: {filename}")
                
                # 分析文件
                result = self.analyze_csv_file(file_path)
                results.append(result)
        
        return results
    
    def generate_report(self, results, output_file=None):
        """
        生成分析报告
        """
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("CSV文件时间分析报告")
        report_lines.append("=" * 80)
        report_lines.append("")
        
        for result in results:
            if 'error' in result:
                report_lines.append(f"文件: {result['file']}")
                report_lines.append(f"错误: {result['error']}")
                report_lines.append("-" * 60)
                continue
            
            report_lines.append(f"文件: {result['file']}")
            report_lines.append(f"起始时间: {result['start_time']}")
            report_lines.append(f"结束时间: {result['end_time']}")
            report_lines.append(f"实际记录数: {result['total_records']}")
            report_lines.append(f"期望记录数: {result['expected_records']}")
            report_lines.append(f"缺失记录数: {result['missing_count']}")
            report_lines.append(f"缺失百分比: {result['missing_percentage']:.2f}%")
            
            if result['missing_ranges']:
                report_lines.append("缺失时间范围:")
                for missing_range in result['missing_ranges']:
                    report_lines.append(f"  - {missing_range}")
            else:
                report_lines.append("无缺失时间点")
            
            report_lines.append("-" * 60)
        
        report_text = "\n".join(report_lines)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"报告已保存到: {output_file}")
        
        return report_text

    def generate_long_missing_report(self, results, output_file=None):
        """
        生成超过10天连续缺失的专门报告
        """
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("连续缺失时间超过10天的报告")
        report_lines.append("=" * 80)
        report_lines.append("")

        has_long_missing = False

        for result in results:
            if 'error' in result:
                continue

            if 'long_missing_ranges' in result and result['long_missing_ranges']:
                has_long_missing = True
                report_lines.append(f"文件: {result['file']}")
                report_lines.append(f"总体时间范围: {result['start_time']} 到 {result['end_time']}")
                report_lines.append("连续缺失超过10天的时间段:")

                for long_range in result['long_missing_ranges']:
                    report_lines.append(f"  - {long_range['start']} 到 {long_range['end']}")
                    report_lines.append(f"    持续时间: {long_range['duration_days']:.1f} 天 ({long_range['duration_hours']} 小时)")

                report_lines.append("-" * 60)

        if not has_long_missing:
            report_lines.append("未发现连续缺失时间超过10天的情况。")

        report_text = "\n".join(report_lines)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"长时间缺失报告已保存到: {output_file}")

        return report_text

    def analyze_all_directories(self, base_dirs):
        """
        分析多个目录下的所有CSV文件
        """
        all_results = {}
        
        for dir_name, dir_path in base_dirs.items():
            print(f"\n正在分析目录: {dir_name} ({dir_path})")
            print("=" * 50)
            
            if os.path.exists(dir_path):
                results = self.analyze_directory(dir_path)
                all_results[dir_name] = results
            else:
                print(f"目录不存在: {dir_path}")
                all_results[dir_name] = []
        
        return all_results

# 使用示例
if __name__ == "__main__":
    print("开始CSV时间分析...")
    analyzer = CSVTimeAnalyzer()

    # 定义要分析的目录（使用绝对路径）
    directories = {
        # 'chengkou': '/home/<USER>/Flood_flow_prediction/Real_tp/chengkou/origin_data',
        # 'guojia': '/home/<USER>/Flood_flow_prediction/Real_tp/guojia/origin_data'
        'dongxi': '/home/<USER>/Flood_flow_prediction/Real_tp/dongxi/origin_data',
        'mituo': '/home/<USER>/Flood_flow_prediction/Real_tp/mituo/origin_data'
    }

    print("定义的目录:", directories)

    # 分析所有目录
    all_results = analyzer.analyze_all_directories(directories)

    # 生成综合报告
    print("\n\n" + "=" * 80)
    print("综合分析报告")
    print("=" * 80)

    for dir_name, results in all_results.items():
        print(f"\n{dir_name.upper()} 目录分析结果:")
        print("-" * 40)

        if not results:
            print("无数据或目录不存在")
            continue

        report = analyzer.generate_report(results)
        print(report)

        # 保存单独的报告文件
        output_file = f"{dir_name}_time_analysis_report.txt"
        analyzer.generate_report(results, output_file)

        # 生成长时间缺失报告
        long_missing_file = f"{dir_name}_long_missing_report.txt"
        long_missing_report = analyzer.generate_long_missing_report(results, long_missing_file)
        print(f"\n{dir_name.upper()} 长时间缺失报告:")
        print(long_missing_report)
