#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流域的实测雨量处理
"""

from rainfall_data_processor import RainfallDataProcessor

def lianghe_process():
    """
    两河流域的实测雨量处理流程
    """
    print("\n" + "="*60)
    print("两河流域的实测雨量处理流程")
    print("="*60)
    
    # 创建处理器实例
    lianghe_processor = RainfallDataProcessor(
        origin_dir="/home/<USER>/Flood_flow_prediction/Real_tp/lianghe1/origin_data",
        merge_dir="/home/<USER>/Flood_flow_prediction/Real_tp/lianghe1/merge_data",
        weight_file="/home/<USER>/Flood_flow_prediction/Real_tp/lianghe1/weight/weight.csv", 
        output_file="/home/<USER>/Flood_flow_prediction/Real_tp/lianghe1/完整_两河流域雨量汇总.csv"
    )
    
    # 执行完整流程
    # lianghe_processor.preprocess_all_csv_file()
    success = lianghe_processor.process_rainfall_data()
    
    print(f"\n两河流域的实测雨量处理结果: {'成功' if success else '失败'}")
    return success

def yantang_process():
    """
    沿塘流域的实测雨量处理流程
    """
    print("\n" + "="*60)
    print("沿塘流域的实测雨量处理流程")
    print("="*60)
    
    # 创建处理器实例
    yantang_processor = RainfallDataProcessor(
        origin_dir="/home/<USER>/Flood_flow_prediction/Real_tp/yantang/origin_data",
        merge_dir="/home/<USER>/Flood_flow_prediction/Real_tp/yantang/merge_data",
        weight_file="/home/<USER>/Flood_flow_prediction/Real_tp/yantang/weight/weight.csv", 
        output_file="/home/<USER>/Flood_flow_prediction/Real_tp/yantang/沿塘流域雨量汇总.csv"
    )
    
    # 执行完整流程
    # yantang_processor.preprocess_all_csv_file()
    success = yantang_processor.process_rainfall_data()
    
    print(f"\n沿塘流域的实测雨量处理结果: {'成功' if success else '失败'}")
    return success


def yinhe_process():
    """
    银河流域的实测雨量处理流程
    """
    print("\n" + "="*60)
    print("银河流域的实测雨量处理流程")
    print("="*60)
    
    # 创建处理器实例
    yinhe_processor = RainfallDataProcessor(
        origin_dir="/home/<USER>/Flood_flow_prediction/Real_tp/yinhe/origin_data",
        merge_dir="/home/<USER>/Flood_flow_prediction/Real_tp/yinhe/merge_data",
        weight_file="/home/<USER>/Flood_flow_prediction/Real_tp/yinhe/weight/weight.csv", 
        output_file="/home/<USER>/Flood_flow_prediction/Real_tp/yinhe/银河流域雨量汇总.csv"
    )
    
    # 执行完整流程
    # yinhe_processor.preprocess_all_csv_file()
    success = yinhe_processor.process_rainfall_data()
    
    print(f"\n银河流域的实测雨量处理结果: {'成功' if success else '失败'}")
    return success


def chengkou_process():
    """
    城口流域的实测雨量处理流程
    """
    print("\n" + "="*60)
    print("城口流域的实测雨量处理流程")
    print("="*60)
    
    # 创建处理器实例
    chengkou_processor = RainfallDataProcessor(
        origin_dir="/home/<USER>/Flood_flow_prediction/Real_tp/chengkou/origin_data",
        merge_dir="/home/<USER>/Flood_flow_prediction/Real_tp/chengkou/merge_data",
        weight_file="/home/<USER>/Flood_flow_prediction/Real_tp/chengkou/weight/weight.csv", 
        output_file="/home/<USER>/Flood_flow_prediction/Real_tp/chengkou/城口流域雨量汇总.csv"
    )
    
    # 执行完整流程
    # chengkou_processor.preprocess_all_csv_file()
    success = chengkou_processor.process_rainfall_data()
    
    print(f"\n城口流域的实测雨量处理结果: {'成功' if success else '失败'}")
    return success


def guojia_process():
    """
    郭家流域的实测雨量处理流程
    """
    print("\n" + "="*60)
    print("郭家流域的实测雨量处理流程")
    print("="*60)

    # 创建处理器实例
    guojia_processor = RainfallDataProcessor(
        origin_dir="/home/<USER>/Flood_flow_prediction/Real_tp/guojia/origin_data",
        merge_dir="/home/<USER>/Flood_flow_prediction/Real_tp/guojia/merge_data",
        weight_file="/home/<USER>/Flood_flow_prediction/Real_tp/guojia/weight/weight.csv",
        output_file="/home/<USER>/Flood_flow_prediction/Real_tp/guojia/郭家流域雨量汇总.csv"
    )

    # 执行完整流程
    # guojia_processor.preprocess_all_csv_file()
    success = guojia_processor.process_rainfall_data()

    print(f"\n郭家流域的实测雨量处理结果: {'成功' if success else '失败'}")
    return success


def dongxi_process():
    """
    东溪流域的实测雨量处理流程
    """
    print("\n" + "="*60)
    print("东溪流域的实测雨量处理流程")
    print("="*60)

    # 创建处理器实例
    dongxi_processor = RainfallDataProcessor(
        origin_dir="/home/<USER>/Flood_flow_prediction/Real_tp/dongxi/origin_data",
        merge_dir="/home/<USER>/Flood_flow_prediction/Real_tp/dongxi/merge_data",
        weight_file="/home/<USER>/Flood_flow_prediction/Real_tp/dongxi/weight/weight.csv",
        output_file="/home/<USER>/Flood_flow_prediction/Real_tp/dongxi/东溪流域雨量汇总.csv"
    )

    # 执行完整流程
    # dongxi_processor.preprocess_all_csv_file()
    success = dongxi_processor.process_rainfall_data()

    print(f"\n东溪流域的实测雨量处理结果: {'成功' if success else '失败'}")
    return success


def mituo_process():
    """
    弥陀流域的实测雨量处理流程
    """
    print("\n" + "="*60)
    print("弥陀流域的实测雨量处理流程")
    print("="*60)

    # 创建处理器实例
    mituo_processor = RainfallDataProcessor(
        origin_dir="/home/<USER>/Flood_flow_prediction/Real_tp/mituo/origin_data",
        merge_dir="/home/<USER>/Flood_flow_prediction/Real_tp/mituo/merge_data",
        weight_file="/home/<USER>/Flood_flow_prediction/Real_tp/mituo/weight/weight.csv",
        output_file="/home/<USER>/Flood_flow_prediction/Real_tp/mituo/弥陀流域雨量汇总.csv"
    )

    # 执行完整流程
    # mituo_processor.preprocess_all_csv_file()
    success = mituo_processor.process_rainfall_data()

    print(f"\n弥陀流域的实测雨量处理结果: {'成功' if success else '失败'}")
    return success



if __name__ == "__main__":

    # lianghe_success = lianghe_process()
    # yantang_success = yantang_process()
    # yinhe_success = yinhe_process()

    # chengkou_success = chengkou_process()
    # guojia_success = guojia_process()

    dongxi_success = dongxi_process()
    mituo_success = mituo_process()


    print("\n" + "="*60)
    print("处理结果总结")
    print("="*60)
    # print(f"两河流域: {'✅ 成功' if lianghe_success else '❌ 失败'}")
    # print(f"沿塘流域: {'✅ 成功' if yantang_success else '❌ 失败'}")
    # print(f"银河流域: {'✅ 成功' if yinhe_success else '❌ 失败'}")
    # print(f"城口流域: {'✅ 成功' if chengkou_success else '❌ 失败'}")
    # print(f"郭家流域: {'✅ 成功' if guojia_success else '❌ 失败'}")
    print(f"东溪流域: {'✅ 成功' if dongxi_success else '❌ 失败'}")
    print(f"弥陀流域: {'✅ 成功' if mituo_success else '❌ 失败'}")

