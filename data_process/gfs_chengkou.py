import xarray as xr
import geopandas as gpd
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
from pathlib import Path
from shapely.geometry import Point
import time
warnings.filterwarnings('ignore')

def extract_gfs_precipitation_batch(start_date="2025-01-01", end_date=None, output_dir="GFS_chengkou_data", batch_days=7):
    """
    分批提取GFS数据中chengkou.shp边界范围内的precipitation_surface数据
    
    Parameters:
    -----------
    start_date : str
        开始日期，格式为"YYYY-MM-DD"
    end_date : str, optional
        结束日期，格式为"YYYY-MM-DD"，默认为今天
    output_dir : str
        输出目录
    batch_days : int
        每批处理的天数
        
    Returns:
    --------
    pd.DataFrame : 处理后的降水数据
    """
    
    # 设置日期范围
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    print(f"提取时间范围: {start_date} 到 {end_date}")
    print(f"分批处理，每批 {batch_days} 天")
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 读取chengkou.shp边界文件
    print("正在读取chengkou流域边界文件...")
    shapefile_path = "/home/<USER>/Flood_flow_prediction/boundary/chengkou.shp"
    chengkou_boundary = gpd.read_file(shapefile_path)
    
    # 确保坐标系正确
    if chengkou_boundary.crs is None:
        chengkou_boundary = chengkou_boundary.set_crs("EPSG:4326")
    elif chengkou_boundary.crs.to_string() != "EPSG:4326":
        chengkou_boundary = chengkou_boundary.to_crs("EPSG:4326")
    
    
    # 存储所有批次的数据
    all_batch_data = []
    
    # 分批处理
    current_date = start_dt
    batch_num = 1
    
    while current_date <= end_dt:
        batch_end_date = min(current_date + timedelta(days=batch_days-1), end_dt)
        
        print(f"\n{'='*60}")
        print(f"处理批次 {batch_num}: {current_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}")
        print(f"{'='*60}")
        
        try:
            # 加载GFS数据（每批只加载一次）
            print("正在加载GFS数据...")
            ds = xr.open_zarr("https://data.dynamical.org/noaa/gfs/forecast/latest.zarr?email=<EMAIL>")
            
            # 直接使用全球数据，不进行边界框预筛选
            print("正在使用精确的流域边界筛选坐标点...")

            # 获取全球所有经纬度坐标点
            lons = ds.longitude.values
            lats = ds.latitude.values
            print(f"全球数据大小: 经度 {len(lons)} 点, 纬度 {len(lats)} 点")

            # 遍历所有坐标点，检查哪些落在流域边界内
            print("正在检查哪些坐标点落在流域边界内...")

            # 创建所有坐标点
            all_points = []
            valid_lon_indices = []
            valid_lat_indices = []

            for i, lat in enumerate(lats):
                for j, lon in enumerate(lons):
                    point = Point(lon, lat)
                    # 检查点是否在流域边界内
                    if chengkou_boundary.geometry.contains(point).any():
                        all_points.append(point)
                        valid_lat_indices.append(i)
                        valid_lon_indices.append(j)

            print(f"找到 {len(all_points)} 个坐标点落在流域边界内")
            
            # 打印找到的具体坐标点
            if len(all_points) > 0:
                print("具体坐标点:")
                for idx, (i, j) in enumerate(zip(valid_lat_indices, valid_lon_indices)):
                    print(f"  点{idx+1}: (经度: {lons[j]:.4f}°, 纬度: {lats[i]:.4f}°)")

            if len(all_points) == 0:
                print("警告: 没有找到落在流域边界内的坐标点，使用距离最近的点")
                # 如果没有点在边界内，选择距离最近的点
                watershed_centroid = chengkou_boundary.geometry.centroid.iloc[0]
                min_distance = float('inf')
                closest_i, closest_j = 0, 0

                for i, lat in enumerate(lats):
                    for j, lon in enumerate(lons):
                        point = Point(lon, lat)
                        distance = point.distance(watershed_centroid)
                        if distance < min_distance:
                            min_distance = distance
                            closest_i, closest_j = i, j

                valid_lat_indices = [closest_i]
                valid_lon_indices = [closest_j]
                print(f"选择最近点: 经度 {lons[closest_j]:.4f}, 纬度 {lats[closest_i]:.4f}")

            # 只保留有效的坐标点
            valid_lons = lons[valid_lon_indices]
            print(valid_lons)
            valid_lats = lats[valid_lat_indices]
            print(valid_lats)
            # 重新筛选数据集，只保留有效的坐标点
            ds_spatial = ds.sel(longitude=valid_lons, latitude=valid_lats)
            print(ds_spatial)
            print(f"最终筛选后数据大小: {len(valid_lons)} 个栅格点")
            print(f"  - 不同经度值: {len(np.unique(valid_lons))} 个: {np.unique(valid_lons)}")
            print(f"  - 不同纬度值: {len(np.unique(valid_lats))} 个: {np.unique(valid_lats)}")
            print(f"  - 总栅格点数: {len(ds_spatial.longitude) * len(ds_spatial.latitude)} 个")
            
            # 输出筛选后数据集的详细信息和几条数据实例
            print(f"\n📋 筛选后数据集详细信息:")
            print(f"数据集维度: {dict(ds_spatial.dims)}")
            print(f"坐标变量: {list(ds_spatial.coords.keys())}")
            print(f"数据变量: {list(ds_spatial.data_vars.keys())}")
            
            print(f"\n🔍 经纬度坐标值:")
            print(f"经度值: {ds_spatial.longitude.values}")
            print(f"纬度值: {ds_spatial.latitude.values}")
            
            if 'precipitation_surface' in ds_spatial.data_vars:
                print(f"\n💧 precipitation_surface数据实例:")
                precip = ds_spatial.precipitation_surface
                print(f"数据形状: {precip.shape}")
                print(f"数据维度: {precip.dims}")
                
                # 输出前几个时间步的数据样例
                if len(precip.init_time) > 0 and len(precip.lead_time) > 0:
                    first_init = precip.init_time.values[0]
                    first_few_leads = precip.lead_time.values[:3]  # 前3个预测时间
                    
                    print(f"\n📊 数据样例 (初始化时间: {pd.to_datetime(first_init)}):")
                    for i, lead in enumerate(first_few_leads):
                        lead_hours = pd.to_timedelta(lead).total_seconds() / 3600
                        sample_data = precip.sel(init_time=first_init, lead_time=lead)
                        print(f"  预测时间 +{lead_hours:.0f}小时:")
                        print(f"    数据值: {sample_data.values}")
                        print(f"    形状: {sample_data.shape}")
                        print(f"    坐标: longitude={sample_data.longitude.values}, latitude={sample_data.latitude.values}")
                        if i < 2:  # 只显示前2个的详细信息
                            print(f"    具体值:")
                            for j, lon in enumerate(sample_data.longitude.values):
                                for k, lat in enumerate(sample_data.latitude.values):
                                    val = sample_data.values[j, k] if sample_data.ndim == 2 else sample_data.values[j*len(sample_data.latitude)+k]
                                    print(f"      ({lon:.4f}°, {lat:.4f}°): {val:.6f}")
                        print()
            else:
                print("⚠️  未找到precipitation_surface变量")

            # 检查precipitation_surface变量
            if 'precipitation_surface' not in ds_spatial.data_vars:
                print("错误: 未找到precipitation_surface变量")
                continue
            
            # 筛选时间范围内的初始化时间
            batch_start_time = pd.Timestamp(current_date)
            batch_end_time = pd.Timestamp(batch_end_date) + pd.Timedelta(days=1)
            
            # 获取该批次时间范围内的初始化时间
            init_times = ds_spatial.init_time.values
            init_times_pd = pd.to_datetime(init_times)
            
            # 筛选批次时间范围内的初始化时间
            mask = (init_times_pd >= batch_start_time) & (init_times_pd < batch_end_time)
            batch_init_times = init_times[mask]
            
            if len(batch_init_times) == 0:
                print(f"该批次没有可用的初始化时间，跳过")
                current_date = batch_end_date + timedelta(days=1)
                batch_num += 1
                continue
            
            print(f"该批次有 {len(batch_init_times)} 个初始化时间")
            
            # 存储该批次的数据
            batch_data = []
            
            # 处理该批次的每个初始化时间
            for i, init_time in enumerate(batch_init_times):
                try:
                    init_time_dt = pd.to_datetime(init_time)
                    print(f"  处理初始化时间 {i+1}/{len(batch_init_times)}: {init_time_dt}")
                    
                    # 选择该初始化时间的数据
                    ds_init = ds_spatial.sel(init_time=init_time)
                    
                    # 选择1-6小时的预测（跳过0小时预测）
                    lead_times = ds_init.lead_time.values
                    
                    # 筛选1-6小时的预测
                    if len(lead_times) >= 7:
                        selected_lead_times = lead_times[1:7]  # 取第2到第7个时间步（1-6小时）
                    elif len(lead_times) >= 2:
                        selected_lead_times = lead_times[1:]  # 跳过第一个，取剩余的
                    else:
                        print(f"    警告: lead_time数量不足，跳过该初始化时间")
                        continue
                    
                    ds_forecast = ds_init.sel(lead_time=selected_lead_times)
                    
                    # 提取precipitation_surface数据
                    precip_data = ds_forecast.precipitation_surface
                    
                    # 转换为DataFrame
                    df = precip_data.to_dataframe().reset_index()
                    
                    # 只删除precipitation_surface列的NaN值
                    df = df.dropna(subset=['precipitation_surface'])
                    
                    if len(df) == 0:
                        print(f"    警告: 该初始化时间没有有效数据")
                        continue
                    
                    # 重命名列
                    if 'precipitation_surface' in df.columns:
                        df = df.rename(columns={'precipitation_surface': 'tp'})
                    
                    # 计算valid_time（初始化时间 + 预测步长）
                    df['valid_time'] = pd.to_datetime(df['init_time']) + pd.to_timedelta(df['lead_time'])

                    # 检查数据中的重复坐标点问题
                    print(f"    原始数据行数: {len(df)}")
                    print(f"    数据中的坐标点分布:")
                    coord_counts = df.groupby(['longitude', 'latitude']).size()
                    for (lon, lat), count in coord_counts.items():
                        print(f"      ({lon:.4f}°, {lat:.4f}°): {count} 条数据")

                    # 去除重复的坐标点，对于相同的(longitude, latitude, valid_time)组合，只保留一条记录
                    # 这里使用mean()来处理可能的重复值，但通常应该是相同的
                    df_deduplicated = df.groupby(['longitude', 'latitude', 'valid_time'])['tp'].mean().reset_index()
                    print(f"    去重后数据行数: {len(df_deduplicated)}")

                    # 现在按valid_time分组，对tp进行求和（流域内所有不重复网格点的降水量之和）
                    grouped_df = df_deduplicated.groupby('valid_time')['tp'].sum().reset_index()

                    # 添加到批次数据中
                    batch_data.append(grouped_df)

                    # 计算实际使用的栅格点数
                    unique_coords = df_deduplicated[['longitude', 'latitude']].drop_duplicates()
                    actual_grid_points = len(unique_coords)
                    print(f"    成功提取 {len(grouped_df)} 个时间点的数据，实际使用栅格点数: {actual_grid_points}")
                    print(f"    每个时间点合并了 {actual_grid_points} 个不重复栅格点的数据")
                    
                except Exception as e:
                    print(f"    错误: 处理初始化时间时出错 - {e}")
                    continue
            
            # 合并该批次的数据
            if batch_data:
                print(f"\n合并批次 {batch_num} 的数据...")
                batch_combined = pd.concat(batch_data, ignore_index=True)
                
                # 按时间排序并去重
                batch_combined = batch_combined.sort_values('valid_time').reset_index(drop=True)
                batch_combined = batch_combined.drop_duplicates(subset=['valid_time'], keep='last')
                
                # 添加到总数据中
                all_batch_data.append(batch_combined)
                
                print(f"批次 {batch_num} 完成: {len(batch_combined)} 条记录")
                print(f"时间范围: {batch_combined['valid_time'].min()} 到 {batch_combined['valid_time'].max()}")
            else:
                print(f"批次 {batch_num} 没有提取到数据")
            
        except Exception as e:
            print(f"批次 {batch_num} 处理失败: {e}")
        
        # 移动到下一批次
        current_date = batch_end_date + timedelta(days=1)
        batch_num += 1
        
        # 添加延迟以避免过度请求
        time.sleep(2)
    
    # 合并所有批次的数据
    if all_batch_data:
        print(f"\n{'='*60}")
        print(f"合并所有批次数据...")
        final_combined = pd.concat(all_batch_data, ignore_index=True)
        
        # 最终排序和去重
        final_combined = final_combined.sort_values('valid_time').reset_index(drop=True)
        final_combined = final_combined.drop_duplicates(subset=['valid_time'], keep='last')
        
        print(f"最终合并数据: {len(final_combined)} 条记录")
        print(f"时间范围: {final_combined['valid_time'].min()} 到 {final_combined['valid_time'].max()}")
        
        # 保存到CSV文件
        output_file = output_path / f"chengkou_gfs_precipitation_{start_date}_to_{end_date}_complete.csv"
        final_combined.to_csv(output_file, index=False)
        
        print(f"数据已保存到: {output_file}")
        
        # 数据质量检查
        print(f"\n数据质量检查:")
        print(f"  总记录数: {len(final_combined)}")
        print(f"  时间范围: {final_combined['valid_time'].min()} 到 {final_combined['valid_time'].max()}")
        print(f"  降水统计: 最小值={final_combined['tp'].min():.6f}, 最大值={final_combined['tp'].max():.6f}, 平均值={final_combined['tp'].mean():.6f}")
        
        return final_combined
    
    else:
        print("未能提取到任何数据")
        return None

if __name__ == "__main__":
    print("🌧️  开始分批提取GFS降水数据...")
    print("=" * 60)
    
    # 先测试提取最近一个月的数据
    result_df = extract_gfs_precipitation_batch(
        start_date="2025-08-01",  # 从7月1日开始测试
        end_date=None,  # 默认到今天
        output_dir="GFS_chengkou_data",
        batch_days=3  # 每批处理3天，减少数据量
    )
    
    if result_df is not None:
        print("\n🎉 数据提取完成！")
        print(f"生成的文件包含 {len(result_df)} 条记录")
        print("数据说明:")
        print("  - valid_time: 预测有效时间")
        print("  - tp: chengkou流域内降水量总和")
        print("  - 数据来源: NOAA GFS模式")
        print("  - 时间分辨率: 1小时")
        print("  - 预测策略: 每6小时初始化，取最新6小时预测")
        print("  - 处理方式: 分批处理，选择距离流域中心最近的4个网格点")
    else:
        print("❌ 数据提取失败！")
