"""
经纬度栅格面积精确计算工具
支持多种计算方法：球面几何、椭球面积计算、投影坐标系计算
"""

import numpy as np
import math
from typing import Tuple, List
import warnings

# 方法1：使用pyproj + shapely（推荐）
try:
    from pyproj import Geod, CRS, Transformer
    from shapely.geometry import Polygon
    PYPROJ_AVAILABLE = True
except ImportError:
    PYPROJ_AVAILABLE = False
    print("警告: pyproj未安装，将使用简化计算方法")
    print("安装命令: pip install pyproj shapely")

# 方法2：使用geopy（备选）
try:
    from geopy.distance import geodesic
    GEOPY_AVAILABLE = True
except ImportError:
    GEOPY_AVAILABLE = False
    print("提示: geopy未安装，无法使用geopy方法")
    print("安装命令: pip install geopy")


class GridAreaCalculator:
    """经纬度栅格面积计算器"""
    
    def __init__(self):
        # WGS84椭球参数
        self.a = 6378137.0  # 长半轴 (米)
        self.f = 1/298.257223563  # 扁率
        self.b = self.a * (1 - self.f)  # 短半轴
        
        if PYPROJ_AVAILABLE:
            # 初始化测地线计算器
            self.geod = Geod(ellps='WGS84')
    
    def method1_pyproj_shapely(self, center_lat: float, center_lon: float, 
                              grid_size: float = 0.25) -> float:
        """
        方法1：使用pyproj + shapely进行精确计算（推荐）
        这是最准确的方法，考虑了地球椭球面
        """
        if not PYPROJ_AVAILABLE:
            raise ImportError("需要安装pyproj和shapely库")
        
        # 计算栅格边界
        half_size = grid_size / 2
        min_lat = center_lat - half_size
        max_lat = center_lat + half_size
        min_lon = center_lon - half_size
        max_lon = center_lon + half_size
        
        # 创建栅格多边形（顺时针顺序）
        polygon_coords = [
            (min_lon, min_lat),  # 左下角
            (max_lon, min_lat),  # 右下角
            (max_lon, max_lat),  # 右上角
            (min_lon, max_lat),  # 左上角
            (min_lon, min_lat)   # 闭合
        ]
        
        # 使用测地线计算面积
        lons, lats = zip(*polygon_coords)
        area, perimeter = self.geod.polygon_area_perimeter(lons, lats)
        
        # 返回绝对值（平方米转平方公里）
        return abs(area) / 1_000_000
    
    def method2_geopy(self, center_lat: float, center_lon: float, 
                     grid_size: float = 0.25) -> float:
        """
        方法2：使用geopy计算四个角点距离
        """
        if not GEOPY_AVAILABLE:
            raise ImportError("需要安装geopy库")
        
        half_size = grid_size / 2
        
        # 四个角点
        corners = [
            (center_lat - half_size, center_lon - half_size),  # 左下
            (center_lat - half_size, center_lon + half_size),  # 右下
            (center_lat + half_size, center_lon + half_size),  # 右上
            (center_lat + half_size, center_lon - half_size),  # 左上
        ]
        
        # 计算边长
        # 底边长度
        bottom_dist = geodesic(corners[0], corners[1]).kilometers
        # 顶边长度
        top_dist = geodesic(corners[3], corners[2]).kilometers
        # 左边长度
        left_dist = geodesic(corners[0], corners[3]).kilometers
        # 右边长度
        right_dist = geodesic(corners[1], corners[2]).kilometers
        
        # 使用梯形面积公式（近似）
        avg_width = (bottom_dist + top_dist) / 2
        avg_height = (left_dist + right_dist) / 2
        
        return avg_width * avg_height
    
    def method3_spherical_geometry(self, center_lat: float, center_lon: float, 
                                  grid_size: float = 0.25) -> float:
        """
        方法3：球面几何计算（简化但较准确）
        """
        # 地球平均半径（米）
        R = 6371000
        
        # 转换为弧度
        lat_rad = math.radians(center_lat)
        grid_rad = math.radians(grid_size)
        
        # 纬度方向距离（恒定）
        lat_dist = R * grid_rad
        
        # 经度方向距离（随纬度变化）
        lon_dist = R * grid_rad * math.cos(lat_rad)
        
        # 面积（平方米转平方公里）
        return (lat_dist * lon_dist) / 1_000_000
    
    def method4_accurate_ellipsoid(self, center_lat: float, center_lon: float, 
                                  grid_size: float = 0.25) -> float:
        """
        方法4：椭球面积计算（高精度数值积分）
        """
        # 椭球参数
        a = self.a  # 长半轴
        e2 = 2 * self.f - self.f**2  # 第一偏心率的平方
        
        # 转换为弧度
        lat_center = math.radians(center_lat)
        dlat = math.radians(grid_size)
        dlon = math.radians(grid_size)
        
        # 纬度范围
        lat1 = lat_center - dlat/2
        lat2 = lat_center + dlat/2
        
        # 使用数值积分计算面积
        def integrand(lat):
            return a**2 * math.cos(lat) * (1 - e2 * math.sin(lat)**2)**(-0.5)
        
        # 简单的梯形积分
        n_steps = 100
        lat_step = (lat2 - lat1) / n_steps
        area = 0
        
        for i in range(n_steps):
            lat_i = lat1 + i * lat_step
            lat_next = lat1 + (i + 1) * lat_step
            area += (integrand(lat_i) + integrand(lat_next)) * lat_step / 2
        
        # 乘以经度差
        area *= dlon
        
        # 转换为平方公里
        return area / 1_000_000
    
    def compare_all_methods(self, center_lat: float, center_lon: float, 
                           grid_size: float = 0.25) -> dict:
        """
        比较所有可用方法的计算结果
        """
        results = {}
        
        # 方法1：pyproj + shapely
        if PYPROJ_AVAILABLE:
            try:
                results['pyproj_shapely'] = self.method1_pyproj_shapely(
                    center_lat, center_lon, grid_size)
            except Exception as e:
                results['pyproj_shapely'] = f"错误: {e}"
        
        # 方法2：geopy
        if GEOPY_AVAILABLE:
            try:
                results['geopy'] = self.method2_geopy(
                    center_lat, center_lon, grid_size)
            except Exception as e:
                results['geopy'] = f"错误: {e}"
        
        # 方法3：球面几何
        try:
            results['spherical'] = self.method3_spherical_geometry(
                center_lat, center_lon, grid_size)
        except Exception as e:
            results['spherical'] = f"错误: {e}"
        
        # 方法4：椭球积分
        try:
            results['ellipsoid'] = self.method4_accurate_ellipsoid(
                center_lat, center_lon, grid_size)
        except Exception as e:
            results['ellipsoid'] = f"错误: {e}"
        
        return results


def calculate_grid_area(center_lat: float, center_lon: float, 
                       grid_size: float = 0.25, method: str = 'auto') -> float:
    """
    便捷函数：计算指定中心点栅格的面积
    
    Args:
        center_lat: 中心纬度
        center_lon: 中心经度
        grid_size: 栅格大小（度）
        method: 计算方法 ('auto', 'pyproj', 'geopy', 'spherical', 'ellipsoid')
    
    Returns:
        面积（平方公里）
    """
    calculator = GridAreaCalculator()
    
    if method == 'auto':
        # 自动选择最佳可用方法
        if PYPROJ_AVAILABLE:
            return calculator.method1_pyproj_shapely(center_lat, center_lon, grid_size)
        elif GEOPY_AVAILABLE:
            return calculator.method2_geopy(center_lat, center_lon, grid_size)
        else:
            return calculator.method3_spherical_geometry(center_lat, center_lon, grid_size)
    
    elif method == 'pyproj':
        return calculator.method1_pyproj_shapely(center_lat, center_lon, grid_size)
    elif method == 'geopy':
        return calculator.method2_geopy(center_lat, center_lon, grid_size)
    elif method == 'spherical':
        return calculator.method3_spherical_geometry(center_lat, center_lon, grid_size)
    elif method == 'ellipsoid':
        return calculator.method4_accurate_ellipsoid(center_lat, center_lon, grid_size)
    else:
        raise ValueError(f"未知方法: {method}")


# 示例使用和测试
if __name__ == "__main__":
    # 用户指定的22个坐标点（0.05°分辨率）
    test_points = [
        (29.00000, 107.05000, "坐标点1"),
        (29.00000, 107.10000, "坐标点2"),
        (29.05000, 107.00000, "坐标点3"),
        (29.05000, 107.05000, "坐标点4"),
        (29.05000, 107.10000, "坐标点5"),
        (29.05000, 107.15000, "坐标点6"),
        (29.05000, 107.20000, "坐标点7"),
        (29.05000, 107.25000, "坐标点8"),
        (29.10000, 107.00000, "坐标点9"),
        (29.10000, 107.05000, "坐标点10"),
        (29.10000, 107.10000, "坐标点11"),
        (29.10000, 107.15000, "坐标点12"),
        (29.10000, 107.20000, "坐标点13"),
        (29.10000, 107.25000, "坐标点14"),
        (29.10000, 107.30000, "坐标点15"),
        (29.15000, 107.05000, "坐标点16"),
        (29.15000, 107.10000, "坐标点17"),
        (29.15000, 107.15000, "坐标点18"),
        (29.15000, 107.20000, "坐标点19"),
        (29.20000, 107.05000, "坐标点20"),
        (29.20000, 107.10000, "坐标点21"),
        (29.20000, 107.15000, "坐标点22"),
    ]

    # 栅格分辨率设置为0.05度
    grid_resolution = 0.05
    
    calculator = GridAreaCalculator()
    
    print("=" * 80)
    print("经纬度栅格面积精确计算结果")
    print("=" * 80)
    
    for lat, lon, description in test_points:
        print(f"\n📍 位置: {description} ({lat}°N, {lon}°E)")
        print("-" * 60)
        
        # 比较所有方法
        results = calculator.compare_all_methods(lat, lon, grid_resolution)
        
        for method, area in results.items():
            if isinstance(area, (int, float)):
                print(f"{method:15s}: {area:8.2f} 平方公里")
            else:
                print(f"{method:15s}: {area}")
        
        # 计算方法间的差异
        numeric_results = {k: v for k, v in results.items() 
                          if isinstance(v, (int, float))}
        
        if len(numeric_results) > 1:
            values = list(numeric_results.values())
            max_val = max(values)
            min_val = min(values)
            diff_percent = ((max_val - min_val) / min_val) * 100
            print(f"\n方法间最大差异: {max_val - min_val:.2f} 平方公里 ({diff_percent:.2f}%)")
    
    print("\n" + "=" * 80)
    print("推荐使用方法:")
    if PYPROJ_AVAILABLE:
        print("✅ pyproj + shapely 方法（最准确）")
    else:
        print("⚠️  建议安装 pyproj 和 shapely 以获得最高精度")
        print("   安装命令: pip install pyproj shapely")

    # 计算22个坐标点的面积之和
    print("\n" + "=" * 80)
    print(f"22个坐标点栅格面积汇总（分辨率：{grid_resolution}°）")
    print("=" * 80)

    total_area = 0
    areas_list = []

    for lat, lon, description in test_points:
        # 使用最准确的方法计算面积
        if PYPROJ_AVAILABLE:
            area = calculator.method1_pyproj_shapely(lat, lon, grid_resolution)
        else:
            area = calculator.method3_spherical_geometry(lat, lon, grid_resolution)

        areas_list.append(area)
        total_area += area
        print(f"{description} ({lat}°N, {lon}°E): {area:8.2f} 平方公里")

    print("-" * 60)
    print(f"22个栅格面积总和: {total_area:8.2f} 平方公里")
    print(f"平均单个栅格面积: {total_area/22:8.2f} 平方公里")

    # 显示面积差异
    max_area = max(areas_list)
    min_area = min(areas_list)
    area_diff = max_area - min_area
    area_diff_percent = (area_diff / min_area) * 100

    print(f"\n栅格面积差异分析:")
    print(f"最大面积: {max_area:.2f} 平方公里")
    print(f"最小面积: {min_area:.2f} 平方公里")
    print(f"面积差异: {area_diff:.2f} 平方公里 ({area_diff_percent:.2f}%)")

    print("=" * 80)


# 批量计算函数
def batch_calculate_areas(coordinates: List[Tuple[float, float]], 
                         grid_size: float = 0.25) -> List[float]:
    """
    批量计算多个位置的栅格面积
    
    Args:
        coordinates: 坐标列表 [(lat1, lon1), (lat2, lon2), ...]
        grid_size: 栅格大小
    
    Returns:
        面积列表（平方公里）
    """
    results = []
    for lat, lon in coordinates:
        area = calculate_grid_area(lat, lon, grid_size)
        results.append(area)
    return results


# 可视化函数（需要安装matplotlib）
def plot_area_comparison(center_lat: float, center_lon: float, 
                        grid_sizes: List[float] = None):
    """
    绘制不同栅格大小的面积对比图
    """
    try:
        import matplotlib.pyplot as plt
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 中文字体
        plt.rcParams['axes.unicode_minus'] = False
    except ImportError:
        print("需要安装matplotlib才能绘图: pip install matplotlib")
        return
    
    if grid_sizes is None:
        grid_sizes = [0.1, 0.25, 0.5, 1.0]
    
    areas = []
    for size in grid_sizes:
        area = calculate_grid_area(center_lat, center_lon, size)
        areas.append(area)
    
    plt.figure(figsize=(10, 6))
    plt.plot(grid_sizes, areas, 'bo-', linewidth=2, markersize=8)
    plt.xlabel('栅格大小 (度)')
    plt.ylabel('面积 (平方公里)')
    plt.title(f'栅格面积随大小变化 - 中心点({center_lat}°N, {center_lon}°E)')
    plt.grid(True, alpha=0.3)
    plt.show()