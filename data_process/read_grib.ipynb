{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["1:Total precipitation:m (accum):regular_ll:surface:level 0:fcst time 5-6 hrs (accum):from 202312311800"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pygrib\n", "\n", "msgs = pygrib.open('/home/<USER>/Flood_flow_prediction/ERA5 hourly data on single levels from 1940 to present.grib')\n", "msg1 = msgs[1]\n", "msg1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 查看grib或GRB2格式的文件信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import xarray as xr\n", "import pygrib\n", "\n", "# ds = xr.open_dataset('/home/<USER>/Flood_flow_prediction/ERA5 hourly data on single levels from 1940 to present.grib')\n", "ds = xr.open_dataset('/home/<USER>/Flood_flow_prediction/China/Z_NWGD_C_BABJ_20240328040542_P_RFFC_SCMOC-ER01_202403280800_07201.GRB2', engine='cfgrib')\n", "ds"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 根据上面的文件信息 读取grib或GRB2格式文件并将其转换为csv格式"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import xarray as xr\n", "# ds = xr.open_dataset('/home/<USER>/Flood_flow_prediction/ERA5 hourly data on single levels from 1940 to present.grib')\n", "# ds = xr.open_dataset('/home/<USER>/Flood_flow_prediction/China/Z_NWGD_C_BABJ_20240328040542_P_RFFC_SCMOC-ER01_202403280800_07201.GRB2', engine='cfgrib')\n", "# ds = xr.open_dataset('/home/<USER>/Flood_flow_prediction/China/Z_NWGD_C_BABJ_20240328043341_P_RFFC_SCMOC-ER01_202403280800_07201.GRB2', engine='cfgrib')\n", "# ds = xr.open_dataset('/home/<USER>/Flood_flow_prediction/China/Z_NWGD_C_BABJ_20240328065721_P_RFFC_SCMOC-ER01_202403280800_07201.GRB2', engine='cfgrib')\n", "ds = xr.open_dataset('/home/<USER>/Flood_flow_prediction/China/Z_NWGD_C_BABJ_20240908092731_P_RFFC_SCMOC-ER01_202409080800_07201.GRB2', engine='cfgrib')\n", "# df = ds.to_dataframe().reset_index()\n", "# df.to_csv('lianghe00200.csv')\n", "\n", "# 计算实际的预报有效时间并添加到数据中\n", "df = ds.to_dataframe().reset_index()\n", "\n", "# 添加实际时间列（预报发起时间 + 预报步长）\n", "df['actual_time'] = df['time'] + df['step']\n", "\n", "# 重新排列列的顺序，使其更易读\n", "# df = df[['time', 'step', 'actual_time', 'latitude', 'longitude', 'tp']]\n", "df = df[['time', 'step', 'actual_time', 'latitude', 'longitude', 'unknown']]\n", "\n", "# 保存CSV\n", "df.to_csv('chinatest.csv', index=False)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame shape: (2607696, 7)\n", "Columns: ['step', 'latitude', 'longitude', 'time', 'surface', 'valid_time', 'unknown']\n", "Data saved to output_wide_format.csv\n"]}], "source": ["import xarray as xr\n", "import pandas as pd\n", "import numpy as np\n", "\n", "\n", "def grib2_to_csv_pivot(grib_file, output_csv):\n", "    \"\"\"\n", "    使用透视表格式保存（每个变量作为一列）\n", "    \"\"\"\n", "    try:\n", "        ds = xr.open_dataset(grib_file, engine='cfgrib')\n", "        \n", "        # 转换为DataFrame\n", "        df = ds.to_dataframe().reset_index()\n", "        \n", "        # 移除NaN值\n", "        df = df.dropna()\n", "        \n", "        print(f\"DataFrame shape: {df.shape}\")\n", "        print(f\"Columns: {df.columns.tolist()}\")\n", "        \n", "        df['actual_time'] = df['time'] + df['step']\n", "\n", "        # 重新排列列的顺序，使其更易读\n", "        df = df[['time', 'step', 'actual_time', 'latitude', 'longitude', 'unknown']]\n", "\n", "        # 保存为CSV\n", "        df.to_csv(output_csv, index=False)\n", "        print(f\"Data saved to {output_csv}\")\n", "        \n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    df2 = grib2_to_csv_pivot('/home/<USER>/Flood_flow_prediction/China/Z_NWGD_C_BABJ_20240328040542_P_RFFC_SCMOC-ER01_202403280800_07201.GRB2', 'output_wide_format.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 使用 pandas 简单比较整个 DataFrame 是否相同"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["两个文件存在差异\n"]}], "source": ["import pandas as pd\n", "\n", "# 读取两个 CSV 文件\n", "df1 = pd.read_csv('china.csv')\n", "df2 = pd.read_csv('china1.csv')\n", "\n", "# 判断两个 DataFrame 是否完全相等\n", "if df1.equals(df2):\n", "    print(\"两个文件内容完全一致\")\n", "else:\n", "    print(\"两个文件存在差异\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["发现以下差异：\n", "       unknown      \n", "          self other\n", "176       0.00  0.07\n", "177       0.00  0.10\n", "178       0.00  0.09\n", "179       0.00  0.09\n", "180       0.00  0.09\n", "...        ...   ...\n", "872223    0.10  0.09\n", "872417    0.07  0.06\n", "872427    0.07  0.08\n", "872827    0.05  0.06\n", "872829    0.00  0.05\n", "\n", "[20092 rows x 2 columns]\n"]}], "source": ["import pandas as pd\n", "\n", "df1 = pd.read_csv('china.csv')\n", "df2 = pd.read_csv('china1.csv')\n", "\n", "# 重置索引以确保比较准确（可选）\n", "# df1.reset_index(drop=True, inplace=True)\n", "# df2.reset_index(drop=True, inplace=True)\n", "\n", "# 比较每个单元格的值，生成布尔矩阵\n", "comparison = df1.compare(df2)\n", "\n", "if comparison.empty:\n", "    print(\"两个文件内容完全一致\")\n", "else:\n", "    print(\"发现以下差异：\")\n", "    print(comparison)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 使用.shp文件裁剪grb2文件 提取范围内的数据"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在读取GRIB2文件...\n", "正在读取 shapefile…\n", "正在进行空间裁剪…\n", "正在转换为CSV...\n", "裁剪后的数据已保存到: /home/<USER>/Flood_flow_prediction/China_clip/yantang_1229_clipped_data.csv\n"]}, {"ename": "NameError", "evalue": "name 'ds' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 87\u001b[0m\n\u001b[1;32m     84\u001b[0m clipped_ds, clipped_df \u001b[38;5;241m=\u001b[39m clip_grib_with_shapefile(grib_file, shapefile_path, output_csv)\n\u001b[1;32m     86\u001b[0m \u001b[38;5;66;03m# 查看裁剪结果\u001b[39;00m\n\u001b[0;32m---> 87\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m原始数据形状:\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[43mds\u001b[49m\u001b[38;5;241m.\u001b[39mdims)\n\u001b[1;32m     88\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m裁剪后数据形状:\u001b[39m\u001b[38;5;124m\"\u001b[39m, clipped_ds\u001b[38;5;241m.\u001b[39mdims)\n\u001b[1;32m     89\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m裁剪后DataFrame形状:\u001b[39m\u001b[38;5;124m\"\u001b[39m, clipped_df\u001b[38;5;241m.\u001b[39mshape)\n", "\u001b[0;31mNameError\u001b[0m: name 'ds' is not defined"]}], "source": ["import xarray as xr\n", "import geopandas as gpd\n", "import rioxarray as rxr\n", "from shapely.geometry import mapping\n", "import pandas as pd\n", "import numpy as np\n", "\n", "def clip_grib_with_shapefile(grib_file, shapefile_path, output_csv=None):\n", "    \"\"\"\n", "    使用shapefile裁剪GRIB2文件\n", "    \n", "    Parameters:\n", "    -----------\n", "    grib_file : str\n", "        GRIB2文件路径\n", "    shapefile_path : str\n", "        shapefile文件路径\n", "    output_csv : str, optional\n", "        输出CSV文件路径\n", "    \n", "    Returns:\n", "    --------\n", "    clipped_ds : xarray.Dataset\n", "        裁剪后的数据集\n", "    \"\"\"\n", "    \n", "    # 1. 读取GRIB2文件\n", "    print(\"正在读取GRIB2文件...\")\n", "    ds = xr.open_dataset(grib_file, engine='cfgrib')\n", "    \n", "    # 2. 设置空间参考系统 (CRS)\n", "    ds = ds.rio.write_crs(\"EPSG:4326\")\n", "    \n", "    # 3. 读取 shapefile\n", "    print(\"正在读取 shapefile…\")\n", "    gdf = gpd.read_file(shapefile_path)\n", "\n", "    # 如果 shapefile 没有 crs，就手动设置为 EPSG:4326\n", "    if gdf.crs is None:\n", "        gdf = gdf.set_crs(\"EPSG:4326\")\n", "\n", "    # 如果原始是其他坐标系，再转换\n", "    elif gdf.crs.to_string() != \"EPSG:4326\":\n", "        gdf = gdf.to_crs(\"EPSG:4326\")\n", "\n", "    # 4. 进行裁剪\n", "    print(\"正在进行空间裁剪…\")\n", "    clipped_ds = ds.rio.clip(gdf.geometry.apply(mapping), gdf.crs, drop=False)\n", "    \n", "    # 5. 转换为DataFrame并保存CSV（可选）\n", "    if output_csv:\n", "        print(\"正在转换为CSV...\")\n", "        df = clipped_ds.to_dataframe().reset_index()\n", "        df = df.dropna()  # 移除NaN值\n", "        \n", "        # 添加实际时间列（如果有time和step）\n", "        # if 'time' in df.columns and 'step' in df.columns:\n", "        #     df['actual_time'] = df['time'] + df['step']\n", "\n", "        # 删除不需要的列并重命名\n", "        # 删除不需要的列\n", "        if 'surface' in df.columns:\n", "            df = df.drop('surface', axis=1)\n", "        if 'spatial_ref' in df.columns:\n", "            df = df.drop('spatial_ref', axis=1)\n", "        df = df.rename(columns={'unknown': 'tp'})\n", "        \n", "        # 重新排列列的顺序\n", "        df = df[['time', 'step', 'valid_time', 'latitude', 'longitude', 'tp']]\n", "\n", "        df.to_csv(output_csv, index=False)\n", "        print(f\"裁剪后的数据已保存到: {output_csv}\")\n", "        \n", "        return clipped_ds, df\n", "    \n", "    return clipped_ds\n", "\n", "# 使用示例\n", "grib_file = '/home/<USER>/Flood_flow_prediction/China_Product/2024/20241229/Z_NWGD_C_BABJ_20241229091602_P_RFFC_SCMOC-ER01_202412290800_07201.GRB2'\n", "shapefile_path = '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp'  # 替换为您的shapefile路径\n", "output_csv = '/home/<USER>/Flood_flow_prediction/China_clip/yantang_1229_clipped_data.csv'\n", "\n", "# 执行裁剪\n", "clipped_ds, clipped_df = clip_grib_with_shapefile(grib_file, shapefile_path, output_csv)\n", "\n", "# 查看裁剪结果\n", "print(\"原始数据形状:\", ds.dims)\n", "print(\"裁剪后数据形状:\", clipped_ds.dims)\n", "print(\"裁剪后DataFrame形状:\", clipped_df.shape)\n"]}], "metadata": {"kernelspec": {"display_name": "flood", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 2}