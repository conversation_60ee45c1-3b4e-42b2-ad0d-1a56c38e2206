#!/usr/bin/env python3
"""
应用tp值系数到Product-use-2024文件夹下的CSV文件

对不同流域的CSV文件中的tp值应用特定的系数：
"""

import pandas as pd
import os
from pathlib import Path

def apply_tp_coefficients():
    """应用tp值系数到所有相关的CSV文件"""
    
    # 定义系数映射
    coefficients = {
        # 'lianghe': {
        #     'E1_lianghe.csv': 0.001,
        #     'E2_lianghe.csv': 0.001,
        #     'C_lianghe.csv': 7
        # },
        # 'yantang': {
        #     'E1_yantang.csv': 0.001,
        #     'E2_yantang.csv': 0.004,
        #     'C_yantang.csv': 22
        # },
        # 'yinhe': {
        #     'E1_yinhe.csv': 0.001,
        #     'E2_yinhe.csv': 0.001,
        #     'C_yinhe.csv': 2
        # },
        # 'chengkou': {
        #     # 'E1_chengkou.csv': 0.001,
        #     'E2_chengkou.csv': 0.006,
        #     # 'C_chengkou.csv': 44
        # },
        # 'guojia': {
        #     # 'E1_guojia.csv': 0.001,
        #     'E2_guojia.csv': 0.006,
        #     # 'C_guojia.csv': 46
        # },
        # 'lianghe': {
        #     # 'E1_lianghe.csv': 0.001,
        #     'E2_lianghe.csv': 0.001,
        #     # 'C_lianghe.csv': 7
        # },
        # 'yantang': {
        #     # 'E1_yantang.csv': 0.001,
        #     'E2_yantang.csv': 0.004,
        #     # 'C_yantang.csv': 22
        # },
        'mituo': {
            # 'E1_mituo.csv': 0.001,
            'E2_mituo.csv': 0.001,
            # 'C_mituo.csv': 11
        },
        'dongxi': {
            # 'E1_dongxi.csv': 0.001,
            'E2_dongxi.csv': 0.005,
            # 'C_dongxi.csv': 118
        },
    }
    
    base_dir = Path('/home/<USER>/Flood_flow_prediction/Product_2024-3-28_2025-5-10_scaled(单位m化成了mm)')
    
    if not base_dir.exists():
        print(f"错误：目录 {base_dir} 不存在")
        return
    
    # 处理每个流域
    for watershed in coefficients.keys():
        watershed_dir = base_dir / watershed
        
        if not watershed_dir.exists():
            print(f"警告：流域目录 {watershed_dir} 不存在，跳过")
            continue
            
        print(f"\n处理流域: {watershed}")
        
        # 处理该流域的每个文件
        for filename, coefficient in coefficients[watershed].items():
            file_path = watershed_dir / filename
            
            if not file_path.exists():
                print(f"  警告：文件 {file_path} 不存在，跳过")
                continue
                
            try:
                # 读取CSV文件
                print(f"  处理文件: {filename} (系数: {coefficient})")
                df = pd.read_csv(file_path)
                
                # 检查是否有tp列
                if 'tp' not in df.columns:
                    print(f"    错误：文件 {filename} 中没有找到tp列")
                    continue
                
                # 应用系数
                original_sum = df['tp'].sum()
                df['tp'] = df['tp'] / coefficient
                new_sum = df['tp'].sum()
                
                # 保存修改后的文件
                df.to_csv(file_path, index=False)
                
                print(f"    成功！tp值总和从 {original_sum:.6f} 变为 {new_sum:.6f}")
                
            except Exception as e:
                print(f"    错误：处理文件 {filename} 时出现异常: {str(e)}")
    
    print("\n所有文件处理完成！")

if __name__ == "__main__":
    apply_tp_coefficients()
