{"cells": [{"cell_type": "markdown", "id": "e2648de7", "metadata": {}, "source": ["#### 从lianghe2025.har中提取两河流域的流量数据"]}, {"cell_type": "code", "execution_count": 15, "id": "32d81f0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV written to /home/<USER>/Flood_flow_prediction/Real_flow/lianghe202500.csv\n"]}], "source": ["import os\n", "import json\n", "import csv\n", "\n", "def main():\n", "    # locate the HAR file\n", "    # base_dir = os.path.dirname(__file__)\n", "    base_dir = \"/home/<USER>/Flood_flow_prediction/Real_flow\"\n", "    har_path = os.path.join(base_dir, 'lianghe2025.har')\n", "\n", "    # read and clean comments\n", "    with open(har_path, 'r', encoding='utf-8') as f:\n", "        lines = f.readlines()\n", "    clean_text = ''.join(line for line in lines if not line.strip().startswith('//'))\n", "    har = json.loads(clean_text)\n", "    # find the relevant entry\n", "    entries = har.get('log', {}).get('entries', [])\n", "    for entry in entries:\n", "        url = entry.get('request', {}).get('url', '')\n", "        if 'waterRegimeprocChart.do' in url:\n", "            content_text = entry.get('response', {}).get('content', {}).get('text', '')\n", "            data_obj = json.loads(content_text)\n", "            data = data_obj.get('data', [])\n", "            # write CSV\n", "            csv_path = os.path.join(base_dir, 'lianghe202500.csv')\n", "            # csv_path = \"/home/<USER>/Flood_flow_prediction/Real_flow/lianghe202500.csv\"\n", "            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:\n", "                writer = csv.writer(csvfile)\n", "                writer.writerow(['时间', '水位(m)', '流量(m3/s)'])\n", "                for d in data:\n", "                    writer.writerow([d.get('TM'), d.get('Z'), d.get('Q')])\n", "            print(f'CSV written to {csv_path}')\n", "            break\n", "\n", "if __name__ == '__main__':\n", "    main()\n", "\n"]}, {"cell_type": "markdown", "id": "1c4357e9", "metadata": {}, "source": ["#### 批量提取har文件中的数据并转换为csv格式"]}, {"cell_type": "code", "execution_count": 13, "id": "e633c3e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始运行HAR文件处理脚本...\n", "发现 6 个HAR文件，开始处理...\n", "==================================================\n", "\n", "处理: lianghe2024.har\n", "✓ 成功提取 9523 条数据记录到 /home/<USER>/Flood_flow_prediction/Real_flow/lianghe2024.csv\n", "\n", "📊 数据分析 - lianghe2024.csv:\n", "   记录总数: 9523\n", "   时间范围: 2024-01-01 08:00 至 2025-01-01 07:00\n", "   水位范围: 237.06m - 243.83m\n", "   平均水位: 237.49m\n", "   流量范围: 0.02m³/s - 831.00m³/s\n", "   平均流量: 9.41m³/s\n", "------------------------------\n", "\n", "处理: lianghe2025.har\n", "✓ 成功提取 3199 条数据记录到 /home/<USER>/Flood_flow_prediction/Real_flow/lianghe2025.csv\n", "\n", "📊 数据分析 - lianghe2025.csv:\n", "   记录总数: 3199\n", "   时间范围: 2025-01-01 08:00 至 2025-05-10 17:00\n", "   水位范围: 237.10m - 239.12m\n", "   平均水位: 237.32m\n", "   流量范围: 0.03m³/s - 116.00m³/s\n", "   平均流量: 3.53m³/s\n", "------------------------------\n", "\n", "处理: yantang2024.har\n", "✓ 成功提取 7710 条数据记录到 /home/<USER>/Flood_flow_prediction/Real_flow/yantang2024.csv\n", "\n", "📊 数据分析 - yantang2024.csv:\n", "   记录总数: 7710\n", "   时间范围: 2024-01-01 08:00 至 2025-01-01 08:00\n", "   水位范围: 490.87m - 496.38m\n", "   平均水位: 491.23m\n", "   流量范围: 0.00m³/s - 569.00m³/s\n", "   平均流量: 24.39m³/s\n", "------------------------------\n", "\n", "处理: yantang2025.har\n", "✓ 成功提取 3315 条数据记录到 /home/<USER>/Flood_flow_prediction/Real_flow/yantang2025.csv\n", "\n", "📊 数据分析 - yantang2025.csv:\n", "   记录总数: 3315\n", "   时间范围: 2025-01-01 08:00 至 2025-05-22 19:00\n", "   水位范围: 490.89m - 492.86m\n", "   平均水位: 490.95m\n", "   流量范围: 0.00m³/s - 101.00m³/s\n", "   平均流量: 5.32m³/s\n", "------------------------------\n", "\n", "处理: yinhe2024.har\n", "✓ 成功提取 8345 条数据记录到 /home/<USER>/Flood_flow_prediction/Real_flow/yinhe2024.csv\n", "\n", "📊 数据分析 - yinhe2024.csv:\n", "   记录总数: 8345\n", "   时间范围: 2024-01-01 08:00 至 2024-12-31 17:00\n", "   水位范围: 272.83m - 275.82m\n", "   平均水位: 273.58m\n", "   流量范围: 0.01m³/s - 55.20m³/s\n", "   平均流量: 1.25m³/s\n", "------------------------------\n", "\n", "处理: yinhe2025.har\n", "✓ 成功提取 3025 条数据记录到 /home/<USER>/Flood_flow_prediction/Real_flow/yinhe2025.csv\n", "\n", "📊 数据分析 - yinhe2025.csv:\n", "   记录总数: 3025\n", "   时间范围: 2025-01-01 08:00 至 2025-05-10 17:00\n", "   水位范围: 273.19m - 273.79m\n", "   平均水位: 273.27m\n", "------------------------------\n", "\n", "🎉 处理完成！成功提取 6/6 个文件的数据\n", "脚本执行完成！\n"]}], "source": ["#!/usr/bin/env python3\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "HAR文件批量处理脚本\n", "用于提取洪水流量预测数据并转换为CSV格式\n", "\"\"\"\n", "\n", "import os\n", "import json\n", "import csv\n", "import glob\n", "from datetime import datetime\n", "\n", "def extract_har_to_csv(har_file_path, output_csv_path):\n", "    \"\"\"\n", "    从HAR文件中提取水位和流量数据,保存为CSV文件\n", "    \n", "    Args:\n", "        har_file_path (str): HAR文件路径\n", "        output_csv_path (str): 输出CSV文件路径\n", "    \n", "    Returns:\n", "        bool: 是否成功提取数据\n", "    \"\"\"\n", "    try:\n", "        # 读取HAR文件并清理注释\n", "        with open(har_file_path, 'r', encoding='utf-8') as f:\n", "            lines = f.readlines()\n", "        clean_text = ''.join(line for line in lines if not line.strip().startswith('//'))\n", "        har = json.loads(clean_text)\n", "        \n", "        # 查找相关的API调用\n", "        entries = har.get('log', {}).get('entries', [])\n", "        data_extracted = False\n", "        \n", "        for entry in entries:\n", "            url = entry.get('request', {}).get('url', '')\n", "            # 查找包含水位流量数据的API调用\n", "            if 'waterRegimeprocChart.do' in url or 'Chart.do' in url:\n", "                content_text = entry.get('response', {}).get('content', {}).get('text', '')\n", "                if content_text:\n", "                    try:\n", "                        data_obj = json.loads(content_text)\n", "                        data = data_obj.get('data', [])\n", "                        \n", "                        if data:\n", "                            # 写入CSV文件\n", "                            with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:\n", "                                writer = csv.writer(csvfile)\n", "                                writer.writerow(['时间', '水位(m)', '流量(m3/s)'])\n", "                                \n", "                                for d in data:\n", "                                    time_str = d.get('TM', '')\n", "                                    water_level = d.get('Z', '')\n", "                                    flow_rate = d.get('Q', '')\n", "                                    writer.writerow([time_str, water_level, flow_rate])\n", "                            \n", "                            print(f\"✓ 成功提取 {len(data)} 条数据记录到 {output_csv_path}\")\n", "                            data_extracted = True\n", "                            break\n", "                    except json.JSONDecodeError:\n", "                        continue\n", "        \n", "        if not data_extracted:\n", "            print(f\"✗ 在 {har_file_path} 中未找到有效的水位流量数据\")\n", "            return False\n", "            \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ 处理 {har_file_path} 时出错: {str(e)}\")\n", "        return False\n", "\n", "def analyze_csv_data(csv_file_path):\n", "    \"\"\"\n", "    分析CSV文件中的数据统计信息\n", "    \n", "    Args:\n", "        csv_file_path (str): CSV文件路径\n", "    \"\"\"\n", "    try:\n", "        with open(csv_file_path, 'r', encoding='utf-8') as f:\n", "            reader = csv.Dict<PERSON><PERSON><PERSON>(f)\n", "            data = list(reader)\n", "        \n", "        if not data:\n", "            print(f\"CSV文件 {csv_file_path} 为空\")\n", "            return\n", "        \n", "        # 统计信息\n", "        water_levels = [float(row['水位(m)']) for row in data if row['水位(m)']]\n", "        flow_rates = [float(row['流量(m3/s)']) for row in data if row['流量(m3/s)']]\n", "        \n", "        print(f\"\\n📊 数据分析 - {os.path.basename(csv_file_path)}:\")\n", "        print(f\"   记录总数: {len(data)}\")\n", "        print(f\"   时间范围: {data[0]['时间']} 至 {data[-1]['时间']}\")\n", "        \n", "        if water_levels:\n", "            print(f\"   水位范围: {min(water_levels):.2f}m - {max(water_levels):.2f}m\")\n", "            print(f\"   平均水位: {sum(water_levels)/len(water_levels):.2f}m\")\n", "        \n", "        if flow_rates:\n", "            print(f\"   流量范围: {min(flow_rates):.2f}m³/s - {max(flow_rates):.2f}m³/s\")\n", "            print(f\"   平均流量: {sum(flow_rates)/len(flow_rates):.2f}m³/s\")\n", "            \n", "    except Exception as e:\n", "        print(f\"分析CSV文件 {csv_file_path} 时出错: {str(e)}\")\n", "\n", "def main():\n", "    \"\"\"主函数：批量处理所有HAR文件\"\"\"\n", "    # 获取当前目录\n", "    # base_dir = os.path.dirname(os.path.abspath(__file__))\n", "    base_dir = \"/home/<USER>/Flood_flow_prediction/Real_flow\"\n", "    \n", "    # 查找所有HAR文件\n", "    har_files = glob.glob(os.path.join(base_dir, '*.har'))\n", "    \n", "    if not har_files:\n", "        print(\"未找到HAR文件\")\n", "        return\n", "    \n", "    print(f\"发现 {len(har_files)} 个HAR文件，开始处理...\")\n", "    print(\"=\" * 50)\n", "    \n", "    successful_extractions = 0\n", "    \n", "    for har_file in sorted(har_files):\n", "        har_name = os.path.basename(har_file)\n", "        csv_name = har_name.replace('.har', '.csv')\n", "        csv_path = os.path.join(base_dir, csv_name)\n", "        \n", "        print(f\"\\n处理: {har_name}\")\n", "        \n", "        if extract_har_to_csv(har_file, csv_path):\n", "            successful_extractions += 1\n", "            analyze_csv_data(csv_path)\n", "        \n", "        print(\"-\" * 30)\n", "    \n", "    print(f\"\\n🎉 处理完成！成功提取 {successful_extractions}/{len(har_files)} 个文件的数据\")\n", "\n", "if __name__ == '__main__':\n", "    print(\"开始运行HAR文件处理脚本...\")\n", "    main()\n", "    print(\"脚本执行完成！\")\n"]}], "metadata": {"kernelspec": {"display_name": "flood", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}