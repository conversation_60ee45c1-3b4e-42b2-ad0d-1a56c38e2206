import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def visualize_watershed_boundaries(user_areas=None):
    """
    可视化四个流域边界的shp文件
    
    Parameters:
    user_areas (dict): 用户提供的面积数据，格式为 {'chengkou': area1, 'lianghe': area2, ...}
                      如果为None，则不显示面积信息
    """
    print("🗺️  加载并可视化流域边界文件...")
    
    # 设置字体（使用默认英文字体，避免中文乱码）
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 流域文件路径
    watershed_files = {
        'chengkou': '/home/<USER>/Flood_flow_prediction/boundary/chengkou.shp',
        'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        'guojia': '/home/<USER>/Flood_flow_prediction/boundary/guojia.shp',
        'dongxi': '/home/<USER>/Flood_flow_prediction/boundary/dongxi.shp',
        'mituo': '/home/<USER>/Flood_flow_prediction/boundary/mituo.shp'
    }

    # 颜色设置
    colors = {
        'chengkou': 'red',
        'lianghe': 'blue',
        'yantang': 'green',
        'guojia': 'purple',
        'dongxi': 'orange',
        'mituo': 'brown'
    }

    # 英文名称
    english_names = {
        'chengkou': 'Chengkou',
        'lianghe': 'Lianghe',
        'yantang': 'Yantang',
        'guojia': 'Guojia',
        'dongxi': 'Dongxi',
        'mituo': 'Mituo'
    }
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # 存储所有边界信息
    boundaries = {}
    all_bounds = []
    
    print("正在加载流域边界文件...")
    
    # 加载并绘制每个流域
    for name, file_path in watershed_files.items():
        try:
            # 读取shapefile
            gdf = gpd.read_file(file_path)
            
            # 确保坐标系为WGS84
            if gdf.crs is None:
                gdf = gdf.set_crs("EPSG:4326")
            elif gdf.crs.to_string() != "EPSG:4326":
                gdf = gdf.to_crs("EPSG:4326")
            
            # 存储边界信息
            boundaries[name] = gdf
            bounds = gdf.total_bounds
            all_bounds.append(bounds)
            
            # 计算面积（仅用于控制台输出，如果用户提供了面积数据则使用用户数据）
            gdf_proj = gdf.to_crs('EPSG:3857')  # Web Mercator
            calculated_area = gdf_proj.area.sum() / 1e6  # 转换为平方公里
            
            print(f"✅ {english_names[name]} Watershed:")
            print(f"   File: {file_path}")
            print(f"   Bounds: Longitude[{bounds[0]:.4f}, {bounds[2]:.4f}], Latitude[{bounds[1]:.4f}, {bounds[3]:.4f}]")
            
            # 根据user_areas设置决定是否显示面积
            if user_areas and name in user_areas:
                display_area = user_areas[name]
                area_source = "(user provided)"
                print(f"   Area: {display_area:.2f} km² {area_source}")
            elif user_areas is None:
                # 当user_areas为None时，不显示面积信息
                pass
            else:
                display_area = calculated_area
                area_source = "(calculated)"
                print(f"   Area: {display_area:.2f} km² {area_source}")
            
            # 绘制流域边界（不显示面积信息）
            gdf.boundary.plot(ax=ax, color=colors[name], linewidth=2, 
                             label=f'{english_names[name]} Watershed')
            
            # 填充流域区域（半透明）
            gdf.plot(ax=ax, facecolor=colors[name], alpha=0.2, edgecolor=colors[name])
            
            # 添加流域名称标注
            centroid = gdf.geometry.centroid.iloc[0]
            ax.annotate(english_names[name], (centroid.x, centroid.y), 
                       ha='center', va='center', fontsize=12, fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
            
        except Exception as e:
            print(f"❌ Failed to load {english_names[name]} watershed: {e}")
    
    # 计算总体边界
    if all_bounds:
        overall_bounds = np.array(all_bounds)
        min_lon = overall_bounds[:, 0].min()
        min_lat = overall_bounds[:, 1].min()
        max_lon = overall_bounds[:, 2].max()
        max_lat = overall_bounds[:, 3].max()
        
        print(f"\n📊 Overall Statistics:")
        print(f"   All watersheds bounds: Longitude[{min_lon:.4f}, {max_lon:.4f}], Latitude[{min_lat:.4f}, {max_lat:.4f}]")
        print(f"   Longitude span: {max_lon - min_lon:.4f}°")
        print(f"   Latitude span: {max_lat - min_lat:.4f}°")
        
        # 设置地图范围（添加适当边距）
        margin_lon = (max_lon - min_lon) * 0.05
        margin_lat = (max_lat - min_lat) * 0.05
        
        ax.set_xlim(min_lon - margin_lon, max_lon + margin_lon)
        ax.set_ylim(min_lat - margin_lat, max_lat + margin_lat)
    
    # 设置坐标轴和标题
    ax.set_xlabel('Longitude (°E)', fontsize=12)
    ax.set_ylabel('Latitude (°N)', fontsize=12)
    ax.set_title('Watershed Boundaries Distribution', fontsize=16, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='--')
    
    # 添加图例
    ax.legend(loc='best', fontsize=11, framealpha=0.9)
    
    # 设置坐标轴刻度
    ax.tick_params(axis='both', which='major', labelsize=10)
    
    # 添加坐标轴格式
    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.2f}°E'))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, p: f'{y:.2f}°N'))
    
    # 保存图片
    output_file = '/home/<USER>/Flood_flow_prediction/watershed_boundaries_visualization.png'
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"\n📊 可视化图表已保存到: {output_file}")
    
    # 显示图片
    plt.show()
    
    # 创建详细的流域对比表
    print(f"\n📋 Detailed Watershed Comparison:")
    print("-" * 80)
    if user_areas:
        print(f"{'Watershed':<10} {'Longitude Range':<20} {'Latitude Range':<20} {'Area(km²)':<12}")
        print("-" * 80)
        
        for name, gdf in boundaries.items():
            bounds = gdf.total_bounds
            
            # 使用用户提供的面积数据
            if name in user_areas:
                area_km2 = user_areas[name]
            else:
                gdf_proj = gdf.to_crs('EPSG:3857')
                area_km2 = gdf_proj.area.sum() / 1e6
            
            lon_range = f"[{bounds[0]:.3f}, {bounds[2]:.3f}]"
            lat_range = f"[{bounds[1]:.3f}, {bounds[3]:.3f}]"
            
            print(f"{english_names[name]:<10} {lon_range:<20} {lat_range:<20} {area_km2:<12.2f}")
    else:
        print(f"{'Watershed':<10} {'Longitude Range':<20} {'Latitude Range':<20}")
        print("-" * 80)
        
        for name, gdf in boundaries.items():
            bounds = gdf.total_bounds
            
            lon_range = f"[{bounds[0]:.3f}, {bounds[2]:.3f}]"
            lat_range = f"[{bounds[1]:.3f}, {bounds[3]:.3f}]"
            
            print(f"{english_names[name]:<10} {lon_range:<20} {lat_range:<20}")
    
    print("-" * 80)
    
    return boundaries

def create_individual_plots(boundaries, user_areas=None):
    """
    创建每个流域的单独详细图
    
    Parameters:
    boundaries: 流域边界数据
    user_areas (dict): 用户提供的面积数据
    """
    print("\n🎨 Creating individual detailed plots for each watershed...")
    
    # 设置颜色
    colors = {
        'chengkou': 'red',
        'lianghe': 'blue',
        'yantang': 'green',
        'guojia': 'purple',
        'dongxi': 'orange',
        'mituo': 'brown'
    }

    english_names = {
        'chengkou': 'Chengkou',
        'lianghe': 'Lianghe',
        'yantang': 'Yantang',
        'guojia': 'Guojia',
        'dongxi': 'Dongxi',
        'mituo': 'Mituo'
    }

    # 创建3x2的子图（6个流域）
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    axes = axes.flatten()
    
    for i, (name, gdf) in enumerate(boundaries.items()):
        ax = axes[i]
        
        # 绘制流域
        gdf.boundary.plot(ax=ax, color=colors[name], linewidth=2)
        gdf.plot(ax=ax, facecolor=colors[name], alpha=0.3, edgecolor=colors[name])
        
        # 设置标题和坐标轴
        ax.set_title(f'{english_names[name]} Watershed', fontsize=14, fontweight='bold')
        ax.set_xlabel('Longitude (°E)', fontsize=10)
        ax.set_ylabel('Latitude (°N)', fontsize=10)
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        # 设置坐标轴范围（添加适当边距）
        bounds = gdf.total_bounds
        margin_lon = (bounds[2] - bounds[0]) * 0.1
        margin_lat = (bounds[3] - bounds[1]) * 0.1
        
        ax.set_xlim(bounds[0] - margin_lon, bounds[2] + margin_lon)
        ax.set_ylim(bounds[1] - margin_lat, bounds[3] + margin_lat)
        
        # 添加坐标轴格式
        ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.2f}°'))
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, p: f'{y:.2f}°'))
        
        # 添加流域信息文本
        if user_areas and name in user_areas:
            area_km2 = user_areas[name]
            info_text = f'Area: {area_km2:.2f} km² (user)\nLongitude: [{bounds[0]:.3f}°, {bounds[2]:.3f}°]\nLatitude: [{bounds[1]:.3f}°, {bounds[3]:.3f}°]'
        elif user_areas is None:
            # 当user_areas为None时，不显示面积信息
            info_text = f'Longitude: [{bounds[0]:.3f}°, {bounds[2]:.3f}°]\nLatitude: [{bounds[1]:.3f}°, {bounds[3]:.3f}°]'
        else:
            gdf_proj = gdf.to_crs('EPSG:3857')
            area_km2 = gdf_proj.area.sum() / 1e6
            info_text = f'Area: {area_km2:.2f} km² (calc)\nLongitude: [{bounds[0]:.3f}°, {bounds[2]:.3f}°]\nLatitude: [{bounds[1]:.3f}°, {bounds[3]:.3f}°]'
        
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                verticalalignment='top', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    plt.suptitle('Individual Watershed Boundaries', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    output_file = '/home/<USER>/Flood_flow_prediction/individual_watershed_boundaries.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"📊 Individual watershed plots saved to: {output_file}")
    
    plt.show()

if __name__ == "__main__":
    print("=" * 80)
    print("🏞️  Watershed Boundaries Visualization")
    print("=" * 80)
    
    # 用户提供的面积数据（您可以在这里修改面积值）
    # 如果不想显示面积，将user_areas设为None
    # user_areas = {
    #     'chengkou': 1167,   # 您可以修改这些数值
    #     'lianghe': 157,
    #     'yantang': 599,
    #     'guojia': 1273,
    #     'dongxi': 850,      # 东溪流域面积（示例值）
    #     'mituo': 420        # 弥陀流域面积（示例值）
    # }
    
    # 如果不想显示面积信息，取消注释下面这行：
    user_areas = None
    
    print("📝 Using area data:")
    if user_areas:
        for name, area in user_areas.items():
            print(f"   {name.capitalize()}: {area} km²")
    else:
        print("   No area data will be displayed")
    print()
    
    # 可视化所有流域边界
    boundaries = visualize_watershed_boundaries(user_areas)
    
    if boundaries:
        # 创建单独的详细图
        create_individual_plots(boundaries, user_areas)
        
        print(f"\n✅ Visualization completed!")
        print(f"   - Overview: watershed_boundaries_visualization.png")
        print(f"   - Detailed plots: individual_watershed_boundaries.png")
    else:
        print("❌ Failed to load any watershed boundary files")
    
    print("\n" + "=" * 80)
