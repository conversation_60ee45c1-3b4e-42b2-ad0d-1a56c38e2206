<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流量预测系统 - 多源降雨产品融合系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="auth.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f7;
            height: 100vh;
            padding: 0;
            overflow: hidden;
        }
        
        .container {
            width: 100%;
            height: 100vh;
            margin: 0;
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px 30px;
            flex-shrink: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
            height: 80px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-left h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-left p {
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0;
        }

        .nav-container {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 40px;
        }

        .nav-left {
            flex: 0 0 auto;
            min-width: 300px;
        }

        .nav-center {
            flex: 1;
            display: flex;
            justify-content: center;
        }

        .nav-right {
            flex: 0 0 auto;
            margin-left: auto;
        }

        .main-nav {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9em;
            border: 2px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .nav-link.admin-only {
            background: rgba(255, 193, 7, 0.2);
            border-color: rgba(255, 193, 7, 0.3);
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1em;
            color: white;
        }

        .user-welcome {
            opacity: 0.8;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.7;
            font-size: 1.0em;
        }

        .user-role::before {
            content: "(";
        }

        .user-role::after {
            content: ")";
        }

        .logout-btn {
            padding: 8px 16px;
            background: rgba(220, 53, 69, 0.2);
            color: white;
            border: 2px solid rgba(220, 53, 69, 0.3);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9em;
        }

        .logout-btn:hover {
            background: rgba(220, 53, 69, 0.3);
            transform: translateY(-2px);
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.2em;
        }

        .dropdown-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .hamburger {
            display: block;
            font-size: 1.1em;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 8px;
            background: white;
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            z-index: 1000;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .dropdown-content.show {
            display: block;
            animation: dropdownFadeIn 0.3s ease;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9em;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #667eea;
        }

        .dropdown-icon {
            font-size: 1.1em;
        }

        .main-wrapper {
            margin-left: 325px;
            transition: margin-left 0.3s ease;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 80px);
            overflow: hidden;
        }
        
        .left-panel {
            width: 45%;
            flex-shrink: 0;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 20px;
            overflow-y: auto;
            box-shadow: 1px 0 0 rgba(0, 0, 0, 0.1);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
        }

        .left-panel::-webkit-scrollbar {
            width: 6px;
        }

        .left-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .right-panel {
            width: 55%;
            overflow-y: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .right-panel::-webkit-scrollbar {
            width: 8px;
        }

        .right-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .input-section {
            background: transparent;
            padding: 0;
            border-radius: 0;
            box-shadow: none;
            height: 100%;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }

        .input-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            font-size: 0.9em;
        }

        .input-group input, .input-group select {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .fusion-result-section {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
            margin-bottom: 20px;
        }

        .fusion-result-section h4 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 0.9em;
        }

        .fusion-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
            font-family: monospace;
            font-size: 12px;
            min-height: 100px;
            color: #666;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-upload-section {
            margin-top: 20px;
        }

        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
        }

        .file-upload-area:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .file-upload-area.dragover {
            border-color: #4caf50;
            background: #f1f8e9;
        }

        .file-upload-icon {
            font-size: 1.5em;
            margin-bottom: 6px;
            color: #667eea;
        }

        .file-upload-text {
            color: #2c3e50;
            font-size: 13px;
        }

        .file-upload-text strong {
            color: #667eea;
        }

        .file-upload-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 13px;
        }

        .file-upload-status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .file-upload-status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
            margin-top: 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .result-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            overflow: hidden;
            border: 1px solid #e1e8ed;
        }

        .result-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .result-card-content {
            padding: 25px;
        }

        .chart-container {
            margin-top: 20px;
            height: 500px;
            position: relative;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.5);
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        }

        .export-btn:active {
            transform: translateY(0);
        }

        /* 数据表格样式 */
        .data-table-container {
            width: 100%;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            position: relative;
        }

        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 11px;
            font-family: monospace;
            table-layout: fixed;
        }

        .data-table thead {
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table th {
            padding: 8px 6px;
            border: 1px solid #ddd;
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
            background: #f8f9fa;
            box-sizing: border-box;
        }

        .data-table td {
            padding: 0;
            border: 1px solid #ddd;
            text-align: center;
            box-sizing: border-box;
        }

        .data-table td input {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            text-align: center;
            font-size: 10px;
            font-family: monospace;
            padding: 4px 6px;
            box-sizing: border-box;
        }

        .data-table td input:focus {
            outline: 2px solid #667eea;
            background: #f0f8ff;
        }

        .data-table tbody {
            display: block;
            height: calc(300px - 34px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .data-table thead,
        .data-table tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            gap: 8px;
        }

        .table-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .table-btn:hover {
            background: #5a6fd8;
        }

        .table-btn.danger {
            background: #dc3545;
        }

        .table-btn.danger:hover {
            background: #c82333;
        }

        .data-input-section {
            margin-top: 20px;
        }

        .data-input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .data-input-group {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
        }

        .data-input-group h4 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            body {
                overflow: auto;
            }

            .header {
                position: relative;
                height: auto;
                padding: 15px;
            }

            .main-wrapper {
                margin-left: 0;
            }

            .main-content {
                flex-direction: column;
                height: auto;
                overflow: visible;
            }
            
            .left-panel {
                width: 100%;
                position: static;
                height: auto;
                background: rgba(248, 249, 250, 0.95);
                margin: 0;
                padding: 20px;
                box-shadow: none;
            }
            
            .right-panel {
                width: 100%;
                margin-left: 0;
                margin-top: 0;
                height: auto;
                padding: 20px;
            }

            .data-input-grid {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
            }

            .config-row {
                grid-template-columns: 1fr !important;
                gap: 10px !important;
            }
        }

        /* 左侧菜单栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 80px;
            bottom: 0;
            width: 325px;
            background: #2c3e50;
            z-index: 999;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .menu-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            position: relative;
        }

        .menu-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #3498db;
        }

        .menu-link.active {
            background: #3498db;
            color: white;
        }

        .menu-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #e74c3c;
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .menu-text {
            flex: 1;
        }

        .menu-arrow {
            font-size: 10px;
            color: #95a5a6;
            transition: transform 0.3s ease;
        }

        .menu-item.expanded .menu-arrow {
            transform: rotate(90deg);
        }

        .submenu {
            background: #34495e;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .menu-item.expanded .submenu {
            max-height: 200px;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 50px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 13px;
            border-left: 3px solid transparent;
        }

        .submenu-link:hover {
            background: rgba(255, 255, 255, 0.05);
            color: #3498db;
            border-left-color: #3498db;
        }

        .submenu-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left-color: #3498db;
        }

        /* 主内容区域调整 */
        .main-wrapper {
            margin-left: 325px;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
            transition: margin-left 0.3s ease;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-wrapper {
                margin-left: 0;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .model-cards {
                grid-template-columns: 1fr;
            }

            .upload-btn {
                width: 30%;
                min-width: 100px;
            }
        }

        /* 中等屏幕适配 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .model-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .upload-btn {
                width: 15%;
                min-width: 110px;
            }
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 10px;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* 模块内容样式 */
        .module-content {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            min-height: calc(100vh - 80px);
            padding: 20px;
        }

        .module-header {
            margin-bottom: 30px;
            text-align: center;
        }

        .module-header h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .module-header p {
            color: #666;
            font-size: 1.1em;
        }

        .model-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .model-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .model-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .model-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .model-status {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin-bottom: 15px;
        }

        .model-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85em;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        /* 流域划分样式 */
        .watershed-section {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .watershed-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .watershed-header h3 {
            color: #2c3e50;
            margin: 0;
            font-size: 1.4em;
            font-weight: 600;
        }

        .upload-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            font-size: 0.95em;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 10%;
            justify-content: center;
            min-width: 120px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
        }

        .upload-btn span {
            font-size: 1.1em;
        }

        .model-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .model-info {
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #007bff;
        }

        .model-info small {
            color: #6c757d;
            font-size: 0.85em;
        }

        .no-models-placeholder {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-style: italic;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px dashed #dee2e6;
        }

        .no-models-placeholder p {
            margin: 0;
            font-size: 1.1em;
        }

        .btn-success {
            background: #28a745;
            color: white;
            border: none;
        }

        .btn-success:hover {
            background: #218838;
        }

        .download-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .download-btn:disabled {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            opacity: 1;
            cursor: not-allowed;
        }

        .download-btn:disabled:hover {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            transform: none;
        }

        /* 模型上传弹框样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .form-text {
            margin-top: 5px;
            font-size: 0.85em;
        }

        .text-muted {
            color: #6c757d;
        }

        /* 融合配置弹窗样式 */
        .fusion-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .fusion-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fusion-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .fusion-modal-header h3 {
            margin: 0;
            font-size: 1.3em;
            font-weight: 600;
        }

        .fusion-modal-close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            transition: all 0.3s ease;
        }

        .fusion-modal-close:hover {
            color: #f0f0f0;
            transform: scale(1.1);
        }

        .fusion-modal-body {
            padding: 25px;
        }

        .fusion-modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 面包屑导航样式 */
        .breadcrumb {
            margin-bottom: 20px;
            padding: 10px 0;
            font-size: 14px;
            color: #666;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: #5a6fd8;
        }

        .breadcrumb .separator {
            margin: 0 8px;
            color: #999;
        }

        /* 标签页样式 */
        .tab-container {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #666;
            background: transparent;
        }

        .tab:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 可折叠部分样式 */
        .collapsible-section {
            margin-bottom: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }

        .collapsible-header {
            padding: 15px 20px;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border-bottom: 1px solid #e1e8ed;
        }

        .collapsible-header:hover {
            background: #e9ecef;
        }

        .collapsible-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .collapsible-toggle {
            font-size: 14px;
            color: #667eea;
            transition: transform 0.3s ease;
        }

        .collapsible-section.expanded .collapsible-toggle {
            transform: rotate(90deg);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.active {
            max-height: 1000px;
            padding: 20px;
        }

        /* 加载指示器样式 */
        .loading-indicator {
            text-align: center;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            margin: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #667eea;
            font-size: 16px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <button class="mobile-menu-toggle" onclick="toggleSidebar()">☰</button>
                    <h1>📊 流量预测系统</h1>
                    <p>Flow Prediction System</p>
                </div>
                <div class="nav-right">
                    <div class="user-section">
                        <div class="user-info" id="userInfo" style="display: none;">
                            <span class="user-welcome">欢迎，</span>
                            <span class="user-name" id="userName"></span>
                            <span class="user-role" id="userRole"></span>
                        </div>
                        <div class="dropdown" id="userDropdown" style="display: none;">
                            <button class="dropdown-btn" onclick="toggleDropdown()">
                                <span class="hamburger">☰</span>
                            </button>
                            <div class="dropdown-content" id="dropdownContent">
                                <a href="#" class="dropdown-item" onclick="showPersonalInfo()">
                                    <span class="dropdown-icon">👤</span>
                                    个人信息管理
                                </a>
                                <a href="#" class="dropdown-item" onclick="AuthManager.logout()">
                                    <span class="dropdown-icon">🚪</span>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 左侧菜单栏 -->
        <div class="sidebar" id="sidebar">
            <ul class="sidebar-menu">
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('fusion')">
                        <span class="menu-icon">🌧️</span>
                        <span class="menu-text">降雨产品融合</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link active" onclick="showModule('flow-prediction')">
                        <span class="menu-icon">📊</span>
                        <span class="menu-text">流量预测系统</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('watershed-info')">
                        <span class="menu-icon">🏞️</span>
                        <span class="menu-text">流域信息</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                        <span class="menu-icon">🔧</span>
                        <span class="menu-text">模型管理</span>
                        <span class="menu-arrow">▶</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="submenu-link" onclick="showModule('model-fusion')">
                            <span class="menu-text">多源融合模型</span>
                        </a>
                        <a href="#" class="submenu-link" onclick="showModule('model-flow')">
                            <span class="menu-text">流量预测模型</span>
                        </a>
                    </div>
                </li>
                <li class="menu-item admin-only" id="userManagementMenu" style="display: none;">
                    <a href="user-management.html" class="menu-link">
                        <span class="menu-icon">👥</span>
                        <span class="menu-text">用户管理</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-wrapper">
            <div class="main-content" id="mainContent">
                <!-- 左侧面板 - 数据输入配置 -->
                <div class="left-panel">
                <div class="input-section">
                    <h3 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">⚙️ 流域流量预测输入配置</h3>

                    <!-- 配置参数行 -->
                    <div class="config-row" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <!-- 流域选择 -->
                        <div class="input-group">
                            <label for="watershed">🏞️ 流域选择:</label>
                            <select id="watershed">
                                <option value="lianghe">两河流域</option>
                                <option value="yantang">沿塘流域</option>
                                <option value="yinhe">银河流域</option>
                            </select>
                        </div>

                        <!-- 历史窗口长度 -->
                        <div class="input-group">
                            <label for="historyWindow">📅 历史窗口长度 (小时):</label>
                            <select id="historyWindow">
                                <option value="168">168</option>
                                <option value="336">336</option>
                            </select>
                        </div>

                        <!-- 预测窗口长度 -->
                        <div class="input-group">
                            <label for="predictionWindow">🔮 预测窗口长度 (小时):</label>
                            <select id="predictionWindow">
                                <option value="6">6</option>
                                <option value="12" selected>12</option>
                                <option value="24">24</option>
                            </select>
                        </div>
                    </div>

                    <!-- 数据输入区域 -->
                    <div class="data-input-section">
                        <h4 style="margin-bottom: 15px; color: #2c3e50; font-size: 0.9em;">数据输入</h4>
                        <div class="data-input-grid">
                            <!-- 左侧：降水预报数据表格 -->
                            <div class="data-input-group">
                                <h4>🌧️ 降水预报数据（历史+未来）  单位：mm</h4>
                                <div class="data-table-container" style="height: 300px; border: 2px solid #e1e8ed; border-radius: 8px; overflow: hidden;">
                                    <table class="data-table" id="precipitationTable">
                                        <thead>
                                            <tr>
                                                <th style="width: 50%;">时间</th>
                                                <th style="width: 50%;">降水预报</th>
                                            </tr>
                                        </thead>
                                        <tbody id="precipitationTableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('precipitation')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('precipitation')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="precipitationCount">0 行</span>
                                </div>
                                <!-- 降水数据文件上传 -->
                                <div class="file-upload-container" style="margin-top: 10px;">
                                    <input type="file" id="precipitationFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handlePrecipitationFileUpload(event)">
                                    <div class="file-upload-area" onclick="document.getElementById('precipitationFileInput').click()" style="padding: 18px; font-size: 12px;">
                                        <div class="file-upload-text">
                                            <strong>请上传"历史+未来"时间点的降水预报</strong>
                                            <br>
                                            <small>支持 .xlsx, .xls 和 .csv 格式</small>
                                        </div>
                                    </div>
                                    <div id="precipitationFileUploadStatus" class="file-upload-status" style="display: none;"></div>
                                </div>
                            </div>

                            <!-- 右侧：流域流量数据表格 -->
                            <div class="data-input-group">
                                <h4>💧 流域流量数据（历史）  单位：m^3/s</h4>
                                <div class="data-table-container" style="height: 300px; border: 2px solid #e1e8ed; border-radius: 8px; overflow: hidden;">
                                    <table class="data-table" id="flowTable">
                                        <thead>
                                            <tr>
                                                <th style="width: 50%;">时间</th>
                                                <th style="width: 50%;">流量</th>
                                            </tr>
                                        </thead>
                                        <tbody id="flowTableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('flow')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('flow')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="flowCount">0 行</span>
                                </div>
                                <!-- 流量数据文件上传 -->
                                <div class="file-upload-container" style="margin-top: 10px;">
                                    <input type="file" id="flowFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFlowFileUpload(event)">
                                    <div class="file-upload-area" onclick="document.getElementById('flowFileInput').click()" style="padding: 16px; font-size: 18px;">
                                        <div class="file-upload-text">
                                            <strong>请上传"历史"时间点的流域流量</strong>
                                            <br>
                                            <small>支持 .xlsx, .xls 和 .csv 格式</small>
                                        </div>
                                    </div>
                                    <div id="flowFileUploadStatus" class="file-upload-status" style="display: none;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 组合数据上传区域 -->
                    <div class="combined-upload-section" style="margin-top: 20px; margin-bottom: 20px;">
                        <h4 style="margin-bottom: 15px; color: #2c3e50; font-size: 0.9em;">📁 组合数据上传</h4>
                        <div class="file-upload-container">
                            <input type="file" id="combinedFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleCombinedFileUpload(event)">
                            <div class="file-upload-area" onclick="document.getElementById('combinedFileInput').click()" style="padding: 18px; font-size: 12px;">
                                <div class="file-upload-text">
                                    <strong>请上传"历史+未来"的降水预报和"历史"的流域流量</strong>
                                    <br>
                                    <small>支持 .xlsx, .xls 和 .csv 格式</small>
                                    <br>
                                    <small style="color: #666; margin-top: 5px; display: block;">
                                        📋 格式要求：第一列为时间戳，第二列为降水预报，第三列为流量，起始时间戳需对齐
                                    </small>
                                </div>
                            </div>
                            <div id="combinedFileUploadStatus" class="file-upload-status" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- 预测按钮 -->
                    <button class="btn btn-primary" onclick="performFlowPrediction()">🚀 开始流量预测</button>
                    
                    <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 8px; font-size: 11px; color: #666;">
                        <strong>💡 使用说明:</strong> 
                        系统将根据选择的流域和窗口长度自动选择相应的权重进行流量预测推理
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 预测结果 -->
            <div class="right-panel">
                <!-- 欢迎信息 -->
                <div id="welcomeMessage" class="result-card">
                    <div class="result-card-header">📊 流量预测系统</div>
                    <div class="result-card-content">
                        <div style="text-align: center; padding: 40px 20px;">
                            <div style="font-size: 4em; margin-bottom: 20px;">🌊</div>
                            <h3 style="color: #2c3e50; margin-bottom: 15px;">开始您的流量预测</h3>
                            <p style="color: #666; margin-bottom: 25px; line-height: 1.6;">
                                请在左侧面板中配置预测参数并上传历史流量数据，然后开始流量预测。
                            </p>
                            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #2e7d32; margin-bottom: 8px;">🏞️ 流域选择</h4>
                                    <p style="font-size: 14px; color: #666;">选择两河、沿塘或银河流域</p>
                                </div>
                                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #1976d2; margin-bottom: 8px;">📊 智能预测</h4>
                                    <p style="font-size: 14px; color: #666;">基于融合产品的流量预测</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载指示器 -->
                <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在处理...</div>
                </div>

                <div class="results-section" id="resultsSection" style="display: none;">
                    <div class="breadcrumb">
                        <a href="#" onclick="showMainView()">🏠 主页</a>
                        <span class="separator">></span>
                        <span id="currentPage">预测结果</span>
                    </div>

                    <div class="tab-container">
                        <div class="tab active" onclick="switchTab('prediction')">🌊 流量预测</div>
                        <div class="tab" onclick="switchTab('detailed')">📋 详细数据</div>
                    </div>

                    <div id="predictionContent" class="tab-content active">
                        <div class="result-card">
                            <div class="result-card-header">
                                🌊 流量预测结果
                                <div style="float: right; display: flex; gap: 10px;">
                                    <button class="export-btn" onclick="showMainView()" title="重新预测" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                                        🔄 重新预测
                                    </button>
                                    <button class="export-btn" onclick="exportPredictionData('csv')" title="导出CSV格式">
                                        📄 CSV
                                    </button>
                                    <button class="export-btn" onclick="exportPredictionData('excel')" title="导出Excel格式">
                                        📊 Excel
                                    </button>
                                    <button class="export-btn" onclick="exportChartImage()" title="导出图表图片">
                                        🖼️ PNG
                                    </button>
                                </div>
                            </div>
                            <div class="result-card-content">
                                <div class="chart-container">
                                    <canvas id="predictionChart"></canvas>
                                </div>
                                <div id="predictionSummary" style="margin-top: 20px;"></div>
                            </div>
                        </div>
                    </div>



                    <div id="detailedContent" class="tab-content">
                        <div class="result-card">
                            <div class="result-card-header">📋 详细预测数据</div>
                            <div class="result-card-content">
                                <div id="detailedDataTable">
                                    <p style="color: #666; text-align: center; padding: 20px;">
                                        请先完成流量预测以查看详细数据
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 认证检查和用户界面初始化
        window.addEventListener('load', function() {
            // 检查用户是否已登录
            if (!AuthManager.requireLogin()) {
                return;
            }

            // 显示用户信息
            displayUserInfo();

            // 初始化权限控制
            initializePermissions();
        });

        function displayUserInfo() {
            const user = AuthManager.getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.username;
                document.getElementById('userRole').textContent = user.role === 'admin' ? '管理员' : '普通用户';
                document.getElementById('userInfo').style.display = 'flex';
                document.getElementById('userDropdown').style.display = 'block';

                // 如果是管理员，显示用户管理链接
                if (user.role === 'admin') {
                    const userManagementLink = document.getElementById('userManagementLink');
                    if (userManagementLink) {
                        userManagementLink.style.display = 'flex';
                    }

                    // 显示左侧菜单中的用户管理
                    document.getElementById('userManagementMenu').style.display = 'block';
                }
            }
        }

        function toggleDropdown() {
            const dropdown = document.getElementById('dropdownContent');
            dropdown.classList.toggle('show');
        }

        // 点击其他地方关闭下拉菜单
        window.addEventListener('click', function(event) {
            if (!event.target.matches('.dropdown-btn') && !event.target.matches('.hamburger')) {
                const dropdown = document.getElementById('dropdownContent');
                if (dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            }
        });

        function initializePermissions() {
            const user = AuthManager.getCurrentUser();
            if (!user) return;

            // 显示管理员菜单
            if (user.role === 'admin') {
                document.getElementById('userManagementMenu').style.display = 'block';
            }
        }

        // 左侧菜单栏功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        function toggleSubmenu(element) {
            const menuItem = element.parentElement;
            menuItem.classList.toggle('expanded');
        }

        function showModule(moduleName) {
            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-link, .submenu-link').forEach(link => {
                link.classList.remove('active');
            });

            // 根据模块名称显示相应内容
            switch(moduleName) {
                case 'fusion':
                    // 跳转到降雨产品融合系统
                    window.location.href = '11.html';
                    break;
                case 'flow-prediction':
                    // 显示流量预测系统（当前页面）
                    // 使用更可靠的方式查找和激活菜单项
                    const flowPredictionLink = document.querySelector('.menu-link[onclick*="flow-prediction"]');
                    if (flowPredictionLink) {
                        flowPredictionLink.classList.add('active');
                    }
                    // 先隐藏其他模块，再显示主内容
                    hideOtherModules();
                    document.getElementById('mainContent').style.display = 'flex';
                    break;
                case 'watershed-info':
                    // 跳转到流域信息页面
                    window.location.href = 'watershed-info.html';
                    break;
                case 'model-fusion':
                    const modelFusionLink = document.querySelector('.submenu-link[onclick*="model-fusion"]');
                    if (modelFusionLink) {
                        modelFusionLink.classList.add('active');
                    }
                    showModelFusionModule();
                    break;
                case 'model-flow':
                    const modelFlowLink = document.querySelector('.submenu-link[onclick*="model-flow"]');
                    if (modelFlowLink) {
                        modelFlowLink.classList.add('active');
                    }
                    showModelFlowModule();
                    break;
                case 'user-management':
                    // 跳转到用户管理页面
                    window.location.href = 'user-management.html';
                    break;
            }

            // 在移动端关闭侧边栏
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        }

        function showPersonalInfo() {
            // 显示个人信息管理弹窗
            showPersonalInfoModal();
        }

        // 显示多源融合模型管理模块
        function showModelFusionModule() {
            hideOtherModules();
            let moduleContent = document.getElementById('modelFusionModule');
            if (!moduleContent) {
                moduleContent = createModelFusionModule();
                document.querySelector('.main-wrapper').appendChild(moduleContent);
            }
            moduleContent.style.display = 'block';

            // 更新页面标题
            updatePageTitle('🔧 多源融合模型管理', 'Multi-Source Fusion Model Management');
        }

        // 显示流量预测模型管理模块
        function showModelFlowModule() {
            hideOtherModules();
            let moduleContent = document.getElementById('modelFlowModule');
            if (!moduleContent) {
                moduleContent = createModelFlowModule();
                document.querySelector('.main-wrapper').appendChild(moduleContent);
            }
            moduleContent.style.display = 'block';

            // 更新页面标题
            updatePageTitle('📊 流量预测模型管理', 'Flow Prediction Model Management');
        }

        // 更新页面标题
        function updatePageTitle(title, subtitle) {
            const headerTitle = document.querySelector('.nav-left h1');
            const headerSubtitle = document.querySelector('.nav-left p');
            if (headerTitle) headerTitle.textContent = title;
            if (headerSubtitle) headerSubtitle.textContent = subtitle;
        }

        // 恢复原始标题
        function restoreOriginalTitle() {
            updatePageTitle('📊 流量预测系统', 'Flow Prediction System');
        }

        function hideOtherModules() {
            // 隐藏其他模块的内容
            const otherModules = document.querySelectorAll('.module-content:not(#mainContent)');
            otherModules.forEach(module => {
                module.style.display = 'none';
            });
            document.getElementById('mainContent').style.display = 'none';
        }

        // 创建流量预测模型管理模块
        function createModelFlowModule() {
            const module = document.createElement('div');
            module.id = 'modelFlowModule';
            module.className = 'module-content';
            module.style.display = 'none';
            module.style.padding = '20px';
            module.style.background = 'rgba(255, 255, 255, 0.98)';
            module.style.backdropFilter = 'blur(20px)';
            module.style.minHeight = 'calc(100vh - 80px)';

            module.innerHTML = `
                <div class="module-header">
                    <h2>📊 流量预测模型管理</h2>
                </div>

                <!-- 两河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🏞️ 两河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('flow', '两河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="lianghe-flow-models">
                        <div class="model-card">
                            <h4>LSTM神经网络</h4>
                            <p>长短期记忆网络用于时间序列预测</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('flow', '两河', 'LSTM神经网络', '长短期记忆网络用于时间序列预测')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, 'LSTM神经网络')">🗑️ 删除</button>
                            </div>
                        </div>
                        <div class="model-card">
                            <h4>水文模型</h4>
                            <p>基于物理过程的流域水文模型</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('flow', '两河', '水文模型', '基于物理过程的流域水文模型')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '水文模型')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 沿塘流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🌊 沿塘流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('flow', '沿塘')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yantang-flow-models">
                        <div class="model-card">
                            <h4>集成学习模型</h4>
                            <p>多模型集成的流量预测方法</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('flow', '沿塘', '集成学习模型', '多模型集成的流量预测方法')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '集成学习模型')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 银河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>⭐ 银河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('flow', '银河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yinhe-flow-models">
                        <!-- 暂无模型 -->
                        <div class="no-models-placeholder">
                            <p>暂无模型，请点击上方"模型上传"按钮添加模型</p>
                        </div>
                    </div>
                </div>
            `;

            return module;
        }

        // 创建多源融合模型管理模块
        function createModelFusionModule() {
            const module = document.createElement('div');
            module.id = 'modelFusionModule';
            module.className = 'module-content';
            module.style.display = 'none';
            module.style.padding = '20px';
            module.style.background = 'rgba(255, 255, 255, 0.98)';
            module.style.backdropFilter = 'blur(20px)';
            module.style.minHeight = 'calc(100vh - 80px)';

            module.innerHTML = `
                <div class="module-header">
                    <h2>🔧 多源融合模型管理</h2>
                </div>

                <!-- 两河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🏞️ 两河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('fusion', '两河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="lianghe-fusion-models">
                        <div class="model-card">
                            <h4>贝叶斯模型平均 (BMA)</h4>
                            <p>基于贝叶斯理论的概率加权融合方法</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('fusion', '两河', '贝叶斯模型平均 (BMA)', '基于贝叶斯理论的概率加权融合方法')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '贝叶斯模型平均 (BMA)')">🗑️ 删除</button>
                            </div>
                        </div>
                        <div class="model-card">
                            <h4>随机森林回归</h4>
                            <p>使用随机森林算法进行非线性融合</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('fusion', '两河', '随机森林回归', '使用随机森林算法进行非线性融合')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '随机森林回归')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 沿塘流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🌊 沿塘流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('fusion', '沿塘')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yantang-fusion-models">
                        <div class="model-card">
                            <h4>多元线性回归</h4>
                            <p>传统的线性回归融合方法</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('fusion', '沿塘', '多元线性回归', '传统的线性回归融合方法')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '多元线性回归')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 银河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>⭐ 银河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('fusion', '银河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yinhe-fusion-models">
                        <div class="model-card">
                            <h4>深度神经网络</h4>
                            <p>多层感知机(MLP)深度学习融合方法</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('fusion', '银河', '深度神经网络', '多层感知机(MLP)深度学习融合方法')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '深度神经网络')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return module;
        }

        // 显示模型上传弹框
        function showModelUploadModal(modelType, watershed) {
            const modal = document.createElement('div');
            modal.className = 'fusion-modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="fusion-modal-content">
                    <div class="fusion-modal-header">
                        <h3>📤 模型上传</h3>
                        <span class="fusion-modal-close" onclick="closeModelUploadModal()">&times;</span>
                    </div>
                    <div class="fusion-modal-body">
                        <form id="modelUploadForm" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="watershedSelect">流域选择:</label>
                                <select id="watershedSelect" class="form-control" required>
                                    <option value="两河" ${watershed === '两河' ? 'selected' : ''}>两河流域</option>
                                    <option value="沿塘" ${watershed === '沿塘' ? 'selected' : ''}>沿塘流域</option>
                                    <option value="银河" ${watershed === '银河' ? 'selected' : ''}>银河流域</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="modelName">模型名称:</label>
                                <input type="text" id="modelName" class="form-control" placeholder="请输入模型名称" required>
                            </div>
                            <div class="form-group">
                                <label for="modelFile">模型文件 (可选):</label>
                                <input type="file" id="modelFile" class="form-control" accept=".pkl,.h5,.pt,.pth,.joblib,.model">
                                <small class="form-text text-muted">支持的文件格式: .pkl, .h5, .pt, .pth, .joblib, .model（可不上传文件，仅创建模型记录）</small>
                            </div>
                            <div class="form-group">
                                <label for="modelDescription">模型描述:</label>
                                <textarea id="modelDescription" class="form-control" rows="3" placeholder="请输入模型描述（可选）"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="fusion-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModelUploadModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="uploadModel('${modelType}')">上传模型</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 关闭模型上传弹框
        function closeModelUploadModal() {
            const modal = document.querySelector('.fusion-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 上传模型
        function uploadModel(modelType) {
            const form = document.getElementById('modelUploadForm');
            const formData = new FormData();

            const watershed = document.getElementById('watershedSelect').value;
            const modelName = document.getElementById('modelName').value;
            const modelFile = document.getElementById('modelFile').files[0];
            const modelDescription = document.getElementById('modelDescription').value;

            if (!modelName) {
                alert('请填写模型名称');
                return;
            }

            formData.append('watershed', watershed);
            formData.append('modelName', modelName);
            if (modelFile) {
                formData.append('modelFile', modelFile);
            }
            formData.append('modelDescription', modelDescription);
            formData.append('modelType', modelType);

            // 这里应该发送到后端API
            // 暂时模拟上传成功
            setTimeout(() => {
                const successMessage = modelFile ? '模型上传成功！' : '模型记录创建成功！';
                alert(successMessage);
                addModelCard(modelType, watershed, {
                    name: modelName,
                    description: modelDescription || '用户自定义模型',
                    fileName: modelFile ? modelFile.name : '无文件',
                    uploadTime: new Date().toLocaleString()
                });
                closeModelUploadModal();
            }, 1000);
        }

        // 添加模型卡片
        function addModelCard(modelType, watershed, modelInfo) {
            const watershedMap = {
                '两河': 'lianghe',
                '沿塘': 'yantang',
                '银河': 'yinhe'
            };

            const containerId = `${watershedMap[watershed]}-${modelType}-models`;
            const container = document.getElementById(containerId);

            if (!container) return;

            // 移除"暂无模型"占位符
            const placeholder = container.querySelector('.no-models-placeholder');
            if (placeholder) {
                placeholder.remove();
            }

            const modelCard = document.createElement('div');
            modelCard.className = 'model-card';

            const hasFile = modelInfo.fileName && modelInfo.fileName !== '无文件';
            const downloadButton = hasFile
                ? `<button class="btn btn-success download-btn" onclick="downloadModel('${modelInfo.fileName}')">📥 下载</button>`
                : `<button class="btn btn-success download-btn" onclick="downloadModel('无文件')" title="无文件可下载">📥 下载</button>`;

            // 卡片内容与预设模型保持一致
            modelCard.innerHTML = `
                <h4>${modelInfo.name}</h4>
                <p>${modelInfo.description}</p>
                <div class="model-status">状态: 已启用</div>
                <div class="model-actions">
                    <button class="btn btn-secondary" onclick="showModelDetailModal('${modelType}', '${watershed}', '${modelInfo.name}', '${modelInfo.description}', this)">查看详情</button>
                    ${downloadButton}
                    <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '${modelInfo.name}')">🗑️ 删除</button>
                </div>
            `;

            container.appendChild(modelCard);
        }

        // 下载模型
        function downloadModel(fileName) {
            if (fileName === '无文件') {
                alert('该模型暂无文件可下载');
                return;
            }
            // 这里应该调用后端API下载文件
            alert(`正在下载模型文件: ${fileName}`);
        }

        // 删除模型
        function deleteModel(button, modelName) {
            if (confirm(`确定要删除模型 "${modelName}" 吗？`)) {
                const modelCard = button.closest('.model-card');
                modelCard.remove();
                alert('模型删除成功！');
            }
        }

        // 显示模型详情弹框
        function showModelDetailModal(modelType, watershed, modelName, modelDescription, buttonElement = null) {
            const modal = document.createElement('div');
            modal.className = 'fusion-modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="fusion-modal-content">
                    <div class="fusion-modal-header">
                        <h3>📋 模型详情</h3>
                        <span class="fusion-modal-close" onclick="closeModelDetailModal()">&times;</span>
                    </div>
                    <div class="fusion-modal-body">
                        <form id="modelDetailForm">
                            <div class="form-group">
                                <label>模型类型:</label>
                                <input type="text" class="form-control" value="${modelType === 'fusion' ? '多源融合模型' : '流量预测模型'}" readonly style="background: #f5f5f5;">
                            </div>
                            <div class="form-group">
                                <label>所属流域:</label>
                                <input type="text" class="form-control" value="${watershed}流域" readonly style="background: #f5f5f5;">
                            </div>
                            <div class="form-group">
                                <label for="editModelName">模型名称:</label>
                                <input type="text" id="editModelName" class="form-control" value="${modelName}" required>
                            </div>
                            <div class="form-group">
                                <label for="editModelDescription">模型描述:</label>
                                <textarea id="editModelDescription" class="form-control" rows="4" required>${modelDescription}</textarea>
                            </div>
                            <div class="form-group">
                                <label>创建时间:</label>
                                <input type="text" class="form-control" value="${new Date().toLocaleString()}" readonly style="background: #f5f5f5;">
                            </div>
                            <div class="form-group">
                                <label>模型状态:</label>
                                <input type="text" class="form-control" value="已启用" readonly style="background: #f5f5f5;">
                            </div>
                        </form>
                    </div>
                    <div class="fusion-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModelDetailModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="updateModelInfo('${modelType}', '${watershed}', '${modelName}', ${buttonElement ? 'true' : 'false'})">保存修改</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // 存储按钮元素引用，用于更新
            modal.buttonElement = buttonElement;
        }

        // 关闭模型详情弹框
        function closeModelDetailModal() {
            const modal = document.querySelector('.fusion-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 更新模型信息
        function updateModelInfo(modelType, watershed, originalName, isUserUploaded) {
            const nameElement = document.getElementById('editModelName');
            const descElement = document.getElementById('editModelDescription');

            if (!nameElement || !descElement) {
                alert('无法找到表单元素，请重新打开详情窗口');
                return;
            }

            const newName = nameElement.value.trim();
            const newDescription = descElement.value.trim();

            if (!newName || !newDescription) {
                alert('请填写完整的模型名称和描述');
                return;
            }

            // 模拟保存成功
            setTimeout(() => {
                alert('模型信息更新成功！');

                // 如果是用户上传的模型，更新对应的卡片
                const modal = document.querySelector('.fusion-modal');
                if (modal && modal.buttonElement) {
                    const modelCard = modal.buttonElement.closest('.model-card');
                    if (modelCard) {
                        const titleElement = modelCard.querySelector('h4');
                        const descElement = modelCard.querySelector('p');
                        if (titleElement) titleElement.textContent = newName;
                        if (descElement) descElement.textContent = newDescription;

                        // 更新查看详情按钮的参数
                        const detailButton = modelCard.querySelector('.btn-secondary');
                        if (detailButton) {
                            detailButton.setAttribute('onclick', `showModelDetailModal('${modelType}', '${watershed}', '${newName}', '${newDescription}', this)`);
                        }
                    }
                } else {
                    // 如果是预设模型，更新页面上的对应卡片
                    updatePresetModelCard(modelType, watershed, originalName, newName, newDescription);
                }

                closeModelDetailModal();
            }, 500);
        }

        // 更新预设模型卡片
        function updatePresetModelCard(modelType, watershed, originalName, newName, newDescription) {
            // 根据模型类型和流域找到对应的容器
            const containerId = `${watershed}-${modelType}-models`;
            const container = document.getElementById(containerId);

            if (container) {
                const modelCards = container.querySelectorAll('.model-card');
                modelCards.forEach(card => {
                    const titleElement = card.querySelector('h4');
                    if (titleElement && titleElement.textContent.includes(originalName)) {
                        // 直接替换模型名称，不保留图标
                        titleElement.textContent = newName;
                        const descElement = card.querySelector('p');
                        if (descElement) descElement.textContent = newDescription;

                        // 更新查看详情按钮的参数
                        const detailButton = card.querySelector('.btn-secondary');
                        if (detailButton) {
                            detailButton.setAttribute('onclick', `showModelDetailModal('${modelType}', '${watershed}', '${newName}', '${newDescription}', this)`);
                        }
                    }
                });
            }
        }

        function showMainContent() {
            hideOtherModules();
            document.getElementById('mainContent').style.display = 'flex';
            // 重新激活流量预测菜单
            document.querySelectorAll('.menu-link, .submenu-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector('[onclick="showModule(\'flow-prediction\')"]').classList.add('active');
            // 恢复原始标题
            restoreOriginalTitle();
        }

        // 显示主视图（重置右侧面板到初始状态）
        function showMainView() {
            // 隐藏结果区域，显示欢迎信息
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('welcomeMessage').style.display = 'block';
        }

        // 标签页切换功能（与11.html一致）
        function switchTab(tabName) {
            // 移除所有标签的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 激活选中的标签和内容
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有事件对象，通过tabName查找对应的标签
                const tabElement = document.querySelector(`[onclick="switchTab('${tabName}')"]`);
                if (tabElement) {
                    tabElement.classList.add('active');
                }
            }
            document.getElementById(tabName + 'Content').classList.add('active');

            // 如果切换到详细数据标签页，生成详细数据表格
            if (tabName === 'detailed' && currentPredictionData && currentPredictionData.length > 0) {
                generateDetailedDataTable();
            }
        }

        // 可折叠部分切换（与11.html一致）
        function toggleCollapsible(element) {
            const section = element.parentElement;
            const content = section.querySelector('.collapsible-content');

            section.classList.toggle('expanded');
            content.classList.toggle('active');
        }

        // 显示加载状态（与11.html一致）
        function showLoading(message = '正在处理...') {
            const loadingDiv = document.getElementById('loadingIndicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'block';
                loadingDiv.querySelector('.loading-text').textContent = message;
            }
        }

        // 隐藏加载状态（与11.html一致）
        function hideLoading() {
            const loadingDiv = document.getElementById('loadingIndicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
        }

        // 显示错误信息（与11.html一致）
        function showError(message) {
            alert('错误: ' + message);
        }

        // 显示成功信息（与11.html一致）
        function showSuccess(message) {
            alert('成功: ' + message);
        }

        // 显示个人信息管理弹窗
        function showPersonalInfoModal() {
            const user = AuthManager.getCurrentUser();
            if (!user) return;

            const modal = document.createElement('div');
            modal.className = 'fusion-modal';
            modal.style.display = 'block';
            modal.style.position = 'fixed';
            modal.style.zIndex = '2000';
            modal.style.left = '0';
            modal.style.top = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            modal.style.backdropFilter = 'blur(5px)';

            modal.innerHTML = `
                <div style="background-color: white; margin: 5% auto; padding: 0; border-radius: 15px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 25px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                        <h3 style="margin: 0; font-size: 1.3em; font-weight: 600;">👤 个人信息管理</h3>
                        <span style="color: white; font-size: 28px; font-weight: bold; cursor: pointer; line-height: 1;" onclick="closePersonalInfoModal()">&times;</span>
                    </div>
                    <div style="padding: 25px;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">用户名</label>
                            <input type="text" value="${user.username}" readonly style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: #f5f5f5;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">当前角色</label>
                            <input type="text" value="${user.role === 'admin' ? '管理员' : '普通用户'}" readonly style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: #f5f5f5;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">新密码</label>
                            <input type="password" id="newPassword" placeholder="输入新密码（留空则不修改）" style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: white;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">确认新密码</label>
                            <input type="password" id="confirmPassword" placeholder="再次输入新密码" style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: white;">
                        </div>
                    </div>
                    <div style="padding: 20px 25px; border-top: 1px solid #eee; display: flex; justify-content: flex-end; gap: 12px;">
                        <button onclick="closePersonalInfoModal()" style="padding: 12px 24px; background: #f8f9fa; color: #6c757d; border: 1px solid #dee2e6; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer;">取消</button>
                        <button onclick="updatePersonalInfo()" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer;">保存修改</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';
        }

        function closePersonalInfoModal() {
            const modal = document.querySelector('.fusion-modal');
            if (modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
            }
        }

        function updatePersonalInfo() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword && newPassword !== confirmPassword) {
                alert('两次输入的密码不一致！');
                return;
            }

            if (newPassword && newPassword.length < 6) {
                alert('密码长度至少6位！');
                return;
            }

            if (newPassword) {
                const success = AuthManager.updatePassword(newPassword);
                if (success) {
                    alert('密码修改成功！');
                    closePersonalInfoModal();
                } else {
                    alert('密码修改失败！');
                }
            } else {
                alert('没有需要修改的信息！');
            }
        }

        let currentChart = null;
        let currentPredictionData = null;
        let currentHistoricalData = null;
        let currentWatershed = null;

        // 处理流量文件上传
        function handleFlowFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const statusDiv = document.getElementById('flowFileUploadStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'file-upload-status';
            statusDiv.innerHTML = '📤 正在读取流量数据文件...';

            const reader = new FileReader();

            if (file.name.endsWith('.csv')) {
                reader.onload = function(e) {
                    try {
                        parseCSVFlowData(e.target.result, file.name);
                    } catch (error) {
                        showFlowUploadError('CSV文件读取失败: ' + error.message);
                    }
                };
                reader.readAsText(file);
            } else {
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        parseExcelFlowData(jsonData, file.name);
                    } catch (error) {
                        showFlowUploadError('Excel文件读取失败: ' + error.message);
                    }
                };
                reader.readAsArrayBuffer(file);
            }
        }

        function parseCSVFlowData(csvText, fileName) {
            const lines = csvText.trim().split('\n');
            if (lines.length < 1) {
                throw new Error('CSV文件数据不足');
            }

            const flowData = [];
            // 始终忽略第一行
            let startRow = 1;

            for (let i = startRow; i < lines.length; i++) {
                const columns = lines[i].split(',');
                if (columns.length >= 2) {
                    const timeStr = columns[0].trim();
                    const flowValue = parseFloat(columns[1].trim());

                    if (!isNaN(flowValue)) {
                        flowData.push({
                            time: timeStr,
                            flow: flowValue
                        });
                    }
                }
            }

            if (flowData.length === 0) {
                throw new Error('未找到有效的流量数据');
            }

            // 填充流量表格
            fillFlowTable([['时间', '流量'], ...flowData.map(item => [item.time, item.flow])]);
            showFlowUploadSuccess(fileName, flowData.length);
        }

        function parseExcelFlowData(jsonData, fileName) {
            if (jsonData.length < 1) {
                throw new Error('Excel文件数据不足');
            }

            const flowData = [];
            // 始终忽略第一行
            let startRow = 1;

            for (let i = startRow; i < jsonData.length; i++) {
                const row = jsonData[i];
                if (row.length >= 2) {
                    const timeStr = row[0];
                    const flowValue = parseFloat(row[1]);

                    if (!isNaN(flowValue)) {
                        flowData.push({
                            time: timeStr,
                            flow: flowValue
                        });
                    }
                }
            }

            if (flowData.length === 0) {
                throw new Error('未找到有效的流量数据');
            }

            // 填充流量表格
            fillFlowTable([['时间', '流量'], ...flowData.map(item => [item.time, item.flow])]);
            showFlowUploadSuccess(fileName, flowData.length);
        }

        function showPrecipitationUploadSuccess(fileName, dataCount) {
            const statusDiv = document.getElementById('precipitationFileUploadStatus');
            statusDiv.className = 'file-upload-status success';
            statusDiv.innerHTML = `
                ✅ 降水预报数据上传成功！<br>
                📁 文件名: ${fileName}<br>
                📊 数据点数: ${dataCount} 个
            `;
        }

        function showPrecipitationUploadError(message) {
            const statusDiv = document.getElementById('precipitationFileUploadStatus');
            statusDiv.className = 'file-upload-status error';
            statusDiv.innerHTML = `❌ ${message}`;
        }

        function showFlowUploadSuccess(fileName, dataCount) {
            const statusDiv = document.getElementById('flowFileUploadStatus');
            statusDiv.className = 'file-upload-status success';
            statusDiv.innerHTML = `
                ✅ 流量数据上传成功！<br>
                📁 文件名: ${fileName}<br>
                📊 数据点数: ${dataCount} 个
            `;
        }

        function showFlowUploadError(message) {
            const statusDiv = document.getElementById('flowFileUploadStatus');
            statusDiv.className = 'file-upload-status error';
            statusDiv.innerHTML = `❌ ${message}`;
        }

        function showCombinedUploadSuccess(fileName, precipitationCount, flowCount) {
            const statusDiv = document.getElementById('combinedFileUploadStatus');
            statusDiv.className = 'file-upload-status success';
            statusDiv.innerHTML = `
                ✅ 组合数据上传成功！<br>
                📁 文件名: ${fileName}<br>
                🌧️ 降水预报数据: ${precipitationCount} 个<br>
                💧 流量数据: ${flowCount} 个
            `;
        }

        function showCombinedUploadError(message) {
            const statusDiv = document.getElementById('combinedFileUploadStatus');
            statusDiv.className = 'file-upload-status error';
            statusDiv.innerHTML = `❌ ${message}`;
        }

        // 生成表格行
        function generateTableRows() {
            const historyWindow = parseInt(document.getElementById('historyWindow').value);
            const predictionWindow = parseInt(document.getElementById('predictionWindow').value);

            // 生成降水预报表格（历史+预测）
            const precipitationTableBody = document.getElementById('precipitationTableBody');
            precipitationTableBody.innerHTML = '';
            const totalPrecipRows = historyWindow + predictionWindow;

            for (let i = 0; i < totalPrecipRows; i++) {
                const row = precipitationTableBody.insertRow();
                const timeCell = row.insertCell(0);
                const valueCell = row.insertCell(1);

                timeCell.innerHTML = `<input type="text" placeholder="YYYY-MM-DD HH:MM:SS" style="width: 100%;">`;
                valueCell.innerHTML = `<input type="number" placeholder="0.0" step="0.001" style="width: 100%;">`;
            }

            // 生成流量表格（仅历史）
            const flowTableBody = document.getElementById('flowTableBody');
            flowTableBody.innerHTML = '';

            for (let i = 0; i < historyWindow; i++) {
                const row = flowTableBody.insertRow();
                const timeCell = row.insertCell(0);
                const valueCell = row.insertCell(1);

                timeCell.innerHTML = `<input type="text" placeholder="YYYY-MM-DD HH:MM:SS" style="width: 100%;">`;
                valueCell.innerHTML = `<input type="number" placeholder="0.0" step="0.001" style="width: 100%;">`;
            }

            // 更新行数显示
            updateRowCount('precipitation');
            updateRowCount('flow');
        }

        // 处理降水预报文件上传
        function handlePrecipitationFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const statusDiv = document.getElementById('precipitationFileUploadStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'file-upload-status';
            statusDiv.innerHTML = '📤 正在处理文件...';

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    let data;
                    if (file.name.endsWith('.csv')) {
                        data = parseCSV(e.target.result);
                    } else {
                        const workbook = XLSX.read(e.target.result, { type: 'binary' });
                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];
                        data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    }

                    if (data.length < 2) {
                        throw new Error('文件数据不足');
                    }

                    // 填充降水预报表格
                    fillPrecipitationTable(data);
                    showPrecipitationUploadSuccess(file.name, data.length - 1);
                } catch (error) {
                    showPrecipitationUploadError(`文件解析失败: ${error.message}`);
                }
            };

            if (file.name.endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                reader.readAsBinaryString(file);
            }
        }

        // 处理组合文件上传
        function handleCombinedFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const statusDiv = document.getElementById('combinedFileUploadStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'file-upload-status';
            statusDiv.innerHTML = '📤 正在处理文件...';

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    let data;
                    if (file.name.endsWith('.csv')) {
                        data = parseCSV(e.target.result);
                    } else {
                        const workbook = XLSX.read(e.target.result, { type: 'binary' });
                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];
                        data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    }

                    if (data.length < 2) {
                        throw new Error('文件数据不足');
                    }

                    // 验证数据格式
                    if (data[0].length < 3) {
                        throw new Error('数据格式错误：需要至少3列（时间戳、降水预报、流量）');
                    }

                    // 分离降水预报和流量数据
                    const precipitationData = [];
                    const flowData = [];

                    for (let i = 1; i < data.length; i++) {
                        const row = data[i];
                        if (row[0] && (row[1] !== undefined && row[1] !== '')) {
                            // 有降水预报数据
                            precipitationData.push([row[0], row[1]]);
                        }
                        if (row[0] && (row[2] !== undefined && row[2] !== '')) {
                            // 有流量数据
                            flowData.push([row[0], row[2]]);
                        }
                    }

                    // 填充表格
                    if (precipitationData.length > 0) {
                        fillPrecipitationTable([['时间', '降水预报'], ...precipitationData]);
                    }

                    if (flowData.length > 0) {
                        fillFlowTable([['时间', '流量'], ...flowData]);
                    }

                    showCombinedUploadSuccess(file.name, precipitationData.length, flowData.length);
                } catch (error) {
                    showCombinedUploadError(`文件解析失败: ${error.message}`);
                }
            };

            if (file.name.endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                reader.readAsBinaryString(file);
            }
        }

        // 填充降水预报表格
        function fillPrecipitationTable(data) {
            const tableBody = document.getElementById('precipitationTableBody');

            // 清空现有数据
            tableBody.innerHTML = '';

            // 添加新数据
            for (let i = 1; i < data.length; i++) {
                const row = tableBody.insertRow();
                const timeCell = row.insertCell(0);
                const valueCell = row.insertCell(1);

                timeCell.innerHTML = `<input type="text" value="${data[i][0] || ''}" placeholder="YYYY-MM-DD HH:MM:SS" style="width: 100%;">`;
                valueCell.innerHTML = `<input type="number" value="${data[i][1] || ''}" placeholder="0.0" step="0.001" style="width: 100%;">`;
            }

            updateRowCount('precipitation');
        }

        // 填充流量表格
        function fillFlowTable(data) {
            const tableBody = document.getElementById('flowTableBody');

            // 清空现有数据
            tableBody.innerHTML = '';

            // 添加新数据
            for (let i = 1; i < data.length; i++) {
                const row = tableBody.insertRow();
                const timeCell = row.insertCell(0);
                const valueCell = row.insertCell(1);

                timeCell.innerHTML = `<input type="text" value="${data[i][0] || ''}" placeholder="YYYY-MM-DD HH:MM:SS" style="width: 100%;">`;
                valueCell.innerHTML = `<input type="number" value="${data[i][1] || ''}" placeholder="0.0" step="0.001" style="width: 100%;">`;
            }

            updateRowCount('flow');
        }

        // 从降水预报表格获取数据
        function getPrecipitationDataFromTable() {
            const tableBody = document.getElementById('precipitationTableBody');
            const rows = tableBody.querySelectorAll('tr');
            const data = [];

            for (let row of rows) {
                const timeInput = row.cells[0].querySelector('input');
                const valueInput = row.cells[1].querySelector('input');

                if (timeInput && valueInput && timeInput.value.trim() && valueInput.value.trim()) {
                    data.push({
                        time: timeInput.value.trim(),
                        value: parseFloat(valueInput.value)
                    });
                }
            }

            return data;
        }

        // 从流量表格获取数据
        function getFlowDataFromTable() {
            const tableBody = document.getElementById('flowTableBody');
            const rows = tableBody.querySelectorAll('tr');
            const data = [];

            for (let row of rows) {
                const timeInput = row.cells[0].querySelector('input');
                const valueInput = row.cells[1].querySelector('input');

                if (timeInput && valueInput && timeInput.value.trim() && valueInput.value.trim()) {
                    data.push({
                        time: timeInput.value.trim(),
                        flow: parseFloat(valueInput.value)
                    });
                }
            }

            return data;
        }

        // 解析CSV数据
        function parseCSV(csvText) {
            const lines = csvText.trim().split('\n');
            return lines.map(line => line.split(',').map(cell => cell.trim()));
        }

        // 显示上传成功信息
        function showUploadSuccess(statusId, message) {
            const statusDiv = document.getElementById(statusId);
            statusDiv.className = 'file-upload-status success';
            statusDiv.innerHTML = message;
        }

        // 显示上传错误信息
        function showUploadError(statusId, message) {
            const statusDiv = document.getElementById(statusId);
            statusDiv.className = 'file-upload-status error';
            statusDiv.innerHTML = `❌ ${message}`;
        }

        // 添加表格行
        function addTableRow(tableName, timestamp = '', value = '') {
            let tbody, placeholder;

            if (tableName === 'precipitation') {
                tbody = document.getElementById('precipitationTableBody');
                placeholder = 'YYYY-MM-DD HH:MM:SS';
            } else if (tableName === 'flow') {
                tbody = document.getElementById('flowTableBody');
                placeholder = 'YYYY-MM-DD HH:MM:SS';
            }

            if (!tbody) return;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td><input type="text" value="${timestamp}" placeholder="${placeholder}" style="width: 100%;"></td>
                <td><input type="number" value="${value}" placeholder="0.0" step="0.001" style="width: 100%;"></td>
            `;

            tbody.appendChild(row);
            updateRowCount(tableName);
        }

        // 清空表格
        function clearTable(tableName) {
            let tbody;

            if (tableName === 'precipitation') {
                tbody = document.getElementById('precipitationTableBody');
            } else if (tableName === 'flow') {
                tbody = document.getElementById('flowTableBody');
            }

            if (!tbody) return;

            tbody.innerHTML = '';
            updateRowCount(tableName);
        }

        // 更新行数显示
        function updateRowCount(tableName) {
            let tbody, countElement;

            if (tableName === 'precipitation') {
                tbody = document.getElementById('precipitationTableBody');
                countElement = document.getElementById('precipitationCount');
            } else if (tableName === 'flow') {
                tbody = document.getElementById('flowTableBody');
                countElement = document.getElementById('flowCount');
            }

            if (!tbody || !countElement) return;

            const count = tbody.querySelectorAll('tr').length;
            countElement.textContent = `${count} 行`;
        }

        // 页面初始化
        function initializePage() {
            // 生成初始表格
            generateTableRows();

            // 监听窗口长度变化
            document.getElementById('historyWindow').addEventListener('change', generateTableRows);
            document.getElementById('predictionWindow').addEventListener('change', generateTableRows);

            // 确保流量预测菜单项处于活动状态
            document.querySelectorAll('.menu-link, .submenu-link').forEach(link => {
                link.classList.remove('active');
            });
            const flowPredictionLink = document.querySelector('.menu-link[onclick*="flow-prediction"]');
            if (flowPredictionLink) {
                flowPredictionLink.classList.add('active');
            }

            // 确保主内容区域可见
            document.getElementById('mainContent').style.display = 'flex';
        }

        // 执行流量预测
        function performFlowPrediction() {
            // 验证降水预报数据
            const precipitationData = getPrecipitationDataFromTable();
            if (!precipitationData || precipitationData.length === 0) {
                alert('请先输入或上传降水预报数据');
                return;
            }

            // 验证流量数据
            const flowData = getFlowDataFromTable();
            if (!flowData || flowData.length === 0) {
                alert('请先输入或上传历史流量数据');
                return;
            }

            const watershed = document.getElementById('watershed').value;
            const historyWindow = parseInt(document.getElementById('historyWindow').value);
            const predictionWindow = parseInt(document.getElementById('predictionWindow').value);

            // 隐藏欢迎信息，显示加载状态
            document.getElementById('welcomeMessage').style.display = 'none';
            document.getElementById('loadingIndicator').style.display = 'block';

            // 模拟预测计算
            setTimeout(() => {
                generatePredictionResults(watershed, historyWindow, predictionWindow);

                // 在右侧面板显示结果，而不是切换整个页面
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'block';

                // 默认显示预测标签页
                switchTab('prediction');
            }, 1000);
        }

        function generatePredictionResults(watershed, historyWindow, predictionWindow) {
            // 获取表格数据
            const precipitationData = getPrecipitationDataFromTable();
            const flowData = getFlowDataFromTable();
            const predictedData = [];

            // 简单的预测模拟（实际应用中会使用训练好的模型）
            const lastFlow = flowData[flowData.length - 1].flow;
            const avgRainfall = precipitationData.reduce((sum, item) => sum + item.value, 0) / precipitationData.length;

            for (let i = 1; i <= predictionWindow; i++) {
                const baseFlow = lastFlow + (avgRainfall * 0.5 * Math.random());
                const noise = (Math.random() - 0.5) * 2;
                predictedData.push({
                    time: new Date(new Date(flowData[flowData.length - 1].time).getTime() + i * 3600000).toISOString().slice(0, 19).replace('T', ' '),
                    flow: Math.max(0, baseFlow + noise)
                });
            }

            // 保存数据供导出使用
            currentHistoricalData = flowData;
            currentPredictionData = predictedData;
            currentWatershed = watershed;

            // 创建图表
            createPredictionChart(flowData, predictedData);

            // 显示预测摘要
            showPredictionSummary(watershed, flowData, predictedData);

            // 生成详细数据表格（预先准备好，当用户切换到详细数据标签页时显示）
            generateDetailedDataTable();
        }

        // 生成详细数据表格
        function generateDetailedDataTable() {
            const detailedDataDiv = document.getElementById('detailedDataTable');
            const precipitationData = getPrecipitationDataFromTable();

            // 获取预测窗口长度
            const predictionWindow = parseInt(document.getElementById('predictionWindow').value);

            // 获取预测窗口对应的降水预报数据
            const predictionPrecipitation = precipitationData.slice(-predictionWindow);

            let tableHTML = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">📋 预测窗口详细数据</h4>
                    <p style="color: #666; font-size: 14px; margin-bottom: 15px;">
                        预测窗口长度：${predictionWindow} 小时
                    </p>
                </div>
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">时间</th>
                                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">降水预报 (mm)</th>
                                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">预测流量 (m³/s)</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // 添加预测数据行
            for (let i = 0; i < predictionWindow; i++) {
                const precipitation = predictionPrecipitation[i] ? predictionPrecipitation[i].value.toFixed(2) : '0.00';
                const flow = currentPredictionData[i] ? currentPredictionData[i].flow.toFixed(2) : '0.00';
                const time = currentPredictionData[i] ? currentPredictionData[i].time : '';

                tableHTML += `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${time}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${precipitation}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #f44336; font-weight: bold;">${flow}</td>
                    </tr>
                `;
            }

            tableHTML += `
                        </tbody>
                    </table>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 8px; font-size: 12px; color: #666;">
                    <strong>📝 说明：</strong>
                    预测流量数据用红色字体显示，表示这些是基于降水预报数据预测得到的流量值。
                </div>
            `;

            detailedDataDiv.innerHTML = tableHTML;
        }

        function createPredictionChart(historicalData, predictedData) {
            const ctx = document.getElementById('predictionChart').getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            // 获取降水预报数据
            const precipitationData = getPrecipitationDataFromTable();

            // 创建时间轴（历史+预测）
            const allTimes = [
                ...historicalData.map(d => d.time),
                ...predictedData.map(d => d.time)
            ];

            // 准备降水数据（对应历史+预测时间段）
            const precipitationValues = precipitationData.slice(0, allTimes.length).map(d => d.value);

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: allTimes,
                    datasets: [
                        {
                            label: '降水预报 (mm)',
                            data: precipitationValues,
                            borderColor: '#2196F3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y1'
                        },
                        {
                            label: '历史流量 (m³/s)',
                            data: [
                                ...historicalData.map(d => d.flow),
                                ...new Array(predictedData.length).fill(null)
                            ],
                            borderColor: '#4CAF50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: '预测流量 (m³/s)',
                            data: [
                                ...new Array(historicalData.length).fill(null),
                                ...predictedData.map(d => d.flow)
                            ],
                            borderColor: '#F44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '流量预测结果（降水预报 + 流量数据）',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '时间'
                            },
                            ticks: {
                                maxTicksLimit: 10
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '流量 (m³/s)'
                            },
                            beginAtZero: true
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '降水预报 (mm)'
                            },
                            reverse: true,  // 倒置y轴，使降水从上到下递增
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    elements: {
                        point: {
                            radius: 1,
                            pointStyle: 'circle',
                            backgroundColor: function(context) {
                                return context.dataset.borderColor;
                            },
                            borderWidth: 0
                        }
                    }
                }
            });
        }

        function showPredictionSummary(watershed, historicalData, predictedData) {
            const summaryDiv = document.getElementById('predictionSummary');

            const avgHistorical = historicalData.reduce((sum, d) => sum + d.flow, 0) / historicalData.length;
            const avgPredicted = predictedData.reduce((sum, d) => sum + d.flow, 0) / predictedData.length;
            const maxPredicted = Math.max(...predictedData.map(d => d.flow));
            const minPredicted = Math.min(...predictedData.map(d => d.flow));

            summaryDiv.innerHTML = `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #667eea;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 Prediction Summary</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <strong>Watershed:</strong> ${watershed}<br>
                            <strong>Historical Window:</strong> ${historicalData.length} hours<br>
                            <strong>Prediction Window:</strong> ${predictedData.length} hours<br>
                            <strong>Average Historical Flow:</strong> ${avgHistorical.toFixed(2)} m³/s
                        </div>
                        <div>
                            <strong>Average Predicted Flow:</strong> ${avgPredicted.toFixed(2)} m³/s<br>
                            <strong>Maximum Predicted Flow:</strong> ${maxPredicted.toFixed(2)} m³/s<br>
                            <strong>Minimum Predicted Flow:</strong> ${minPredicted.toFixed(2)} m³/s<br>
                            <strong>Flow Change:</strong> ${((avgPredicted - avgHistorical) / avgHistorical * 100).toFixed(1)}%
                        </div>
                    </div>
                </div>
            `;
        }

        // 导出预测数据为CSV格式
        function exportPredictionData(format) {
            if (!currentHistoricalData || !currentPredictionData) {
                alert('没有可导出的预测数据，请先执行流量预测');
                return;
            }

            if (format === 'csv') {
                exportToCSV();
            } else if (format === 'excel') {
                exportToExcel();
            }
        }

        function exportToCSV() {
            // 创建CSV内容
            let csvContent = 'Time,Data_Type,Flow_m3_s,Watershed,Notes\n';

            // 添加历史数据
            currentHistoricalData.forEach(item => {
                csvContent += `${item.time},Historical,${item.flow.toFixed(3)},${currentWatershed},Observed flow data\n`;
            });

            // 添加预测数据
            currentPredictionData.forEach(item => {
                csvContent += `${item.time},Predicted,${item.flow.toFixed(3)},${currentWatershed},Predicted flow data\n`;
            });

            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `flow_prediction_${currentWatershed}_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function exportToExcel() {
            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 准备数据
            const data = [
                ['Time', 'Data Type', 'Flow (m³/s)', 'Watershed', 'Notes']
            ];

            // 添加历史数据
            currentHistoricalData.forEach(item => {
                data.push([item.time, 'Historical', parseFloat(item.flow.toFixed(3)), currentWatershed, 'Observed flow data']);
            });

            // 添加预测数据
            currentPredictionData.forEach(item => {
                data.push([item.time, 'Predicted', parseFloat(item.flow.toFixed(3)), currentWatershed, 'Predicted flow data']);
            });

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(data);

            // 设置列宽
            ws['!cols'] = [
                { wch: 20 }, // Time
                { wch: 12 }, // Data Type
                { wch: 15 }, // Flow
                { wch: 12 }, // Watershed
                { wch: 25 }  // Notes
            ];

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, 'Flow Prediction');

            // 创建摘要工作表
            const summaryData = [
                ['Flow Prediction Summary'],
                [''],
                ['Parameter', 'Value'],
                ['Watershed', currentWatershed],
                ['Historical Window (hours)', currentHistoricalData.length],
                ['Prediction Window (hours)', currentPredictionData.length],
                ['Average Historical Flow (m³/s)', (currentHistoricalData.reduce((sum, d) => sum + d.flow, 0) / currentHistoricalData.length).toFixed(3)],
                ['Average Predicted Flow (m³/s)', (currentPredictionData.reduce((sum, d) => sum + d.flow, 0) / currentPredictionData.length).toFixed(3)],
                ['Maximum Predicted Flow (m³/s)', Math.max(...currentPredictionData.map(d => d.flow)).toFixed(3)],
                ['Minimum Predicted Flow (m³/s)', Math.min(...currentPredictionData.map(d => d.flow)).toFixed(3)],
                ['Export Date', new Date().toISOString().slice(0, 19).replace('T', ' ')]
            ];

            const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
            summaryWs['!cols'] = [{ wch: 30 }, { wch: 20 }];
            XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

            // 下载文件
            XLSX.writeFile(wb, `flow_prediction_${currentWatershed}_${new Date().toISOString().slice(0, 10)}.xlsx`);
        }

        // 导出图表为图片
        function exportChartImage() {
            if (!currentChart) {
                alert('没有可导出的图表，请先执行流量预测');
                return;
            }

            // 创建下载链接
            const link = document.createElement('a');
            link.download = `flow_prediction_chart_${currentWatershed}_${new Date().toISOString().slice(0, 10)}.png`;
            link.href = currentChart.toBase64Image();
            link.click();
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        // 键盘事件监听
        document.addEventListener('keydown', function(e) {
            // ESC键关闭弹窗或返回主页
            if (e.key === 'Escape') {
                const modal = document.querySelector('.fusion-modal');
                if (modal) {
                    closePersonalInfoModal();
                } else {
                    showMainContent();
                }
            }
        });

        // 点击弹窗外部关闭弹窗
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('fusion-modal')) {
                closePersonalInfoModal();
            }
        });
    </script>
</body>
</html>
