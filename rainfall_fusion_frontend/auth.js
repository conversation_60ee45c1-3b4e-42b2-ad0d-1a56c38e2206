/**
 * 用户认证和权限管理系统
 * Multi-Source Rainfall Product Fusion System - Authentication Module
 */

class AuthManager {
    static SESSION_KEY = 'rainfall_fusion_session';
    static USERS_KEY = 'rainfall_fusion_users';
    static SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24小时

    // 初始化默认用户
    static initializeDefaultUsers() {
        const existingUsers = this.getUsers();
        if (existingUsers.length === 0) {
            const defaultUsers = [
                {
                    id: 1,
                    username: 'admin',
                    password: '123456',
                    role: 'admin',
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                },
                {
                    id: 2,
                    username: 'user',
                    password: '123456',
                    role: 'user',
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                }
            ];
            localStorage.setItem(this.USERS_KEY, JSON.stringify(defaultUsers));
        }
    }

    // 获取所有用户
    static getUsers() {
        const users = localStorage.getItem(this.USERS_KEY);
        return users ? JSON.parse(users) : [];
    }

    // 保存用户数据
    static saveUsers(users) {
        localStorage.setItem(this.USERS_KEY, JSON.stringify(users));
    }

    // 用户登录
    static async login(username, password) {
        return new Promise((resolve) => {
            // 模拟网络延迟
            setTimeout(() => {
                const users = this.getUsers();
                const user = users.find(u => u.username === username && u.password === password);
                
                if (user) {
                    // 更新最后登录时间
                    user.lastLogin = new Date().toISOString();
                    this.saveUsers(users);
                    
                    // 创建会话
                    const session = {
                        userId: user.id,
                        username: user.username,
                        role: user.role,
                        loginTime: new Date().toISOString(),
                        expiresAt: new Date(Date.now() + this.SESSION_TIMEOUT).toISOString()
                    };
                    
                    localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
                    
                    resolve({
                        success: true,
                        message: '登录成功',
                        user: {
                            username: user.username,
                            role: user.role,
                            displayName: user.displayName
                        }
                    });
                } else {
                    resolve({
                        success: false,
                        message: '用户名或密码错误'
                    });
                }
            }, 500);
        });
    }

    // 用户登出
    static logout() {
        localStorage.removeItem(this.SESSION_KEY);
        window.location.href = 'index.html';
    }

    // 检查是否已登录
    static isLoggedIn() {
        const session = this.getCurrentSession();
        return session && new Date() < new Date(session.expiresAt);
    }

    // 获取当前会话
    static getCurrentSession() {
        const session = localStorage.getItem(this.SESSION_KEY);
        return session ? JSON.parse(session) : null;
    }

    // 获取当前用户信息
    static getCurrentUser() {
        const session = this.getCurrentSession();
        if (session && this.isLoggedIn()) {
            return {
                userId: session.userId,
                username: session.username,
                role: session.role,
                loginTime: session.loginTime
            };
        }
        return null;
    }

    // 检查用户权限
    static hasPermission(requiredRole) {
        const user = this.getCurrentUser();
        if (!user) return false;
        
        if (requiredRole === 'admin') {
            return user.role === 'admin';
        } else if (requiredRole === 'user') {
            return user.role === 'admin' || user.role === 'user';
        }
        
        return false;
    }

    // 要求登录
    static requireLogin() {
        if (!this.isLoggedIn()) {
            window.location.href = 'index.html';
            return false;
        }
        return true;
    }

    // 要求管理员权限
    static requireAdmin() {
        if (!this.requireLogin()) return false;
        
        if (!this.hasPermission('admin')) {
            alert('您没有权限访问此功能');
            return false;
        }
        return true;
    }

    // 添加新用户（仅管理员）
    static addUser(userData) {
        if (!this.hasPermission('admin')) {
            return { success: false, message: '权限不足' };
        }

        const users = this.getUsers();
        
        // 检查用户名是否已存在
        if (users.find(u => u.username === userData.username)) {
            return { success: false, message: '用户名已存在' };
        }

        // 验证密码强度
        if (!this.validatePassword(userData.password)) {
            return { success: false, message: '密码强度不足，至少6位字符' };
        }

        const newUser = {
            id: Math.max(...users.map(u => u.id), 0) + 1,
            username: userData.username,
            password: userData.password,
            role: userData.role || 'user',
            createdAt: new Date().toISOString(),
            lastLogin: null
        };

        users.push(newUser);
        this.saveUsers(users);

        return { success: true, message: '用户添加成功', user: newUser };
    }

    // 更新用户信息
    static updateUser(userId, userData) {
        if (!this.hasPermission('admin')) {
            return { success: false, message: '权限不足' };
        }

        const users = this.getUsers();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex === -1) {
            return { success: false, message: '用户不存在' };
        }

        // 检查用户名是否与其他用户冲突
        const existingUser = users.find(u => u.username === userData.username && u.id !== userId);
        if (existingUser) {
            return { success: false, message: '用户名已存在' };
        }

        // 如果更新密码，验证密码强度
        if (userData.password && !this.validatePassword(userData.password)) {
            return { success: false, message: '密码强度不足，至少6位字符' };
        }

        // 更新用户信息
        const updatedUser = { ...users[userIndex] };
        if (userData.username) updatedUser.username = userData.username;
        if (userData.password) updatedUser.password = userData.password;
        if (userData.role) updatedUser.role = userData.role;
        updatedUser.updatedAt = new Date().toISOString();

        users[userIndex] = updatedUser;
        this.saveUsers(users);

        return { success: true, message: '用户更新成功', user: updatedUser };
    }

    // 删除用户
    static deleteUser(userId) {
        if (!this.hasPermission('admin')) {
            return { success: false, message: '权限不足' };
        }

        const currentUser = this.getCurrentUser();
        if (currentUser && currentUser.userId === userId) {
            return { success: false, message: '不能删除当前登录用户' };
        }

        const users = this.getUsers();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex === -1) {
            return { success: false, message: '用户不存在' };
        }

        users.splice(userIndex, 1);
        this.saveUsers(users);

        return { success: true, message: '用户删除成功' };
    }

    // 验证密码强度
    static validatePassword(password) {
        return password && password.length >= 6;
    }

    // 检查会话是否即将过期
    static checkSessionExpiry() {
        const session = this.getCurrentSession();
        if (session) {
            const expiresAt = new Date(session.expiresAt);
            const now = new Date();
            const timeLeft = expiresAt - now;
            
            // 如果会话已过期
            if (timeLeft <= 0) {
                this.logout();
                return false;
            }
            
            // 如果会话即将在30分钟内过期，显示警告
            if (timeLeft <= 30 * 60 * 1000) {
                console.warn('会话即将过期，请及时保存工作');
            }
        }
        return true;
    }

    // 延长会话
    static extendSession() {
        const session = this.getCurrentSession();
        if (session) {
            session.expiresAt = new Date(Date.now() + this.SESSION_TIMEOUT).toISOString();
            localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
        }
    }

    // 获取所有用户（用于用户管理）
    static getAllUsers() {
        return this.getUsers();
    }

    // 更新用户密码
    static updatePassword(newPassword) {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser) return false;

            const users = this.getUsers();
            const userIndex = users.findIndex(u => u.username === currentUser.username);

            if (userIndex !== -1) {
                users[userIndex].password = newPassword;
                this.saveUsers(users);

                // 更新当前会话中的用户信息
                const session = this.getSession();
                if (session) {
                    session.user.password = newPassword;
                    localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
                }

                return true;
            }
            return false;
        } catch (error) {
            console.error('更新密码失败:', error);
            return false;
        }
    }

    // 添加新用户（管理员功能）
    static addUser(username, password, role = 'user') {
        try {
            const users = this.getUsers();

            // 检查用户名是否已存在
            if (users.find(u => u.username === username)) {
                return { success: false, message: '用户名已存在' };
            }

            const newUser = {
                id: Math.max(...users.map(u => u.id), 0) + 1,
                username,
                password,
                role,
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            users.push(newUser);
            this.saveUsers(users);

            return { success: true, message: '用户添加成功' };
        } catch (error) {
            console.error('添加用户失败:', error);
            return { success: false, message: '添加用户失败' };
        }
    }

    // 删除用户（管理员功能）
    static deleteUser(username) {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser || currentUser.role !== 'admin') {
                return { success: false, message: '权限不足' };
            }

            if (username === currentUser.username) {
                return { success: false, message: '不能删除当前登录用户' };
            }

            const users = this.getUsers();
            const filteredUsers = users.filter(u => u.username !== username);

            if (filteredUsers.length === users.length) {
                return { success: false, message: '用户不存在' };
            }

            this.saveUsers(filteredUsers);
            return { success: true, message: '用户删除成功' };
        } catch (error) {
            console.error('删除用户失败:', error);
            return { success: false, message: '删除用户失败' };
        }
    }

    // 编辑用户信息（管理员功能）
    static editUser(username, updates) {
        try {
            const currentUser = this.getCurrentUser();
            if (!currentUser || currentUser.role !== 'admin') {
                return { success: false, message: '权限不足' };
            }

            const users = this.getUsers();
            const userIndex = users.findIndex(u => u.username === username);

            if (userIndex === -1) {
                return { success: false, message: '用户不存在' };
            }

            // 更新用户信息
            Object.assign(users[userIndex], updates);
            this.saveUsers(users);

            return { success: true, message: '用户信息更新成功' };
        } catch (error) {
            console.error('编辑用户失败:', error);
            return { success: false, message: '编辑用户失败' };
        }
    }
}

// 初始化认证系统
AuthManager.initializeDefaultUsers();

// 定期检查会话状态
setInterval(() => {
    AuthManager.checkSessionExpiry();
}, 60000); // 每分钟检查一次

// 页面活动时延长会话
document.addEventListener('click', () => {
    if (AuthManager.isLoggedIn()) {
        AuthManager.extendSession();
    }
});

document.addEventListener('keypress', () => {
    if (AuthManager.isLoggedIn()) {
        AuthManager.extendSession();
    }
});

// 导出到全局作用域
window.AuthManager = AuthManager;
