<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于多源降水融合的流域洪水预报系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .main-container {
            display: flex;
            height: 100vh;
            width: 100vw;
        }

        .welcome-container {
            background: rgba(77, 56, 56, 0.85);
            padding: 60px 50px;
            flex: 1;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow-y: auto;
            position: relative;
        }

        .welcome-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            /* 使用background.jpg作为背景图片 */
            background-image: url('background.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.8;
            z-index: -1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.98);
            padding: 40px 50px 60px 50px;
            width: 450px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            padding-top: 80px;
            box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
        }

        .welcome-icon {
            font-size: 5em;
            margin-bottom: 30px;
            color: #667eea;
        }

        .welcome-title {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 30px;
            line-height: 1.6;
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-item {
            background: #f8f9fa;
            padding: 25px 20px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }

        .feature-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .feature-desc {
            color: #666;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h2 {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 0.9em;
        }

        .login-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #667eea;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
            font-size: 0.9em;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
            font-size: 1.1em;
            user-select: none;
        }

        .password-toggle:hover {
            color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message, .success-message {
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }



        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
        }

        @media (max-width: 1024px) {
            .main-container {
                flex-direction: column;
                height: auto;
                min-height: 100vh;
            }

            .welcome-container {
                padding: 40px 30px;
            }

            .login-container {
                width: 100%;
                padding: 40px 30px;
            }

            .features-grid {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .welcome-container, .login-container {
                padding: 30px 20px;
            }

            .welcome-title {
                font-size: 1.8em;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .feature-item {
                padding: 20px 15px;
            }

            .feature-icon {
                font-size: 2em;
            }

            .feature-title {
                font-size: 1em;
            }

            .feature-desc {
                font-size: 0.85em;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="welcome-container">
            <h1 class="welcome-title">基于多源降水融合的流域洪水预报系统</h1>
            <p class="welcome-subtitle">Multi-Source Precipitation Fusion Based Watershed Flood Forecasting System<br>专业的降水数据融合与洪水预报平台</p>

            <div class="features-grid">
                <!-- 第一行：三个功能 -->
                <div class="feature-item">
                    <div class="feature-icon">👥</div>
                    <div class="feature-title">用户管理</div>
                    <div class="feature-desc">完善的用户认证和权限管理</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">统计分析</div>
                    <div class="feature-desc">多源降水数据统计分析</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">模型管理</div>
                    <div class="feature-desc">预报模型配置和参数管理</div>
                </div>

                <!-- 第二行：三个功能 -->
                <div class="feature-item">
                    <div class="feature-icon">🔄</div>
                    <div class="feature-title">降雨数据融合</div>
                    <div class="feature-desc">智能融合多个预报产品</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🌊</div>
                    <div class="feature-title">流域洪水预测</div>
                    <div class="feature-desc">基于融合数据的洪水预报</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📈</div>
                    <div class="feature-title">精度评估</div>
                    <div class="feature-desc">预报精度评估和误差分析</div>
                </div>
            </div>
        </div>

        <div class="login-container">
            <div class="login-header">
                <div class="login-icon">🔑</div>
                <h2>系统登录</h2>
                <p>请输入您的账户信息</p>
            </div>

            <div id="errorMessage" class="error-message"></div>
            <div id="successMessage" class="success-message"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="password">密码</label>
                    <div class="password-container">
                        <input type="password" id="password" name="password" required autocomplete="current-password">
                        <span class="password-toggle" onclick="togglePassword()">👁️</span>
                    </div>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">登录</button>
                <div class="loading" id="loading">正在验证...</div>
            </form>
        </div>
    </div>
    
    <div class="footer">
        <p>&copy; 2024 多源降雨产品融合系统 | 专业版</p>
    </div>

    <script src="auth.js"></script>
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.querySelector('.password-toggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleIcon.textContent = '👁️';
            }
        }

        function showMessage(message, type = 'error') {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            if (type === 'error') {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                successDiv.style.display = 'none';
            } else {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                errorDiv.style.display = 'none';
            }

            setTimeout(() => {
                errorDiv.style.display = 'none';
                successDiv.style.display = 'none';
            }, 5000);
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');

            if (show) {
                loading.style.display = 'block';
                loginBtn.disabled = true;
            } else {
                loading.style.display = 'none';
                loginBtn.disabled = false;
            }
        }



        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showMessage('请输入用户名和密码');
                return;
            }

            showLoading(true);

            try {
                const result = await AuthManager.login(username, password);
                if (result.success) {
                    showMessage('登录成功，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '11.html';
                    }, 1000);
                } else {
                    showMessage(result.message);
                }
            } catch (error) {
                showMessage('登录过程中发生错误，请重试');
            } finally {
                showLoading(false);
            }
        });

        // 检查是否已登录，如果已登录则直接跳转到主系统
        window.addEventListener('load', function() {
            if (AuthManager.isLoggedIn()) {
                showMessage('检测到已登录，正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = '11.html';
                }, 1000);
            }
        });
    </script>
</body>
</html>
