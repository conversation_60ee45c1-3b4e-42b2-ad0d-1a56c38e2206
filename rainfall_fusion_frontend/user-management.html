<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 多源降雨产品融合系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f7;
            height: 100vh;
            padding: 0;
            overflow: hidden;
        }

        .container {
            width: 100%;
            height: 100vh;
            margin: 0;
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 30px;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            gap: 40px;
        }

        .nav-left {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-left h1 {
            font-size: 1.8em;
            color: white;
            margin-bottom: 5px;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-left p {
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1em;
            color: white;
        }

        .user-welcome {
            opacity: 0.8;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.7;
            font-size: 1.0em;
        }

        .user-role::before {
            content: "(";
        }

        .user-role::after {
            content: ")";
        }

        .dropdown {
            position: relative;
        }

        .dropdown-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.2em;
        }

        .dropdown-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .hamburger {
            display: block;
            font-size: 1.1em;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 8px;
            background: white;
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            z-index: 1000;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .dropdown-content.show {
            display: block;
            animation: dropdownFadeIn 0.3s ease;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9em;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #667eea;
        }

        .dropdown-icon {
            font-size: 1.1em;
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 10px;
        }

        /* 左侧菜单栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 80px;
            bottom: 0;
            width: 325px;
            background: #2c3e50;
            z-index: 999;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .menu-item {
            position: relative;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-link:hover {
            background: rgba(255, 255, 255, 0.05);
            color: #3498db;
            border-left-color: #3498db;
        }

        .menu-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left-color: #3498db;
        }

        .menu-icon {
            font-size: 18px;
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .menu-text {
            font-size: 14px;
            font-weight: 500;
        }

        .menu-arrow {
            margin-left: auto;
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .menu-item.expanded .menu-arrow {
            transform: rotate(90deg);
        }

        .submenu {
            background: #34495e;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .menu-item.expanded .submenu {
            max-height: 200px;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 50px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 13px;
            border-left: 3px solid transparent;
        }

        .submenu-link:hover {
            background: rgba(255, 255, 255, 0.05);
            color: #3498db;
            border-left-color: #3498db;
        }

        .submenu-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left-color: #3498db;
        }

        /* 主内容区域调整 */
        .main-wrapper {
            margin-left: 325px;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
            transition: margin-left 0.3s ease;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-wrapper {
                margin-left: 0;
            }

            .mobile-menu-toggle {
                display: block;
            }
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .main-content {
            padding: 30px;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            margin: 20px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 140px);
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .page-title {
            color: #2c3e50;
            font-size: 1.8em;
            font-weight: 600;
        }

        .user-table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-table th,
        .user-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e1e8ed;
        }

        .user-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .user-table tbody tr:hover {
            background: #f8f9fa;
        }

        .role-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-admin {
            background: #e3f2fd;
            color: #1976d2;
        }

        .role-user {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn-edit {
            background: #ffc107;
            color: #212529;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .modal-title {
            color: #2c3e50;
            font-size: 1.3em;
            font-weight: 600;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .message {
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .message.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .message.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state-icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .main-content {
                margin: 10px;
                padding: 20px;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .user-table {
                font-size: 14px;
            }

            .user-table th,
            .user-table td {
                padding: 10px 8px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .modal-content {
                margin: 10% auto;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <button class="mobile-menu-toggle" onclick="toggleSidebar()">☰</button>
                    <h1>👥 用户管理</h1>
                    <p>User Management System</p>
                </div>
                <div class="nav-right">
                    <div class="user-section">
                        <div class="user-info" id="userInfo" style="display: none;">
                            <span class="user-welcome">欢迎，</span>
                            <span class="user-name" id="userName"></span>
                            <span class="user-role" id="userRole"></span>
                        </div>
                        <div class="dropdown" id="userDropdown" style="display: none;">
                            <button class="dropdown-btn" onclick="toggleDropdown()">
                                <span class="hamburger">☰</span>
                            </button>
                            <div class="dropdown-content" id="dropdownContent">
                                <a href="#" class="dropdown-item" onclick="showPersonalInfo()">
                                    <span class="dropdown-icon">👤</span>
                                    个人信息管理
                                </a>
                                <a href="#" class="dropdown-item" onclick="AuthManager.logout()">
                                    <span class="dropdown-icon">🚪</span>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 左侧菜单栏 -->
        <div class="sidebar" id="sidebar">
            <ul class="sidebar-menu">
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('fusion')">
                        <span class="menu-icon">🌧️</span>
                        <span class="menu-text">降雨产品融合</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('flow-prediction')">
                        <span class="menu-icon">📊</span>
                        <span class="menu-text">流量预测系统</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('watershed-info')">
                        <span class="menu-icon">🏞️</span>
                        <span class="menu-text">流域信息</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                        <span class="menu-icon">🔧</span>
                        <span class="menu-text">模型管理</span>
                        <span class="menu-arrow">▶</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="submenu-link" onclick="showModule('model-fusion')">
                            <span class="menu-text">多源融合模型</span>
                        </a>
                        <a href="#" class="submenu-link" onclick="showModule('model-flow')">
                            <span class="menu-text">流量预测模型</span>
                        </a>
                    </div>
                </li>
                <li class="menu-item admin-only" id="userManagementMenu">
                    <a href="#" class="menu-link active" onclick="showModule('user-management')">
                        <span class="menu-icon">👥</span>
                        <span class="menu-text">用户管理</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-wrapper">
            <div class="main-content">
            <div class="page-header">
                <h2 class="page-title">用户管理</h2>
                <button class="btn btn-primary" onclick="showAddUserModal()">➕ 添加用户</button>
            </div>

            <div id="messageContainer"></div>

            <div class="user-table-container">
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>角色</th>
                            <th>创建时间</th>
                            <th>最后登录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <!-- 用户数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑用户模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">添加用户</h3>
                <span class="close" onclick="closeUserModal()">&times;</span>
            </div>
            <form id="userForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <label for="role">角色</label>
                    <select id="role" name="role" required>
                        <option value="user">普通用户</option>
                        <option value="admin">管理员</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeUserModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        let editingUserId = null;

        // 页面加载时检查权限
        window.addEventListener('load', function() {
            if (!AuthManager.requireAdmin()) {
                return;
            }

            displayUserInfo();
            loadUsers();
        });

        function displayUserInfo() {
            const user = AuthManager.getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.username;
                document.getElementById('userRole').textContent = user.role === 'admin' ? '管理员' : '普通用户';
                document.getElementById('userInfo').style.display = 'flex';
                document.getElementById('userDropdown').style.display = 'block';

                // 如果是管理员，显示用户管理功能
                if (user.role === 'admin') {
                    document.getElementById('userManagementMenu').style.display = 'block';
                }
            }
        }

        function toggleDropdown() {
            const dropdown = document.getElementById('dropdownContent');
            dropdown.classList.toggle('show');
        }

        // 点击其他地方关闭下拉菜单
        window.addEventListener('click', function(event) {
            if (!event.target.matches('.dropdown-btn') && !event.target.matches('.hamburger')) {
                const dropdown = document.getElementById('dropdownContent');
                if (dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            }
        });

        // 左侧菜单栏功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        function toggleSubmenu(element) {
            const menuItem = element.parentElement;
            menuItem.classList.toggle('expanded');
        }

        function showModule(moduleName) {
            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-link, .submenu-link').forEach(link => {
                link.classList.remove('active');
            });

            // 根据模块名称显示相应内容
            switch(moduleName) {
                case 'fusion':
                    // 跳转到降雨产品融合系统
                    window.location.href = '11.html';
                    break;
                case 'flow-prediction':
                    // 跳转到流量预测系统
                    window.location.href = 'flow_prediction.html';
                    break;
                case 'watershed-info':
                    // 跳转到流域信息页面
                    window.location.href = 'watershed-info.html';
                    break;
                case 'model-fusion':
                case 'model-flow':
                    // 跳转到相应的模型管理页面
                    window.location.href = '11.html';
                    break;
                case 'user-management':
                    // 当前页面，保持active状态
                    document.querySelector('[onclick="showModule(\'user-management\')"]').classList.add('active');
                    break;
            }

            // 在移动端关闭侧边栏
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        }

        function showPersonalInfo() {
            // 显示个人信息管理弹窗
            showPersonalInfoModal();
        }

        function loadUsers() {
            const users = AuthManager.getUsers();
            const tbody = document.getElementById('userTableBody');

            if (users.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="empty-state">
                            <div class="empty-state-icon">👤</div>
                            <p>暂无用户数据</p>
                        </td>
                    </tr>
                `;
                return;
            }

            // 按角色排序：管理员在前，普通用户在后
            const sortedUsers = users.sort((a, b) => {
                if (a.role === 'admin' && b.role !== 'admin') return -1;
                if (a.role !== 'admin' && b.role === 'admin') return 1;
                return 0; // 同角色保持原顺序
            });

            tbody.innerHTML = sortedUsers.map(user => `
                <tr>
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td><span class="role-badge role-${user.role}">${user.role === 'admin' ? '管理员' : '普通用户'}</span></td>
                    <td>${formatDate(user.createdAt)}</td>
                    <td>${user.lastLogin ? formatDate(user.lastLogin) : '从未登录'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-small btn-edit" onclick="editUser(${user.id})">编辑</button>
                            <button class="btn btn-small btn-delete" onclick="deleteUser(${user.id})">删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function showMessage(message, type = 'success') {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="message ${type}">${message}</div>`;
            
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function showAddUserModal() {
            editingUserId = null;
            document.getElementById('modalTitle').textContent = '添加用户';
            document.getElementById('userForm').reset();
            document.getElementById('userModal').style.display = 'block';
        }

        function editUser(userId) {
            const users = AuthManager.getUsers();
            const user = users.find(u => u.id === userId);

            if (user) {
                editingUserId = userId;
                document.getElementById('modalTitle').textContent = '编辑用户';
                document.getElementById('username').value = user.username;
                document.getElementById('password').value = '';
                document.getElementById('password').placeholder = '留空则不修改密码';
                document.getElementById('role').value = user.role;
                document.getElementById('userModal').style.display = 'block';
            }
        }

        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可恢复。')) {
                const result = AuthManager.deleteUser(userId);
                if (result.success) {
                    showMessage(result.message, 'success');
                    loadUsers();
                } else {
                    showMessage(result.message, 'error');
                }
            }
        }

        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
            document.getElementById('password').placeholder = '';
        }

        // 表单提交处理
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const userData = {
                username: formData.get('username'),
                password: formData.get('password'),
                role: formData.get('role')
            };
            
            let result;
            if (editingUserId) {
                // 编辑用户时，如果密码为空则不更新密码
                if (!userData.password) {
                    delete userData.password;
                }
                result = AuthManager.updateUser(editingUserId, userData);
            } else {
                result = AuthManager.addUser(userData);
            }
            
            if (result.success) {
                showMessage(result.message, 'success');
                closeUserModal();
                loadUsers();
            } else {
                showMessage(result.message, 'error');
            }
        });

        // 显示个人信息管理弹窗
        function showPersonalInfoModal() {
            const user = AuthManager.getCurrentUser();
            if (!user) return;

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.style.position = 'fixed';
            modal.style.zIndex = '2000';
            modal.style.left = '0';
            modal.style.top = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            modal.style.backdropFilter = 'blur(5px)';

            modal.innerHTML = `
                <div style="background-color: white; margin: 5% auto; padding: 0; border-radius: 15px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 25px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                        <h3 style="margin: 0; font-size: 1.3em; font-weight: 600;">👤 个人信息管理</h3>
                        <span style="color: white; font-size: 28px; font-weight: bold; cursor: pointer; line-height: 1;" onclick="closePersonalInfoModal()">&times;</span>
                    </div>
                    <div style="padding: 25px;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">用户名</label>
                            <input type="text" value="${user.username}" readonly style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: #f5f5f5;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">当前角色</label>
                            <input type="text" value="${user.role === 'admin' ? '管理员' : '普通用户'}" readonly style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: #f5f5f5;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">新密码</label>
                            <input type="password" id="newPassword" placeholder="输入新密码（留空则不修改）" style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: white;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; font-size: 0.95em;">确认新密码</label>
                            <input type="password" id="confirmPassword" placeholder="再次输入新密码" style="width: 100%; padding: 12px 15px; border: 2px solid #e1e8ed; border-radius: 10px; font-size: 14px; background: white;">
                        </div>
                    </div>
                    <div style="padding: 20px 25px; border-top: 1px solid #eee; display: flex; justify-content: flex-end; gap: 12px;">
                        <button onclick="closePersonalInfoModal()" style="padding: 12px 24px; background: #f8f9fa; color: #6c757d; border: 1px solid #dee2e6; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer;">取消</button>
                        <button onclick="updatePersonalInfo()" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer;">保存修改</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';
        }

        function closePersonalInfoModal() {
            const modal = document.querySelector('.modal[style*="z-index: 2000"]');
            if (modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
            }
        }

        function updatePersonalInfo() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword && newPassword !== confirmPassword) {
                alert('两次输入的密码不一致！');
                return;
            }

            if (newPassword && newPassword.length < 6) {
                alert('密码长度至少6位！');
                return;
            }

            if (newPassword) {
                const success = AuthManager.updatePassword(newPassword);
                if (success) {
                    alert('密码修改成功！');
                    closePersonalInfoModal();
                } else {
                    alert('密码修改失败！');
                }
            } else {
                alert('没有需要修改的信息！');
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('userModal');
            if (event.target === modal) {
                closeUserModal();
            }

            // 关闭个人信息模态框
            if (event.target.style && event.target.style.zIndex === '2000') {
                closePersonalInfoModal();
            }
        }
    </script>
</body>
</html>
