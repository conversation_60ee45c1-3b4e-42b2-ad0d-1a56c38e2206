<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多源降雨产品融合系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="auth.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            padding: 0;
            overflow: hidden;
        }        .container {
            width: 100%;
            height: 100vh;
            margin: 0;
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
        }        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px 30px;
            flex-shrink: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 80px;
            display: flex;
            align-items: center;
        }

        .nav-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-left h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-left p {
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9em;
            border: 2px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .nav-link.admin-only {
            background: rgba(255, 193, 7, 0.2);
            border-color: rgba(255, 193, 7, 0.3);
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1em;
            color: white;
        }

        .user-welcome {
            opacity: 0.8;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.7;
            font-size: 1.0em;
        }

        .user-role::before {
            content: "(";
        }

        .user-role::after {
            content: ")";
        }

        .logout-btn {
            padding: 8px 16px;
            background: rgba(220, 53, 69, 0.2);
            color: white;
            border: 2px solid rgba(220, 53, 69, 0.3);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9em;
        }

        .logout-btn:hover {
            background: rgba(220, 53, 69, 0.3);
            transform: translateY(-2px);
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.2em;
        }

        .dropdown-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .hamburger {
            display: block;
            font-size: 1.1em;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 8px;
            background: white;
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            z-index: 1000;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .dropdown-content.show {
            display: block;
            animation: dropdownFadeIn 0.3s ease;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9em;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #667eea;
        }

        .dropdown-icon {
            font-size: 1.1em;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 80px);
            overflow: hidden;
        }

        .left-panel {
            width: 45%;
            flex-shrink: 0;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 20px;
            overflow-y: auto;
            box-shadow: 1px 0 0 rgba(0, 0, 0, 0.1);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
        }

        .left-panel::-webkit-scrollbar {
            width: 6px;
        }

        .left-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }        .right-panel {
            width: 55%;
            overflow-y: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .right-panel::-webkit-scrollbar {
            width: 8px;
        }

        .right-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .right-panel::-webkit-scrollbar {
            width: 8px;
        }

        .right-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }        .input-section {
            background: transparent;
            padding: 0;
            border-radius: 0;
            box-shadow: none;
            height: 100%;
        }.input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            font-size: 0.9em;
        }

        .input-group input, .input-group select {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .data-input-section {
            margin-top: 20px;
        }        .data-input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .data-input-group {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
        }

        .data-input-group h4 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 0.9em;
        }        .data-input-group textarea {
            width: 100%;
            height: 120px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            resize: vertical;
        }

        /* 数据表格样式 */
        .data-table-container {
            width: 100%;
            height: 200px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            position: relative;
        }

        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 11px;
            font-family: monospace;
            table-layout: fixed;
        }

        .data-table thead {
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table th {
            padding: 8px 6px;
            border: 1px solid #ddd;
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
            background: #f8f9fa;
            box-sizing: border-box;
        }

        .data-table td {
            padding: 0;
            border: 1px solid #ddd;
            text-align: center;
            box-sizing: border-box;
        }

        .data-table td input {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            text-align: center;
            font-size: 10px;
            font-family: monospace;
            padding: 4px 6px;
            box-sizing: border-box;
        }

        .data-table td input:focus {
            outline: 2px solid #667eea;
            background: #f0f8ff;
        }

        .data-table tbody {
            display: block;
            height: calc(200px - 34px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .data-table thead,
        .data-table tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .data-table .time-col {
            width: calc(65% - 8px);
        }

        .data-table .value-col {
            width: calc(35% - 8px);
        }

        /* 滚动条样式 */
        .data-table tbody::-webkit-scrollbar {
            width: 8px;
        }

        .data-table tbody::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .data-table tbody::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .data-table tbody::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            gap: 8px;
        }

        .table-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .table-btn:hover {
            background: #5a6fd8;
        }

        .table-btn.danger {
            background: #dc3545;
        }

        .table-btn.danger:hover {
            background: #c82333;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        /* 文件上传样式 */
        .file-upload-section {
            margin-top: 20px;
        }

        .file-upload-container {
            margin-bottom: 10px;
        }

        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
        }

        .file-upload-area:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .file-upload-area.dragover {
            border-color: #4caf50;
            background: #f1f8e9;
        }

        .file-upload-icon {
            font-size: 1.5em;
            margin-bottom: 6px;
            color: #667eea;
        }

        .file-upload-text {
            color: #2c3e50;
            font-size: 13px;
        }

        .file-upload-text strong {
            color: #667eea;
        }

        .file-upload-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 13px;
        }

        .file-upload-status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .file-upload-status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .file-format-hint {
            margin-top: 10px;
            padding: 8px 12px;
            background: #f0f8ff;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }

        /* 统计分析图表样式 */
        .stats-charts-container {
            margin-top: 20px;
        }

        .stats-chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .stats-chart-item {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .stats-chart-item h5 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .stats-chart-wrapper {
            height: 250px;
            position: relative;
        }        .button-group {
            display: flex;
            flex-direction: row;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            flex: 1;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #8b4513;
            box-shadow: 0 5px 15px rgba(252, 182, 159, 0.4);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.6);
        }        .results-section {
            margin-top: 0;
        }

        .result-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            overflow: hidden;
            border: 1px solid #e1e8ed;
        }

        .result-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .result-card-content {
            padding: 25px;
        }

        .stats-table, .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 14px;
        }

        .stats-table th, .stats-table td,
        .metrics-table th, .metrics-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }

        .stats-table th, .metrics-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .stats-table tbody tr:nth-child(even),
        .metrics-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .stats-table tbody tr:hover,
        .metrics-table tbody tr:hover {
            background: #e3f2fd;
            transition: background-color 0.3s ease;
        }        .chart-container {
            margin-top: 20px;
            height: 500px;
            position: relative;
        }

        .fusion-result {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #4caf50;
        }

        .fusion-result h4 {
            color: #2e7d32;
            margin-bottom: 15px;
        }

        .fusion-data {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 8px;
            font-family: monospace;
            font-size: 12px;
        }

        .fusion-data span {
            background: white;
            padding: 5px;
            border-radius: 4px;
            text-align: center;
        }

        /* 融合配置弹窗样式 */
        .fusion-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .fusion-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
            max-height: 90vh;
            overflow-y: auto;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fusion-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .fusion-modal-header h3 {
            margin: 0;
            font-size: 1.3em;
            font-weight: 600;
        }

        .fusion-modal-close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            transition: all 0.3s ease;
        }

        .fusion-modal-close:hover {
            color: #f0f0f0;
            transform: scale(1.1);
        }

        .fusion-modal-body {
            padding: 25px;
        }

        .fusion-form-group {
            margin-bottom: 20px;
        }

        .fusion-form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95em;
        }

        .fusion-form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fusion-form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .fusion-method-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            margin-top: 10px;
        }

        .fusion-method-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .fusion-method-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .fusion-method-option.selected {
            border-color: #667eea;
            background: #f0f4ff;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .fusion-method-radio {
            margin-right: 12px;
            transform: scale(1.2);
        }

        .fusion-method-info {
            flex: 1;
        }

        .fusion-method-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .fusion-method-desc {
            font-size: 0.85em;
            color: #666;
            line-height: 1.4;
        }

        .fusion-modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .fusion-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fusion-btn-cancel {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .fusion-btn-cancel:hover {
            background: #e9ecef;
        }

        .fusion-btn-confirm {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .fusion-btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        /* 左侧菜单栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 80px;
            bottom: 0;
            width: 325px;
            background: #2c3e50;
            z-index: 999;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .menu-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            position: relative;
        }

        .menu-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #3498db;
        }

        .menu-link.active {
            background: #3498db;
            color: white;
        }

        .menu-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #e74c3c;
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .menu-text {
            flex: 1;
        }

        .menu-arrow {
            font-size: 10px;
            color: #95a5a6;
            transition: transform 0.3s ease;
        }

        .menu-item.expanded .menu-arrow {
            transform: rotate(90deg);
        }

        .submenu {
            background: #34495e;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .menu-item.expanded .submenu {
            max-height: 200px;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 50px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 13px;
            border-left: 3px solid transparent;
        }

        .submenu-link:hover {
            background: rgba(255, 255, 255, 0.05);
            color: #3498db;
            border-left-color: #3498db;
        }

        .submenu-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left-color: #3498db;
        }

        /* 主内容区域调整 */
        .main-wrapper {
            margin-left: 325px;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
            transition: margin-left 0.3s ease;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-wrapper {
                margin-left: 0;
            }

            .mobile-menu-toggle {
                display: block;
            }
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 10px;
        }

        /* 模块内容样式 */
        .module-content {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            min-height: calc(100vh - 80px);
            padding: 20px;
        }

        .module-header {
            margin-bottom: 30px;
            text-align: center;
        }

        .module-header h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .module-header p {
            color: #666;
            font-size: 1.1em;
        }

        .model-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .model-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .model-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .model-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .model-status {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin-bottom: 15px;
        }

        .model-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85em;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        /* 流域划分样式 */
        .watershed-section {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .watershed-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .watershed-header h3 {
            color: #2c3e50;
            margin: 0;
            font-size: 1.4em;
            font-weight: 600;
        }

        .upload-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            font-size: 0.95em;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 10%;
            justify-content: center;
            min-width: 120px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
        }

        .upload-btn span {
            font-size: 1.1em;
        }

        .model-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .model-info {
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #007bff;
        }

        .model-info small {
            color: #6c757d;
            font-size: 0.85em;
        }

        .no-models-placeholder {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-style: italic;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px dashed #dee2e6;
        }

        .no-models-placeholder p {
            margin: 0;
            font-size: 1.1em;
        }

        .btn-success {
            background: #28a745;
            color: white;
            border: none;
        }

        .btn-success:hover {
            background: #218838;
        }

        .download-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .download-btn:disabled {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            opacity: 1;
            cursor: not-allowed;
        }

        .download-btn:disabled:hover {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            transform: none;
        }

        /* 模型上传弹框样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .form-text {
            margin-top: 5px;
            font-size: 0.85em;
        }

        .text-muted {
            color: #6c757d;
        }

        /* 用户管理样式 */
        .user-management-actions {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
        }

        .user-table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-table th,
        .user-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .user-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .user-table tr:hover {
            background: #f8f9fa;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #f44336;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tab-container {
            display: flex;
            margin-bottom: 20px;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: #f8f9fa;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .tab:first-child {
            border-radius: 10px 0 0 10px;
        }

        .tab:last-child {
            border-radius: 0 10px 10px 0;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            display: none;
        }        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 10px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .pagination-btn:hover {
            background: #f0f0f0;
            border-color: #999;
        }

        .pagination-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .pagination-btn:disabled {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
            border-color: #e0e0e0;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
            margin: 0 15px;
        }        /* 表格容器样式 */
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 展开/折叠样式 */
        .collapsible-section {
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .collapsible-header {
            background: #f8f9fa;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .collapsible-header:hover {
            background: #e9ecef;
        }

        .collapsible-header h4 {
            margin: 0;
            color: #2c3e50;
        }

        .collapsible-toggle {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            transition: transform 0.3s ease;
        }

        .collapsible-content {
            padding: 20px;
            display: none;
        }

        .collapsible-content.active {
            display: block;
        }

        .collapsible-section.expanded .collapsible-toggle {
            transform: rotate(180deg);
        }

        /* 数据预览样式 */
        .data-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }

        .data-preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .data-preview-stats {
            font-size: 12px;
            color: #666;
        }

        .view-full-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s ease;
        }

        .view-full-btn:hover {
            background: #5a6fd8;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: #f8f9fa;
            padding: 10px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }        .breadcrumb .separator {
            margin: 0 8px;
            color: #666;
        }        /* 回到顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 40px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }        /* 页面指示器 */
        .page-indicator {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .page-indicator {
                bottom: 100px;
                top: auto;
                right: 20px;
                transform: none;
                flex-direction: row;
                gap: 8px;
            }
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .indicator-dot.active {
            background: #667eea;
            transform: scale(1.2);
            border-color: white;
        }        .indicator-dot:hover {
            background: #667eea;
            transform: scale(1.1);
        }

        /* 加载指示器 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 16px;
            color: #333;
            font-weight: 600;
        }        @media (max-width: 768px) {
            body {
                overflow: auto;
            }
            
            .header {
                position: relative;
                height: auto;
                padding: 15px;
            }
            
            .main-wrapper {
                margin-left: 0;
            }

            .main-content {
                flex-direction: column;
                height: auto;
                overflow: visible;
            }

            .left-panel {
                width: 100%;
                position: static;
                height: auto;
                background: rgba(248, 249, 250, 0.95);
                margin: 0;
                padding: 20px;
                box-shadow: none;
            }

            .model-cards {
                grid-template-columns: 1fr;
            }

            .upload-btn {
                width: 30%;
                min-width: 100px;
            }
            
            .right-panel {
                width: 100%;
                margin-left: 0;
                margin-top: 0;
                height: auto;
                padding: 20px;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
            }
            
            .data-input-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .tab-container {
                flex-direction: column;
            }
            
            .tab {
                border-radius: 0 !important;
            }
            
            .tab:first-child {
                border-radius: 10px 10px 0 0 !important;
            }
            
            .tab:last-child {
                border-radius: 0 0 10px 10px !important;
            }

            .stats-chart-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        /* 中等屏幕适配 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .model-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .upload-btn {
                width: 15%;
                min-width: 110px;
            }
        }

        @media (max-width: 1200px) and (min-width: 769px) {
            .left-panel {
                width: 45%;
            }
            
            .data-input-group textarea {
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <button class="mobile-menu-toggle" onclick="toggleSidebar()">☰</button>
                    <h1>🌧️ 多源降雨产品融合系统</h1>
                    <p>Multi-Source Rainfall Product Fusion System</p>
                </div>
                <div class="nav-right">
                    <div class="user-section">
                        <div class="user-info" id="userInfo" style="display: none;">
                            <span class="user-welcome">欢迎，</span>
                            <span class="user-name" id="userName"></span>
                            <span class="user-role" id="userRole"></span>
                        </div>
                        <div class="dropdown" id="userDropdown" style="display: none;">
                            <button class="dropdown-btn" onclick="toggleDropdown()">
                                <span class="hamburger">☰</span>
                            </button>
                            <div class="dropdown-content" id="dropdownContent">
                                <a href="#" class="dropdown-item" onclick="showPersonalInfo()">
                                    <span class="dropdown-icon">👤</span>
                                    个人信息管理
                                </a>
                                <a href="#" class="dropdown-item" onclick="AuthManager.logout()">
                                    <span class="dropdown-icon">🚪</span>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 左侧菜单栏 -->
        <div class="sidebar" id="sidebar">
            <ul class="sidebar-menu">
                <li class="menu-item">
                    <a href="#" class="menu-link active" onclick="showModule('fusion')">
                        <span class="menu-icon">🌧️</span>
                        <span class="menu-text">降雨产品融合</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('flow-prediction')">
                        <span class="menu-icon">📊</span>
                        <span class="menu-text">流量预测系统</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('watershed-info')">
                        <span class="menu-icon">🏞️</span>
                        <span class="menu-text">流域信息</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                        <span class="menu-icon">🔧</span>
                        <span class="menu-text">模型管理</span>
                        <span class="menu-arrow">▶</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="submenu-link" onclick="showModule('model-fusion')">
                            <span class="menu-text">多源融合模型</span>
                        </a>
                        <a href="#" class="submenu-link" onclick="showModule('model-flow')">
                            <span class="menu-text">流量预测模型</span>
                        </a>
                    </div>
                </li>
                <li class="menu-item admin-only" id="userManagementMenu" style="display: none;">
                    <a href="user-management.html" class="menu-link">
                        <span class="menu-icon">👥</span>
                        <span class="menu-text">用户管理</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-wrapper">
            <div class="main-content" id="mainContent">
                <!-- 左侧面板 - 数据输入配置 -->
                <div class="left-panel">
                <div class="input-section" id="input-section">
                    <h3 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">📊 数据输入配置</h3>

                    <div class="input-grid" style="grid-template-columns: 1fr 1fr 1fr 1fr;">
                        <!-- 流域选择 -->
                        <div class="input-group">
                            <label for="basinSelect" style="font-weight: bold; color: #2c3e50;">
                                🏞️ 选择流域:
                            </label>
                            <select id="basinSelect" style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px; background: white; cursor: pointer;" onchange="onBasinChange()">
                                <option value="">请选择流域...</option>
                                <option value="lianghe">两河 (Lianghe)</option>
                                <option value="yantang">沿塘 (Yantang)</option>
                                <option value="yinhe">银河 (Yinhe)</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label for="startTime">起始时间:</label>
                            <input type="datetime-local" id="startTime" value="2024-01-01T00:00">
                        </div>
                        <div class="input-group">
                            <label for="endTime">结束时间:</label>
                            <input type="datetime-local" id="endTime" value="2024-01-01T23:00">
                        </div>
                        <div class="input-group">
                            <label for="frequency">预报频率:</label>
                            <select id="frequency">
                                <option value="1">1小时</option>
                                <option value="3">3小时</option>
                                <option value="6">6小时</option>
                                <option value="12">12小时</option>
                                <option value="24">24小时</option>
                            </select>
                        </div>
                    </div>

                    <div class="data-input-section">
                        <h4 style="margin-bottom: 15px; color: #2c3e50; font-size: 0.9em;">降雨数据输入 (表格格式: 时间戳 + 降水量，单位: mm)</h4>
                        <div class="data-input-grid">
                            <div class="data-input-group">
                                <h4>产品1预报数据</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="product1Table">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="product1TableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('product1')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('product1')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="product1Count">0 行</span>
                                </div>
                            </div>
                            <div class="data-input-group">
                                <h4>产品2预报数据</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="product2Table">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="product2TableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('product2')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('product2')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="product2Count">0 行</span>
                                </div>
                            </div>
                            <div class="data-input-group">
                                <h4>产品3预报数据</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="product3Table">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="product3TableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('product3')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('product3')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="product3Count">0 行</span>
                                </div>
                            </div>
                            <div class="data-input-group">
                                <h4>实测雨量 (可选)</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="observedTable">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="observedTableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('observed')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('observed')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="observedCount">0 行</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Excel文件上传区域 -->
                    <div class="file-upload-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h4 style="margin: 0; color: #2c3e50; font-size: 0.9em;">📁 数据文件上传</h4>
                            <div style="width: 16.67%; text-align: right;">
                                <button class="btn" style="background: #6c757d; color: white; border: none; font-size: 10px; padding: 4.5px 6px; border-radius: 3px; white-space: nowrap; width: 100%;" onclick="clearAllData()">
                                    🗑️ 清空数据
                                </button>
                            </div>
                        </div>
                        <div class="file-upload-container">
                            <input type="file" id="excelFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFileUpload(event)">
                            <div class="file-upload-area" onclick="document.getElementById('excelFileInput').click()">
                                <div class="file-upload-icon">📄</div>
                                <div class="file-upload-text">
                                    <strong>点击上传数据文件</strong>
                                    <br>
                                    <small>支持 .xlsx, .xls 和 .csv 格式</small>
                                </div>
                            </div>
                            <div id="fileUploadStatus" class="file-upload-status" style="display: none;"></div>
                        </div>
                        <div class="file-format-hint">
                            <small style="color: #666;">
                                <strong>📋 文件格式要求:</strong> 数据文件应包含5列数据：第1列为时间戳，第2、3、4列分别为产品1、2、3预报雨量，第5列为实测雨量
                            </small>
                        </div>
                    </div>

                    <div class="button-group">
                        <button class="btn btn-primary" onclick="performStatisticalAnalysis()">📈 统计分析</button>
                        <button class="btn btn-secondary" onclick="performDataFusion()">🔄 数据融合</button>
                    </div>

                    <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 8px; font-size: 11px; color: #666;">
                        <strong>💡 快捷键提示:</strong> 
                        Ctrl+1/2/3 切换标签页 | ←→ 分页导航 | ESC 返回主页
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 结果显示 -->
            <div class="right-panel">
                <!-- 当没有结果时显示的欢迎信息 -->
                <div id="welcomeMessage" class="result-card">
                    <div class="result-card-header">🌧️ 欢迎使用多源降雨产品融合系统</div>
                    <div class="result-card-content">
                        <div style="text-align: center; padding: 40px 20px;">
                            <div style="font-size: 4em; margin-bottom: 20px;">📊</div>
                            <h3 style="color: #2c3e50; margin-bottom: 15px;">开始您的数据分析</h3>
                            <p style="color: #666; margin-bottom: 25px; line-height: 1.6;">
                                请在左侧面板中输入降雨预报数据，然后选择进行统计分析或数据融合操作。
                            </p>
                            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #2e7d32; margin-bottom: 8px;">📈 统计分析</h4>
                                    <p style="font-size: 14px; color: #666;">分析各产品的基础统计特征</p>
                                </div>
                                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #1976d2; margin-bottom: 8px;">🔄 数据融合</h4>
                                    <p style="font-size: 14px; color: #666;">融合多源数据并评估模型效果</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="results-section" id="resultsSection" style="display: none;">
                <div class="breadcrumb">
                    <a href="#" onclick="showMainView()">🏠 主页</a>
                    <span class="separator">></span>
                    <span id="currentPage">分析结果</span>
                </div>

                <div class="tab-container">
                    <div class="tab active" onclick="switchTab('stats')">📊 统计分析</div>
                    <div class="tab" onclick="switchTab('fusion')">🔄 融合结果</div>
                    <div class="tab" onclick="switchTab('detailed')">📋 详细数据</div>
                </div>

                <div id="statsContent" class="tab-content active">
                    <div class="result-card">
                        <div class="result-card-header">📊 数据统计分析结果</div>
                        <div class="result-card-content">
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📈 基础统计信息</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div id="statsTableContainer"></div>
                                </div>
                            </div>

                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📊 数据可视化图表</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div class="stats-charts-container">
                                        <div class="stats-chart-grid">
                                            <div class="stats-chart-item">
                                                <h5>产品1预报数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="product1Chart"></canvas>
                                                </div>
                                            </div>
                                            <div class="stats-chart-item">
                                                <h5>产品2预报数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="product2Chart"></canvas>
                                                </div>
                                            </div>
                                            <div class="stats-chart-item">
                                                <h5>产品3预报数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="product3Chart"></canvas>
                                                </div>
                                            </div>
                                            <div class="stats-chart-item">
                                                <h5>实测雨量数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="observedChart"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="fusionContent" class="tab-content">
                    <div class="result-card">
                        <div class="result-card-header">🔄 多源数据融合结果</div>
                        <div class="result-card-content">
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📁 融合结果导出</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div id="fusionResults"></div>
                                </div>
                            </div>
                            
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📊 可视化图表</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div class="chart-container">
                                        <canvas id="fusionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="collapsible-section">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📋 精度评估指标</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    <div id="metricsTableContainer"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="detailedContent" class="tab-content">
                    <div class="result-card">
                        <div class="result-card-header">📋 详细数据视图</div>
                        <div class="result-card-content">
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>🔢 原始数据预览</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div id="rawDataPreview"></div>
                                </div>
                            </div>
                            
                            <div class="collapsible-section">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📊 分页数据表格</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    <div id="paginatedTableContainer"></div>
                                </div>                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 回到顶部按钮 -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        ⬆️
    </button>

    <!-- 页面指示器 -->
    <div class="page-indicator">
        <div class="indicator-dot active" onclick="scrollToSection('header')" title="顶部"></div>
        <div class="indicator-dot" onclick="scrollToSection('input-section')" title="数据输入"></div>
        <div class="indicator-dot" onclick="scrollToSection('resultsSection')" title="分析结果"></div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text" id="loadingText">正在处理数据...</div>
        </div>
    </div><script>
        // 认证检查和用户界面初始化
        window.addEventListener('load', function() {
            // 检查用户是否已登录
            if (!AuthManager.requireLogin()) {
                return;
            }

            // 显示用户信息
            displayUserInfo();

            // 初始化权限控制
            initializePermissions();
        });

        function displayUserInfo() {
            const user = AuthManager.getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.username;
                document.getElementById('userRole').textContent = user.role === 'admin' ? '管理员' : '普通用户';
                document.getElementById('userInfo').style.display = 'flex';
                document.getElementById('userDropdown').style.display = 'block';

                // 如果是管理员，显示用户管理功能
                if (user.role === 'admin') {
                    // 显示右上角用户管理链接（如果存在）
                    const userManagementLink = document.getElementById('userManagementLink');
                    if (userManagementLink) {
                        userManagementLink.style.display = 'flex';
                    }

                    // 显示左侧菜单中的用户管理
                    document.getElementById('userManagementMenu').style.display = 'block';
                }
            }
        }

        function toggleDropdown() {
            const dropdown = document.getElementById('dropdownContent');
            dropdown.classList.toggle('show');
        }

        // 点击其他地方关闭下拉菜单
        window.addEventListener('click', function(event) {
            if (!event.target.matches('.dropdown-btn') && !event.target.matches('.hamburger')) {
                const dropdown = document.getElementById('dropdownContent');
                if (dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            }
        });

        function initializePermissions() {
            const user = AuthManager.getCurrentUser();
            if (!user) return;

            // 显示管理员菜单
            if (user.role === 'admin') {
                document.getElementById('userManagementMenu').style.display = 'block';
            }
        }

        // 左侧菜单栏功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        function toggleSubmenu(element) {
            const menuItem = element.parentElement;
            menuItem.classList.toggle('expanded');
        }

        function showModule(moduleName) {
            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-link, .submenu-link').forEach(link => {
                link.classList.remove('active');
            });

            // 根据模块名称显示相应内容
            switch(moduleName) {
                case 'fusion':
                    // 显示降雨产品融合模块（当前页面）
                    document.querySelector('[onclick="showModule(\'fusion\')"]').classList.add('active');
                    document.getElementById('mainContent').style.display = 'flex';
                    hideOtherModules();
                    restoreOriginalTitle();
                    break;
                case 'flow-prediction':
                    // 跳转到流量预测系统
                    window.location.href = 'flow_prediction.html';
                    break;
                case 'watershed-info':
                    // 跳转到流域信息页面
                    window.location.href = 'watershed-info.html';
                    break;
                case 'model-fusion':
                    document.querySelector('[onclick="showModule(\'model-fusion\')"]').classList.add('active');
                    showModelFusionModule();
                    break;
                case 'model-flow':
                    document.querySelector('[onclick="showModule(\'model-flow\')"]').classList.add('active');
                    showModelFlowModule();
                    break;
                case 'user-management':
                    // 跳转到用户管理页面
                    window.location.href = 'user-management.html';
                    break;
            }

            // 在移动端关闭侧边栏
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        }

        // 更新页面标题
        function updatePageTitle(title, subtitle) {
            const headerTitle = document.querySelector('.nav-left h1');
            const headerSubtitle = document.querySelector('.nav-left p');
            if (headerTitle) headerTitle.textContent = title;
            if (headerSubtitle) headerSubtitle.textContent = subtitle;
        }

        // 恢复原始标题
        function restoreOriginalTitle() {
            updatePageTitle('🌧️ 多源降雨产品融合系统', 'Multi-Source Rainfall Product Fusion System');
        }

        function hideOtherModules() {
            // 隐藏其他模块的内容
            const otherModules = document.querySelectorAll('.module-content:not(#mainContent)');
            otherModules.forEach(module => {
                module.style.display = 'none';
            });
        }

        function showPersonalInfo() {
            // 显示个人信息管理弹窗
            showPersonalInfoModal();
        }

        // 显示多源融合模型管理模块
        function showModelFusionModule() {
            hideOtherModules();
            let moduleContent = document.getElementById('modelFusionModule');
            if (!moduleContent) {
                moduleContent = createModelFusionModule();
                document.querySelector('.main-wrapper').appendChild(moduleContent);
            }
            moduleContent.style.display = 'block';

            // 更新页面标题
            updatePageTitle('🔧 多源融合模型管理', 'Multi-Source Fusion Model Management');
        }

        // 显示流量预测模型管理模块
        function showModelFlowModule() {
            hideOtherModules();
            let moduleContent = document.getElementById('modelFlowModule');
            if (!moduleContent) {
                moduleContent = createModelFlowModule();
                document.querySelector('.main-wrapper').appendChild(moduleContent);
            }
            moduleContent.style.display = 'block';

            // 更新页面标题
            updatePageTitle('📊 流量预测模型管理', 'Flow Prediction Model Management');
        }



        // 创建多源融合模型管理模块
        function createModelFusionModule() {
            const module = document.createElement('div');
            module.id = 'modelFusionModule';
            module.className = 'module-content';
            module.style.display = 'none';
            module.style.padding = '20px';
            module.style.background = 'rgba(255, 255, 255, 0.98)';
            module.style.backdropFilter = 'blur(20px)';
            module.style.minHeight = 'calc(100vh - 80px)';

            module.innerHTML = `
                <div class="module-header">
                    <h2>🔧 多源融合模型管理</h2>
                </div>

                <!-- 两河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🏞️ 两河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('fusion', '两河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="lianghe-fusion-models">
                        <div class="model-card">
                            <h4>贝叶斯模型平均 (BMA)</h4>
                            <p>基于贝叶斯理论的概率加权融合方法</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('fusion', '两河', '贝叶斯模型平均 (BMA)', '基于贝叶斯理论的概率加权融合方法')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '贝叶斯模型平均 (BMA)')">🗑️ 删除</button>
                            </div>
                        </div>
                        <div class="model-card">
                            <h4>随机森林回归</h4>
                            <p>使用随机森林算法进行非线性融合</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('fusion', '两河', '随机森林回归', '使用随机森林算法进行非线性融合')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '随机森林回归')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 沿塘流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🌊 沿塘流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('fusion', '沿塘')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yantang-fusion-models">
                        <div class="model-card">
                            <h4>深度神经网络 (MLP)</h4>
                            <p>多层感知器神经网络融合方法</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('fusion', '沿塘', '深度神经网络 (MLP)', '多层感知器神经网络融合方法')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '深度神经网络 (MLP)')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 银河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>⭐ 银河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('fusion', '银河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yinhe-fusion-models">
                        <!-- 暂无模型 -->
                        <div class="no-models-placeholder">
                            <p>暂无模型，请点击上方"模型上传"按钮添加模型</p>
                        </div>
                    </div>
                </div>
            `;

            return module;
        }

        // 创建流量预测模型管理模块
        function createModelFlowModule() {
            const module = document.createElement('div');
            module.id = 'modelFlowModule';
            module.className = 'module-content';
            module.style.display = 'none';
            module.style.padding = '20px';
            module.style.background = 'rgba(255, 255, 255, 0.98)';
            module.style.backdropFilter = 'blur(20px)';
            module.style.minHeight = 'calc(100vh - 80px)';

            module.innerHTML = `
                <div class="module-header">
                    <h2>📊 流量预测模型管理</h2>
                </div>

                <!-- 两河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🏞️ 两河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('flow', '两河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="lianghe-flow-models">
                        <div class="model-card">
                            <h4>LSTM神经网络</h4>
                            <p>长短期记忆网络用于时间序列预测</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('flow', '两河', 'LSTM神经网络', '长短期记忆网络用于时间序列预测')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, 'LSTM神经网络')">🗑️ 删除</button>
                            </div>
                        </div>
                        <div class="model-card">
                            <h4>水文模型</h4>
                            <p>基于物理过程的流域水文模型</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('flow', '两河', '水文模型', '基于物理过程的流域水文模型')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '水文模型')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 沿塘流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>🌊 沿塘流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('flow', '沿塘')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yantang-flow-models">
                        <div class="model-card">
                            <h4>集成学习模型</h4>
                            <p>多模型集成的流量预测方法</p>
                            <div class="model-status">状态: 已启用</div>
                            <div class="model-actions">
                                <button class="btn btn-secondary" onclick="showModelDetailModal('flow', '沿塘', '集成学习模型', '多模型集成的流量预测方法')">查看详情</button>
                                <button class="btn btn-success download-btn">📥 下载</button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '集成学习模型')">🗑️ 删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 银河流域 -->
                <div class="watershed-section">
                    <div class="watershed-header">
                        <h3>⭐ 银河流域</h3>
                        <button class="btn btn-primary upload-btn" onclick="showModelUploadModal('flow', '银河')">
                            <span>📤</span> 模型上传
                        </button>
                    </div>
                    <div class="model-cards" id="yinhe-flow-models">
                        <!-- 暂无模型 -->
                        <div class="no-models-placeholder">
                            <p>暂无模型，请点击上方"模型上传"按钮添加模型</p>
                        </div>
                    </div>
                </div>
            `;

            return module;
        }

        // 显示模型上传弹框
        function showModelUploadModal(modelType, watershed) {
            const modal = document.createElement('div');
            modal.className = 'fusion-modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="fusion-modal-content">
                    <div class="fusion-modal-header">
                        <h3>📤 模型上传</h3>
                        <span class="fusion-modal-close" onclick="closeModelUploadModal()">&times;</span>
                    </div>
                    <div class="fusion-modal-body">
                        <form id="modelUploadForm" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="watershedSelect">流域选择:</label>
                                <select id="watershedSelect" class="form-control" required>
                                    <option value="两河" ${watershed === '两河' ? 'selected' : ''}>两河流域</option>
                                    <option value="沿塘" ${watershed === '沿塘' ? 'selected' : ''}>沿塘流域</option>
                                    <option value="银河" ${watershed === '银河' ? 'selected' : ''}>银河流域</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="modelName">模型名称:</label>
                                <input type="text" id="modelName" class="form-control" placeholder="请输入模型名称" required>
                            </div>
                            <div class="form-group">
                                <label for="modelFile">模型文件 (可选):</label>
                                <input type="file" id="modelFile" class="form-control" accept=".pkl,.h5,.pt,.pth,.joblib,.model">
                                <small class="form-text text-muted">支持的文件格式: .pkl, .h5, .pt, .pth, .joblib, .model（可不上传文件，仅创建模型记录）</small>
                            </div>
                            <div class="form-group">
                                <label for="modelDescription">模型描述 (可选):</label>
                                <textarea id="modelDescription" class="form-control" rows="3" placeholder="请输入模型描述（可选）"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="fusion-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModelUploadModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="uploadModel('${modelType}')">上传模型</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 关闭模型上传弹框
        function closeModelUploadModal() {
            const modal = document.querySelector('.fusion-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 上传模型
        function uploadModel(modelType) {
            const form = document.getElementById('modelUploadForm');
            const formData = new FormData();

            const watershed = document.getElementById('watershedSelect').value;
            const modelName = document.getElementById('modelName').value;
            const modelFile = document.getElementById('modelFile').files[0];
            const modelDescription = document.getElementById('modelDescription').value;

            if (!modelName) {
                alert('请填写模型名称');
                return;
            }

            formData.append('watershed', watershed);
            formData.append('modelName', modelName);
            if (modelFile) {
                formData.append('modelFile', modelFile);
            }
            formData.append('modelDescription', modelDescription);
            formData.append('modelType', modelType);

            // 这里应该发送到后端API
            // 暂时模拟上传成功
            setTimeout(() => {
                const successMessage = modelFile ? '模型上传成功！' : '模型记录创建成功！';
                alert(successMessage);
                addModelCard(modelType, watershed, {
                    name: modelName,
                    description: modelDescription || '用户自定义模型',
                    fileName: modelFile ? modelFile.name : '无文件',
                    uploadTime: new Date().toLocaleString()
                });
                closeModelUploadModal();
            }, 1000);
        }

        // 添加模型卡片
        function addModelCard(modelType, watershed, modelInfo) {
            const watershedMap = {
                '两河': 'lianghe',
                '沿塘': 'yantang',
                '银河': 'yinhe'
            };

            const containerId = `${watershedMap[watershed]}-${modelType}-models`;
            const container = document.getElementById(containerId);

            if (!container) return;

            // 移除"暂无模型"占位符
            const placeholder = container.querySelector('.no-models-placeholder');
            if (placeholder) {
                placeholder.remove();
            }

            const modelCard = document.createElement('div');
            modelCard.className = 'model-card';

            const hasFile = modelInfo.fileName && modelInfo.fileName !== '无文件';
            const downloadButton = hasFile
                ? `<button class="btn btn-success download-btn" onclick="downloadModel('${modelInfo.fileName}')">📥 下载</button>`
                : `<button class="btn btn-success download-btn" onclick="downloadModel('无文件')" title="无文件可下载">📥 下载</button>`;

            modelCard.innerHTML = `
                <h4>${modelInfo.name}</h4>
                <p>${modelInfo.description}</p>
                <div class="model-status">状态: 已启用</div>
                <div class="model-actions">
                    <button class="btn btn-secondary" onclick="showModelDetailModal('${modelType}', '${watershed}', '${modelInfo.name}', '${modelInfo.description}', this)">查看详情</button>
                    ${downloadButton}
                    <button class="btn btn-danger btn-sm" onclick="deleteModel(this, '${modelInfo.name}')">🗑️ 删除</button>
                </div>
            `;

            container.appendChild(modelCard);
        }

        // 下载模型
        function downloadModel(fileName) {
            if (fileName === '无文件') {
                alert('该模型暂无文件可下载');
                return;
            }
            // 这里应该调用后端API下载文件
            alert(`正在下载模型文件: ${fileName}`);
        }

        // 删除模型
        function deleteModel(button, modelName) {
            if (confirm(`确定要删除模型 "${modelName}" 吗？`)) {
                const modelCard = button.closest('.model-card');
                modelCard.remove();
                alert('模型删除成功！');
            }
        }

        // 显示模型详情弹框
        function showModelDetailModal(modelType, watershed, modelName, modelDescription, buttonElement = null) {
            const modal = document.createElement('div');
            modal.className = 'fusion-modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="fusion-modal-content">
                    <div class="fusion-modal-header">
                        <h3>📋 模型详情</h3>
                        <span class="fusion-modal-close" onclick="closeModelDetailModal()">&times;</span>
                    </div>
                    <div class="fusion-modal-body">
                        <form id="modelDetailForm">
                            <div class="form-group">
                                <label>模型类型:</label>
                                <input type="text" class="form-control" value="${modelType === 'fusion' ? '多源融合模型' : '流量预测模型'}" readonly style="background: #f5f5f5;">
                            </div>
                            <div class="form-group">
                                <label>所属流域:</label>
                                <input type="text" class="form-control" value="${watershed}流域" readonly style="background: #f5f5f5;">
                            </div>
                            <div class="form-group">
                                <label for="editModelName">模型名称:</label>
                                <input type="text" id="editModelName" class="form-control" value="${modelName}" required>
                            </div>
                            <div class="form-group">
                                <label for="editModelDescription">模型描述:</label>
                                <textarea id="editModelDescription" class="form-control" rows="4" required>${modelDescription}</textarea>
                            </div>
                            <div class="form-group">
                                <label>创建时间:</label>
                                <input type="text" class="form-control" value="${new Date().toLocaleString()}" readonly style="background: #f5f5f5;">
                            </div>
                            <div class="form-group">
                                <label>模型状态:</label>
                                <input type="text" class="form-control" value="已启用" readonly style="background: #f5f5f5;">
                            </div>
                        </form>
                    </div>
                    <div class="fusion-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModelDetailModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="updateModelInfo('${modelType}', '${watershed}', '${modelName}', ${buttonElement ? 'true' : 'false'})">保存修改</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // 存储按钮元素引用，用于更新
            modal.buttonElement = buttonElement;
        }

        // 关闭模型详情弹框
        function closeModelDetailModal() {
            const modal = document.querySelector('.fusion-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 更新模型信息
        function updateModelInfo(modelType, watershed, originalName, isUserUploaded) {
            const nameElement = document.getElementById('editModelName');
            const descElement = document.getElementById('editModelDescription');

            if (!nameElement || !descElement) {
                alert('无法找到表单元素，请重新打开详情窗口');
                return;
            }

            const newName = nameElement.value.trim();
            const newDescription = descElement.value.trim();

            if (!newName || !newDescription) {
                alert('请填写完整的模型名称和描述');
                return;
            }

            // 模拟保存成功
            setTimeout(() => {
                alert('模型信息更新成功！');

                // 如果是用户上传的模型，更新对应的卡片
                const modal = document.querySelector('.fusion-modal');
                if (modal && modal.buttonElement) {
                    const modelCard = modal.buttonElement.closest('.model-card');
                    if (modelCard) {
                        const titleElement = modelCard.querySelector('h4');
                        const descElement = modelCard.querySelector('p');
                        if (titleElement) titleElement.textContent = newName;
                        if (descElement) descElement.textContent = newDescription;

                        // 更新查看详情按钮的参数
                        const detailButton = modelCard.querySelector('.btn-secondary');
                        if (detailButton) {
                            detailButton.setAttribute('onclick', `showModelDetailModal('${modelType}', '${watershed}', '${newName}', '${newDescription}', this)`);
                        }
                    }
                } else {
                    // 如果是预设模型，更新页面上的对应卡片
                    updatePresetModelCard(modelType, watershed, originalName, newName, newDescription);
                }

                closeModelDetailModal();
            }, 500);
        }

        // 更新预设模型卡片
        function updatePresetModelCard(modelType, watershed, originalName, newName, newDescription) {
            // 根据模型类型和流域找到对应的容器
            const watershedMap = {
                '两河': 'lianghe',
                '沿塘': 'yantang',
                '银河': 'yinhe'
            };

            const containerId = `${watershedMap[watershed]}-${modelType}-models`;
            const container = document.getElementById(containerId);

            if (container) {
                const modelCards = container.querySelectorAll('.model-card');
                modelCards.forEach(card => {
                    const titleElement = card.querySelector('h4');
                    if (titleElement && titleElement.textContent.includes(originalName)) {
                        // 直接替换模型名称，不保留图标
                        titleElement.textContent = newName;
                        const descElement = card.querySelector('p');
                        if (descElement) descElement.textContent = newDescription;

                        // 更新查看详情按钮的参数
                        const detailButton = card.querySelector('.btn-secondary');
                        if (detailButton) {
                            detailButton.setAttribute('onclick', `showModelDetailModal('${modelType}', '${watershed}', '${newName}', '${newDescription}', this)`);
                        }
                    }
                });
            }
        }

        // 显示个人信息管理弹窗
        function showPersonalInfoModal() {
            const user = AuthManager.getCurrentUser();
            if (!user) return;

            const modal = document.createElement('div');
            modal.className = 'fusion-modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="fusion-modal-content">
                    <div class="fusion-modal-header">
                        <h3>👤 个人信息管理</h3>
                        <span class="fusion-modal-close" onclick="closePersonalInfoModal()">&times;</span>
                    </div>
                    <div class="fusion-modal-body">
                        <div class="fusion-form-group">
                            <label class="fusion-form-label">用户名</label>
                            <input type="text" class="fusion-form-select" value="${user.username}" readonly style="background: #f5f5f5;">
                        </div>

                        <div class="fusion-form-group">
                            <label class="fusion-form-label">当前角色</label>
                            <input type="text" class="fusion-form-select" value="${user.role === 'admin' ? '管理员' : '普通用户'}" readonly style="background: #f5f5f5;">
                        </div>

                        <div class="fusion-form-group">
                            <label class="fusion-form-label">新密码</label>
                            <input type="password" id="newPassword" class="fusion-form-select" placeholder="输入新密码（留空则不修改）">
                        </div>

                        <div class="fusion-form-group">
                            <label class="fusion-form-label">确认新密码</label>
                            <input type="password" id="confirmPassword" class="fusion-form-select" placeholder="再次输入新密码">
                        </div>
                    </div>
                    <div class="fusion-modal-footer">
                        <button class="fusion-btn fusion-btn-cancel" onclick="closePersonalInfoModal()">取消</button>
                        <button class="fusion-btn fusion-btn-confirm" onclick="updatePersonalInfo()">保存修改</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';
        }

        function closePersonalInfoModal() {
            const modal = document.querySelector('.fusion-modal');
            if (modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
            }
        }

        function updatePersonalInfo() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword && newPassword !== confirmPassword) {
                alert('两次输入的密码不一致！');
                return;
            }

            if (newPassword && newPassword.length < 6) {
                alert('密码长度至少6位！');
                return;
            }

            if (newPassword) {
                const success = AuthManager.updatePassword(newPassword);
                if (success) {
                    alert('密码修改成功！');
                    closePersonalInfoModal();
                } else {
                    alert('密码修改失败！');
                }
            } else {
                alert('没有需要修改的信息！');
            }
        }

        let currentChart = null;
        let currentPage = 1;
        let itemsPerPage = 10;
        let allDataPoints = [];

        // 流域选择处理函数
        function onBasinChange() {
            const basinSelect = document.getElementById('basinSelect');
            const selectedBasin = basinSelect.value;

            if (selectedBasin) {
                // 清理任何现有的流域信息显示
                const existingInfo = document.getElementById('basinInfo');
                if (existingInfo) {
                    existingInfo.remove();
                }

                // 根据流域调整默认参数
                adjustParametersForBasin(selectedBasin);
            }
        }

        // function showBasinInfo(basin) {
        //     const basinNames = {
        //         'lianghe': '两河流域',
        //         'yantang': '沿塘流域',
        //         'yinhe': '银河流域'
        //     };

        //     const basinDescriptions = {
        //         'lianghe': '两河流域位于长江中下游地区，流域面积约1200平方公里，主要河流包括东河和西河。',
        //         'yantang': '沿塘流域位于太湖流域南部，流域面积约800平方公里，地势平坦，河网密布。',
        //         'yinhe': '银河流域位于珠江流域北部，流域面积约950平方公里，山地丘陵地形为主。'
        //     };

        //     // 创建或更新流域信息显示
        //     let infoDiv = document.getElementById('basinInfo');
        //     if (!infoDiv) {
        //         infoDiv = document.createElement('div');
        //         infoDiv.id = 'basinInfo';
        //         infoDiv.style.cssText = `
        //             margin: 15px 0;
        //             padding: 12px;
        //             background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        //             border-radius: 8px;
        //             border-left: 4px solid #2196f3;
        //             font-size: 13px;
        //             line-height: 1.4;
        //         `;
        //         // 将信息显示在input-grid后面
        //         const inputGrid = document.querySelector('.input-grid');
        //         inputGrid.parentNode.insertBefore(infoDiv, inputGrid.nextSibling);
        //     }

        //     infoDiv.innerHTML = `
        //         <div style="font-weight: bold; color: #1976d2; margin-bottom: 5px;">
        //             📍 已选择：${basinNames[basin]}
        //         </div>
        //         <div style="color: #555;">
        //             ${basinDescriptions[basin]}
        //         </div>
        //     `;
        // }

        function adjustParametersForBasin(basin) {
            // 根据不同流域调整BMA算法参数
            const basinConfigs = {
                'lianghe': {
                    temperature: 2.0,
                    minWeight: 0.15,
                    maxWeight: 0.70,
                    description: '山区流域，降雨变化较大'
                },
                'yantang': {
                    temperature: 1.8,
                    minWeight: 0.18,
                    maxWeight: 0.65,
                    description: '平原流域，降雨相对均匀'
                },
                'yinhe': {
                    temperature: 2.2,
                    minWeight: 0.12,
                    maxWeight: 0.75,
                    description: '丘陵流域，降雨时空分布不均'
                }
            };

            // 存储当前流域配置供后续使用
            window.currentBasinConfig = basinConfigs[basin];

            console.log(`已切换到${basin}流域配置:`, window.currentBasinConfig);
        }

        // function loadBasinSampleData(basin) {
        //     // 为不同流域加载相应的示例数据
        //     const sampleData = {
        //         'lianghe': {
        //             product1: generateBasinSpecificData(2.5, 1.8, 24),
        //             product2: generateBasinSpecificData(2.3, 1.6, 24),
        //             product3: generateBasinSpecificData(2.7, 2.0, 24),
        //             observed: generateBasinSpecificData(2.4, 1.7, 24)
        //         },
        //         'yantang': {
        //             product1: generateBasinSpecificData(1.8, 1.2, 24),
        //             product2: generateBasinSpecificData(1.6, 1.0, 24),
        //             product3: generateBasinSpecificData(2.0, 1.4, 24),
        //             observed: generateBasinSpecificData(1.7, 1.1, 24)
        //         },
        //         'yinhe': {
        //             product1: generateBasinSpecificData(3.2, 2.5, 24),
        //             product2: generateBasinSpecificData(2.9, 2.2, 24),
        //             product3: generateBasinSpecificData(3.5, 2.8, 24),
        //             observed: generateBasinSpecificData(3.1, 2.4, 24)
        //         }
        //     };

        //     const basinNames = {
        //         'lianghe': '两河流域',
        //         'yantang': '沿塘流域',
        //         'yinhe': '银河流域'
        //     };

        //     // 询问用户是否要加载示例数据
        //     if (confirm(`是否要加载${basinNames[basin]}的示例数据？`)) {
        //         const data = sampleData[basin];

        //         // 清空现有表格
        //         ['product1', 'product2', 'product3', 'observed'].forEach(product => {
        //             clearTable(product);
        //         });

        //         // 填充新数据
        //         fillTableData('product1', data.product1);
        //         fillTableData('product2', data.product2);
        //         fillTableData('product3', data.product3);
        //         fillTableData('observed', data.observed);

        //         // 显示加载成功消息
        //         showBasinDataLoadedMessage(basin);
        //     }
        // }

        function generateBasinSpecificData(baseValue, variation, count) {
            const data = [];
            const startTime = new Date('2024-01-01T00:00:00');

            for (let i = 0; i < count; i++) {
                const timestamp = new Date(startTime.getTime() + i * 60 * 60 * 1000);
                const timeStr = timestamp.toISOString().slice(0, 16).replace('T', ' ');
                const value = Math.max(0, baseValue + (Math.random() - 0.5) * variation * 2);
                data.push({ timestamp: timeStr, value: parseFloat(value.toFixed(2)) });
            }

            return data;
        }

        // function showBasinDataLoadedMessage(basin) {
        //     const basinNames = {
        //         'lianghe': '两河流域',
        //         'yantang': '沿塘流域',
        //         'yinhe': '银河流域'
        //     };

        //     // 创建临时消息提示
        //     const messageDiv = document.createElement('div');
        //     messageDiv.style.cssText = `
        //         position: fixed;
        //         top: 20px;
        //         right: 20px;
        //         background: #4caf50;
        //         color: white;
        //         padding: 12px 20px;
        //         border-radius: 6px;
        //         box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        //         z-index: 10000;
        //         font-size: 14px;
        //         animation: slideInRight 0.3s ease-out;
        //     `;
        //     messageDiv.innerHTML = `✅ 已加载${basinNames[basin]}示例数据`;

        //     document.body.appendChild(messageDiv);

        //     // 3秒后自动移除
        //     setTimeout(() => {
        //         messageDiv.style.animation = 'slideOutRight 0.3s ease-in';
        //         setTimeout(() => {
        //             if (messageDiv.parentNode) {
        //                 messageDiv.parentNode.removeChild(messageDiv);
        //             }
        //         }, 300);
        //     }, 3000);
        // }

        function parseData(dataString) {
            if (!dataString.trim()) return [];

            const lines = dataString.trim().split('\n');
            const parsedData = [];

            lines.forEach((line, index) => {
                const trimmedLine = line.trim();
                if (!trimmedLine) return;

                // 检查是否包含逗号分隔符
                if (trimmedLine.includes(',')) {
                    const parts = trimmedLine.split(',');
                    if (parts.length >= 2) {
                        const timestamp = parts[0].trim();
                        const value = parseFloat(parts[1].trim());

                        if (!isNaN(value)) {
                            parsedData.push({
                                timestamp: timestamp,
                                value: value
                            });
                        }
                    }
                } else {
                    // 兼容旧格式（仅数值）
                    const value = parseFloat(trimmedLine);
                    if (!isNaN(value)) {
                        // 生成默认时间戳
                        const baseTime = new Date('2024-01-01 09:00:00');
                        const timestamp = new Date(baseTime.getTime() + index * 3600000).toISOString().slice(0, 19).replace('T', ' ');
                        parsedData.push({
                            timestamp: timestamp,
                            value: value
                        });
                    }
                }
            });

            return parsedData;
        }

        // 从表格中获取数据
        function getTableData(productName) {
            const tbody = document.getElementById(productName + 'TableBody');
            const rows = tbody.querySelectorAll('tr');
            const data = [];

            rows.forEach(row => {
                const timeInput = row.querySelector('.time-input');
                const valueInput = row.querySelector('.value-input');

                if (timeInput && valueInput && timeInput.value.trim() && valueInput.value.trim()) {
                    const timestamp = timeInput.value.trim();
                    const value = parseFloat(valueInput.value.trim());

                    if (!isNaN(value)) {
                        data.push({
                            timestamp: timestamp,
                            value: value
                        });
                    }
                }
            });

            return data;
        }

        // 添加表格行
        function addTableRow(productName, timestamp = '', value = '') {
            const tbody = document.getElementById(productName + 'TableBody');
            const row = document.createElement('tr');

            row.innerHTML = `
                <td><input type="text" class="time-input" value="${timestamp}" placeholder="YYYY-MM-DD HH:MM"></td>
                <td><input type="number" class="value-input" value="${value}" placeholder="0.0" step="0.001"></td>
            `;

            tbody.appendChild(row);
            updateRowCount(productName);
        }

        // 清空表格
        function clearTable(productName) {
            const tbody = document.getElementById(productName + 'TableBody');
            tbody.innerHTML = '';
            updateRowCount(productName);
        }

        // 更新行数显示
        function updateRowCount(productName) {
            const tbody = document.getElementById(productName + 'TableBody');
            const count = tbody.querySelectorAll('tr').length;
            const countElement = document.getElementById(productName + 'Count');
            if (countElement) {
                countElement.textContent = `${count} 行`;
            }
        }

        // 填充表格数据
        function fillTableData(productName, data) {
            clearTable(productName);

            data.forEach(item => {
                if (typeof item === 'string' && item.includes(',')) {
                    // 处理字符串格式：'时间戳,数值'
                    const parts = item.split(',');
                    const timestamp = parts[0].trim();
                    const value = parts[1].trim();
                    addTableRow(productName, timestamp, value);
                } else if (typeof item === 'object' && item.timestamp && item.value !== undefined) {
                    // 处理对象格式：{timestamp: '时间戳', value: 数值}
                    addTableRow(productName, item.timestamp, item.value);
                } else if (typeof item === 'number') {
                    // 处理纯数值格式（生成默认时间戳）
                    const defaultTime = new Date('2024-01-01 09:00:00');
                    const rowIndex = document.getElementById(productName + 'TableBody').children.length;
                    const timestamp = new Date(defaultTime.getTime() + rowIndex * 3600000).toISOString().slice(0, 19).replace('T', ' ');
                    addTableRow(productName, timestamp, item);
                }
            });
        }

        // 数据文件处理函数
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const statusDiv = document.getElementById('fileUploadStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'file-upload-status';
            statusDiv.innerHTML = '📤 正在读取文件...';

            const reader = new FileReader();

            // 根据文件类型选择不同的处理方式
            if (file.name.endsWith('.csv')) {
                reader.onload = function(e) {
                    try {
                        parseCSVData(e.target.result, file.name);
                    } catch (error) {
                        showUploadError('CSV文件读取失败: ' + error.message);
                    }
                };
                reader.readAsText(file);
            } else {
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });

                        // 获取第一个工作表
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];

                        // 转换为JSON格式
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                        if (jsonData.length === 0) {
                            throw new Error('Excel文件为空');
                        }

                        // 解析数据
                        parseExcelData(jsonData, file.name);

                    } catch (error) {
                        showUploadError('文件读取失败: ' + error.message);
                    }
                };
                reader.readAsArrayBuffer(file);
            }

            reader.onerror = function() {
                showUploadError('文件读取失败，请检查文件格式');
            };
        }

        function parseExcelData(jsonData, fileName) {
            try {
                // 始终忽略第一行
                let startRow = 1;

                const dataRows = jsonData.slice(startRow);

                if (dataRows.length === 0) {
                    throw new Error('没有找到有效的数据行');
                }

                // 提取各列数据
                const product1Data = [];
                const product2Data = [];
                const product3Data = [];
                const observedData = [];

                dataRows.forEach((row, index) => {
                    if (row.length < 5) {
                        throw new Error(`第${startRow + index + 1}行数据不完整，需要5列数据（时间戳 + 产品1 + 产品2 + 产品3 + 实测雨量）`);
                    }

                    // 读取前五列：第一列为时间戳，第2、3、4列为产品1、2、3，第五列为实测雨量
                    const timestamp = row[0];
                    const val1 = parseFloat(row[1]); // 产品1
                    const val2 = parseFloat(row[2]); // 产品2
                    const val3 = parseFloat(row[3]); // 产品3
                    const val4 = parseFloat(row[4]); // 实测雨量

                    if (isNaN(val1) || isNaN(val2) || isNaN(val3)) {
                        throw new Error(`第${startRow + index + 1}行产品预报数据包含无效数值`);
                    }

                    // 格式化时间戳
                    let formattedTimestamp = timestamp;
                    if (timestamp instanceof Date) {
                        formattedTimestamp = timestamp.toISOString().slice(0, 19).replace('T', ' ');
                    } else if (typeof timestamp === 'string') {
                        formattedTimestamp = timestamp;
                    } else if (typeof timestamp === 'number') {
                        // Excel日期序列号转换
                        const excelDate = new Date((timestamp - 25569) * 86400 * 1000);
                        formattedTimestamp = excelDate.toISOString().slice(0, 19).replace('T', ' ');
                    }

                    // 为每个产品创建带时间戳的数据
                    product1Data.push({ timestamp: formattedTimestamp, value: val1 });
                    product2Data.push({ timestamp: formattedTimestamp, value: val2 });
                    product3Data.push({ timestamp: formattedTimestamp, value: val3 });

                    // 实测雨量数据（第五列）
                    if (!isNaN(val4)) {
                        observedData.push({ timestamp: formattedTimestamp, value: val4 });
                    } else {
                        // 如果实测雨量为空或无效，也要保持时间戳一致
                        observedData.push({ timestamp: formattedTimestamp, value: 0.0 });
                    }
                });

                // 填充到表格并添加视觉反馈
                fillTableWithAnimation('product1', product1Data, '产品1');
                fillTableWithAnimation('product2', product2Data, '产品2');
                fillTableWithAnimation('product3', product3Data, '产品3');
                fillTableWithAnimation('observed', observedData, '实测雨量');

                // 显示成功信息和数据预览
                const statusDiv = document.getElementById('fileUploadStatus');
                statusDiv.className = 'file-upload-status success';

                // 生成数据预览
                const previewData = generateDataPreviewWithTimestamp(product1Data, product2Data, product3Data, observedData);

                statusDiv.innerHTML = `
                    ✅ 文件上传成功！<br>
                    📊 已读取 ${product1Data.length} 行数据<br>
                    📁 文件名: ${fileName}<br>
                    📋 数据分布：产品1(${product1Data.length}行) | 产品2(${product2Data.length}行) | 产品3(${product3Data.length}行) | 实测雨量(${observedData.length}行)
                    <br><br>
                    <div style="margin-top: 10px;">
                        <strong>📋 数据预览 (前5行):</strong>
                        ${previewData}
                    </div>
                `;

            } catch (error) {
                showUploadError(error.message);
            }
        }

        function parseCSVData(csvText, fileName) {
            try {
                const lines = csvText.trim().split('\n');
                if (lines.length === 0) {
                    throw new Error('CSV文件为空');
                }

                // 始终忽略第一行
                let startRow = 1;

                const dataRows = lines.slice(startRow);

                if (dataRows.length === 0) {
                    throw new Error('没有找到有效的数据行');
                }

                // 提取各列数据
                const product1Data = [];
                const product2Data = [];
                const product3Data = [];
                const observedData = [];

                dataRows.forEach((line, index) => {
                    const columns = line.split(',');
                    if (columns.length < 5) {
                        throw new Error(`第${startRow + index + 1}行数据不完整，需要5列数据（时间戳 + 产品1 + 产品2 + 产品3 + 实测雨量）`);
                    }

                    // 读取前五列：第一列为时间戳，第2、3、4列为产品1、2、3，第五列为实测雨量
                    const timestamp = columns[0].trim();
                    const val1 = parseFloat(columns[1].trim()); // 产品1
                    const val2 = parseFloat(columns[2].trim()); // 产品2
                    const val3 = parseFloat(columns[3].trim()); // 产品3
                    const val4 = parseFloat(columns[4].trim()); // 实测雨量

                    if (isNaN(val1) || isNaN(val2) || isNaN(val3)) {
                        throw new Error(`第${startRow + index + 1}行产品预报数据包含无效数值`);
                    }

                    if (isNaN(val4)) {
                        console.warn(`第${startRow + index + 1}行实测雨量数据无效，将设为0`);
                    }

                    // 存储数据
                    product1Data.push({ timestamp: timestamp, value: val1 });
                    product2Data.push({ timestamp: timestamp, value: val2 });
                    product3Data.push({ timestamp: timestamp, value: val3 });
                    observedData.push({ timestamp: timestamp, value: isNaN(val4) ? 0 : val4 });
                });

                // 更新全局数据变量
                window.product1Data = product1Data;
                window.product2Data = product2Data;
                window.product3Data = product3Data;
                window.observedData = observedData;

                // 填充到表格并添加视觉反馈
                fillTableWithAnimation('product1', product1Data, '产品1');
                fillTableWithAnimation('product2', product2Data, '产品2');
                fillTableWithAnimation('product3', product3Data, '产品3');
                fillTableWithAnimation('observed', observedData, '实测雨量');

                // 显示成功信息和数据预览
                const statusDiv = document.getElementById('fileUploadStatus');
                statusDiv.className = 'file-upload-status success';

                // 生成数据预览
                const previewData = generateDataPreviewWithTimestamp(product1Data, product2Data, product3Data, observedData);

                statusDiv.innerHTML = `
                    ✅ 文件上传成功！<br>
                    📊 已读取 ${product1Data.length} 行数据<br>
                    📁 文件名: ${fileName}<br>
                    📋 数据分布：产品1(${product1Data.length}行) | 产品2(${product2Data.length}行) | 产品3(${product3Data.length}行) | 实测雨量(${observedData.length}行)
                    <br><br>
                    <div style="margin-top: 10px;">
                        <strong>📋 数据预览 (前5行):</strong>
                        ${previewData}
                    </div>
                `;

            } catch (error) {
                showUploadError(error.message);
            }
        }

        function showUploadError(message) {
            const statusDiv = document.getElementById('fileUploadStatus');
            statusDiv.className = 'file-upload-status error';
            statusDiv.innerHTML = `❌ ${message}`;
        }

        // 带动画效果的表格填充函数
        function fillTableWithAnimation(productName, data, label) {
            const parentGroup = document.getElementById(productName + 'Table').closest('.data-input-group');

            // 添加高亮效果
            parentGroup.style.transition = 'all 0.3s ease';
            parentGroup.style.transform = 'scale(1.02)';
            parentGroup.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
            parentGroup.style.borderColor = '#667eea';

            // 清空并逐步填充数据
            clearTable(productName);

            setTimeout(() => {
                fillTableData(productName, data);

                // 显示数据行数提示
                const dataCount = data.length;
                const header = parentGroup.querySelector('h4');
                const originalText = header.textContent;
                header.innerHTML = `${originalText} <span style="color: #4caf50; font-size: 0.8em;">(已填充 ${dataCount} 行)</span>`;

                // 恢复原始样式
                setTimeout(() => {
                    parentGroup.style.transform = 'scale(1)';
                    parentGroup.style.boxShadow = '';
                    parentGroup.style.borderColor = '#e1e8ed';

                    // 3秒后移除行数提示
                    setTimeout(() => {
                        header.textContent = originalText;
                    }, 3000);
                }, 500);
            }, 200);
        }

        // 带动画效果的文本框填充函数（兼容性保留）
        function fillTextAreaWithAnimation(elementId, data, label) {
            const textarea = document.getElementById(elementId);
            if (!textarea) return; // 如果找不到textarea，跳过

            const parentGroup = textarea.closest('.data-input-group');

            // 添加高亮效果
            parentGroup.style.transition = 'all 0.3s ease';
            parentGroup.style.transform = 'scale(1.02)';
            parentGroup.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
            parentGroup.style.borderColor = '#667eea';

            // 清空并逐步填充数据
            textarea.value = '';

            setTimeout(() => {
                textarea.value = data;

                // 显示数据行数提示
                const dataLines = data.split('\n').length;
                const header = parentGroup.querySelector('h4');
                const originalText = header.textContent;
                header.innerHTML = `${originalText} <span style="color: #4caf50; font-size: 0.8em;">(已填充 ${dataLines} 行)</span>`;

                // 恢复原始样式
                setTimeout(() => {
                    parentGroup.style.transform = 'scale(1)';
                    parentGroup.style.boxShadow = '';
                    parentGroup.style.borderColor = '#e1e8ed';

                    // 3秒后移除行数提示
                    setTimeout(() => {
                        header.textContent = originalText;
                    }, 3000);
                }, 500);
            }, 200);
        }

        // 生成数据预览表格（带时间戳）
        function generateDataPreviewWithTimestamp(product1Data, product2Data, product3Data, observedData) {
            const maxRows = Math.min(5, product1Data.length);
            const hasObserved = observedData.length === product1Data.length;

            let previewHtml = `
                <div style="overflow-x: auto; margin-top: 8px;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 10px; background: white; border-radius: 4px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">行号</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">时间戳</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">产品1</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">产品2</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">产品3</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">实测雨量</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            for (let i = 0; i < maxRows; i++) {
                // 处理对象格式的数据
                let timestamp, val1, val2, val3, observedVal;

                if (typeof product1Data[i] === 'object') {
                    // 对象格式：{timestamp: '时间戳', value: 数值}
                    timestamp = product1Data[i].timestamp;
                    val1 = parseFloat(product1Data[i].value).toFixed(3);
                    val2 = parseFloat(product2Data[i].value).toFixed(3);
                    val3 = parseFloat(product3Data[i].value).toFixed(3);
                    observedVal = observedData[i] ? parseFloat(observedData[i].value).toFixed(3) : '0.000';
                } else {
                    // 字符串格式：'时间戳,数值'
                    const product1Parts = product1Data[i].split(',');
                    const product2Parts = product2Data[i].split(',');
                    const product3Parts = product3Data[i].split(',');

                    timestamp = product1Parts[0];
                    val1 = parseFloat(product1Parts[1]).toFixed(3);
                    val2 = parseFloat(product2Parts[1]).toFixed(3);
                    val3 = parseFloat(product3Parts[1]).toFixed(3);

                    if (observedData[i]) {
                        const observedParts = observedData[i].split(',');
                        observedVal = parseFloat(observedParts[1]).toFixed(3);
                    } else {
                        observedVal = '0.000';
                    }
                }

                previewHtml += `
                    <tr>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">${i + 1}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center; font-size: 9px;">${timestamp}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${val1}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${val2}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${val3}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${observedVal}</td>
                    </tr>
                `;
            }

            previewHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            if (product1Data.length > 5) {
                previewHtml += `<div style="text-align: center; margin-top: 5px; font-size: 10px; color: #666;">... 还有 ${product1Data.length - 5} 行数据</div>`;
            }

            return previewHtml;
        }

        // 生成数据预览表格（兼容旧格式）
        function generateDataPreview(product1Data, product2Data, product3Data, observedData) {
            const maxRows = Math.min(5, product1Data.length);
            const hasObserved = observedData.length === product1Data.length;

            let previewHtml = `
                <div style="overflow-x: auto; margin-top: 8px;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 11px; background: white; border-radius: 4px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">行号</th>
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">产品1</th>
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">产品2</th>
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">产品3</th>
                                ${hasObserved ? '<th style="padding: 6px; border: 1px solid #ddd; text-align: center;">实测雨量</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
            `;

            for (let i = 0; i < maxRows; i++) {
                previewHtml += `
                    <tr>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">${i + 1}</td>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${product1Data[i].toFixed(3)}</td>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${product2Data[i].toFixed(3)}</td>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${product3Data[i].toFixed(3)}</td>
                        ${hasObserved ? `<td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${observedData[i].toFixed(3)}</td>` : ''}
                    </tr>
                `;
            }

            previewHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            if (product1Data.length > 5) {
                previewHtml += `<div style="text-align: center; margin-top: 5px; font-size: 10px; color: #666;">... 还有 ${product1Data.length - 5} 行数据</div>`;
            }

            return previewHtml;
        }

        // 自动生成时间序列表格
        function generateTimeSeriesTables() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const frequency = parseInt(document.getElementById('frequency').value);

            if (!startTime || !endTime) {
                alert('请先设置起始时间和结束时间');
                return;
            }

            const start = new Date(startTime);
            const end = new Date(endTime);

            if (start >= end) {
                alert('结束时间必须晚于起始时间');
                return;
            }

            // 生成时间戳数组
            const timestamps = [];
            let current = new Date(start);

            while (current <= end) {
                timestamps.push(current.toISOString().slice(0, 19).replace('T', ' '));
                current = new Date(current.getTime() + frequency * 3600000); // 增加频率小时数
            }

            // 为每个产品表格生成时间序列
            const products = ['product1', 'product2', 'product3', 'observed'];

            products.forEach(productName => {
                // 获取现有数据
                const existingData = getTableData(productName);
                const existingValues = existingData.map(item => item.value);

                // 清空表格
                clearTable(productName);

                // 为每个时间戳添加行
                timestamps.forEach((timestamp, index) => {
                    const value = existingValues[index] || 0.0;
                    addTableRow(productName, timestamp, value);
                });
            });

            alert(`已为 ${timestamps.length} 个时间点生成时间序列表格`);
        }

        // 从数据中提取数值
        function extractValuesFromData(dataString) {
            const lines = dataString.split('\n');
            const values = [];

            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (!trimmedLine) return;

                if (trimmedLine.includes(',')) {
                    // 已有时间戳格式，提取数值部分
                    const parts = trimmedLine.split(',');
                    if (parts.length >= 2) {
                        const value = parseFloat(parts[1].trim());
                        if (!isNaN(value)) {
                            values.push(value);
                        }
                    }
                } else {
                    // 纯数值格式
                    const value = parseFloat(trimmedLine);
                    if (!isNaN(value)) {
                        values.push(value);
                    }
                }
            });

            return values;
        }

        // 生成带时间戳的数据
        function generateTimestampedData(timestamps, values) {
            const result = [];
            const maxLength = Math.min(timestamps.length, values.length);

            for (let i = 0; i < maxLength; i++) {
                result.push(`${timestamps[i]},${values[i]}`);
            }

            // 如果时间戳多于数值，用0填充
            for (let i = values.length; i < timestamps.length; i++) {
                result.push(`${timestamps[i]},0.0`);
            }

            return result.join('\n');
        }

        // 清空所有数据
        function clearAllData() {
            if (confirm('确定要清空所有输入的数据吗？')) {
                // 清空所有表格
                clearTable('product1');
                clearTable('product2');
                clearTable('product3');
                clearTable('observed');

                // 清空文件上传状态
                document.getElementById('fileUploadStatus').style.display = 'none';
                document.getElementById('excelFileInput').value = '';

                // 重置所有标题
                const headers = document.querySelectorAll('.data-input-group h4');
                headers.forEach(header => {
                    const originalTexts = ['产品1预报数据', '产品2预报数据', '产品3预报数据', '实测雨量 (可选)'];
                    const index = Array.from(headers).indexOf(header);
                    if (index < originalTexts.length) {
                        header.textContent = originalTexts[index];
                    }
                });

                // 显示清空成功提示
                const statusDiv = document.getElementById('fileUploadStatus');
                statusDiv.style.display = 'block';
                statusDiv.className = 'file-upload-status success';
                statusDiv.innerHTML = '✅ 所有数据已清空';

                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 2000);
            }
        }

        // 初始化表格
        function initializeTables() {
            const products = ['product1', 'product2', 'product3', 'observed'];
            products.forEach(productName => {
                // 为每个表格添加几行示例数据
                addTableRow(productName, '2024-01-01 09:00', '0.0');
                addTableRow(productName, '2024-01-01 10:00', '0.0');
                addTableRow(productName, '2024-01-01 11:00', '0.0');
            });
        }

        // 添加拖拽上传功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化表格
            initializeTables();

            const uploadArea = document.querySelector('.file-upload-area');

            if (uploadArea) {
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];
                        if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls') || file.name.endsWith('.csv')) {
                            document.getElementById('excelFileInput').files = files;
                            handleFileUpload({ target: { files: files } });
                        } else {
                            showUploadError('请上传数据文件 (.xlsx, .xls 或 .csv 格式)');
                        }
                    }
                });
            }
        });

        function calculateStats(data, name) {
            if (data.length === 0) return null;
            
            const mean = data.reduce((a, b) => a + b, 0) / data.length;
            const variance = data.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / data.length;
            const std = Math.sqrt(variance);
            const min = Math.min(...data);
            const max = Math.max(...data);
            const nonZeroCount = data.filter(val => val > 0).length;
            
            return {
                name,
                mean: mean.toFixed(3),
                std: std.toFixed(3),
                min: min.toFixed(3),
                max: max.toFixed(3),
                nonZeroCount,
                total: data.length
            };
        }

        // 折叠/展开功能
        function toggleCollapsible(header) {
            const section = header.parentElement;
            const content = section.querySelector('.collapsible-content');
            const toggle = section.querySelector('.collapsible-toggle');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                section.classList.remove('expanded');
                content.style.display = 'none';
            } else {
                content.classList.add('active');
                section.classList.add('expanded');
                content.style.display = 'block';
            }
        }

        // 分页功能
        function createPagination(totalItems, container) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            
            let paginationHtml = `
                <div class="pagination-container">
                    <button class="pagination-btn" onclick="changePage(1)" ${currentPage === 1 ? 'disabled' : ''}>
                        ⏮️ 首页
                    </button>
                    <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                        ⬅️ 上一页
                    </button>
                    <span class="pagination-info">
                        第 ${currentPage} 页，共 ${totalPages} 页 (${totalItems} 条记录)
                    </span>
                    <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                        下一页 ➡️
                    </button>
                    <button class="pagination-btn" onclick="changePage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>
                        末页 ⏭️
                    </button>
                </div>
            `;
            
            const paginationDiv = document.createElement('div');
            paginationDiv.innerHTML = paginationHtml;
            container.appendChild(paginationDiv);
        }

        function changePage(page) {
            currentPage = page;
            displayPaginatedTable();
        }

        function displayPaginatedTable() {
            const container = document.getElementById('paginatedTableContainer');
            container.innerHTML = '';
            
            if (allDataPoints.length === 0) {
                container.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, allDataPoints.length);
            const pageData = allDataPoints.slice(startIndex, endIndex);
            
            let tableHtml = `
                <div class="table-container">
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>时间</th>
                                <th>产品1 (mm)</th>
                                <th>产品2 (mm)</th>
                                <th>产品3 (mm)</th>
                                <th>融合结果 (mm)</th>
                                ${allDataPoints[0].observed !== undefined ? '<th>实测雨量 (mm)</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            pageData.forEach((item, index) => {
                const globalIndex = startIndex + index + 1;
                tableHtml += `
                    <tr>
                        <td>${globalIndex}</td>
                        <td>${item.time}</td>
                        <td>${item.product1.toFixed(3)}</td>
                        <td>${item.product2.toFixed(3)}</td>
                        <td>${item.product3.toFixed(3)}</td>
                        <td style="font-weight: 600; color: #2e7d32;">${item.fusedResult.toFixed(3)}</td>
                        ${item.observed !== undefined ? `<td>${item.observed.toFixed(3)}</td>` : ''}
                    </tr>
                `;
            });
            
            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;
            
            container.innerHTML = tableHtml;
            createPagination(allDataPoints.length, container);
        }

        function displayRawDataPreview() {
            const container = document.getElementById('rawDataPreview');
            
            if (allDataPoints.length === 0) {
                container.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            const previewCount = 5;
            const hasObserved = allDataPoints[0].observed !== undefined;
            
            let html = `
                <div class="data-preview">
                    <div class="data-preview-header">
                        <h5>📊 数据概览</h5>
                        <div class="data-preview-stats">
                            总计 ${allDataPoints.length} 条记录，显示前 ${Math.min(previewCount, allDataPoints.length)} 条
                        </div>
                    </div>
                    <div style="overflow-x: auto;">
                        <table class="stats-table" style="min-width: 600px;">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>产品1</th>
                                    <th>产品2</th>
                                    <th>产品3</th>
                                    <th>融合结果</th>
                                    ${hasObserved ? '<th>实测雨量</th>' : ''}
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            for (let i = 0; i < Math.min(previewCount, allDataPoints.length); i++) {
                const item = allDataPoints[i];
                html += `
                    <tr>
                        <td>${item.time}</td>
                        <td>${item.product1.toFixed(3)}</td>
                        <td>${item.product2.toFixed(3)}</td>
                        <td>${item.product3.toFixed(3)}</td>
                        <td style="font-weight: 600; color: #2e7d32;">${item.fusedResult.toFixed(3)}</td>
                        ${hasObserved ? `<td>${item.observed.toFixed(3)}</td>` : ''}
                    </tr>
                `;
            }
            
            html += `
                            </tbody>
                        </table>
                    </div>
                    ${allDataPoints.length > previewCount ? 
                        `<div style="text-align: center; margin-top: 15px;">
                            <button class="view-full-btn" onclick="switchTab('detailed'); document.querySelector('.collapsible-section:nth-child(2) .collapsible-header').click();">
                                查看完整数据表格
                            </button>
                        </div>` : ''
                    }
                </div>
            `;
            
            container.innerHTML = html;
        }        function showMainView() {
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('welcomeMessage').style.display = 'block';
            document.getElementById('currentPage').textContent = '主页';
        }

        // 加载指示器函数
        function showLoading(text = '正在处理数据...') {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('loadingOverlay').classList.add('active');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('active');
        }

        function performStatisticalAnalysis() {
            showLoading('正在进行统计分析...');

            setTimeout(() => {
                // 从表格获取数据
                const product1Data = getTableData('product1');
                const product2Data = getTableData('product2');
                const product3Data = getTableData('product3');
                const observedData = getTableData('observed');

                // 提取数值数组
                const product1 = product1Data.map(item => item.value);
                const product2 = product2Data.map(item => item.value);
                const product3 = product3Data.map(item => item.value);
                const observed = observedData.map(item => item.value);

                if (product1.length === 0 || product2.length === 0 || product3.length === 0) {
                    hideLoading();
                    showError('请确保至少输入三个预报产品的数据！');
                    return;
                }

                if (product1.length !== product2.length || product2.length !== product3.length) {
                    hideLoading();
                    showError('三个预报产品的数据长度必须一致！');
                    return;
                }

                if (observed.length > 0 && observed.length !== product1.length) {
                    hideLoading();
                    showError('实测雨量数据长度必须与预报数据一致！');
                    return;
                }

                const stats = [];
                stats.push(calculateStats(product1, '产品1'));
                stats.push(calculateStats(product2, '产品2'));
                stats.push(calculateStats(product3, '产品3'));
                
                if (observed.length > 0) {
                    stats.push(calculateStats(observed, '实测雨量'));
                }

                // 准备所有数据点
                prepareDataPoints(product1, product2, product3, observed);
                displayStatsTable(stats);
                createStatsCharts(product1, product2, product3, observed);
                displayRawDataPreview();
                document.getElementById('welcomeMessage').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('currentPage').textContent = '统计分析';
                switchTab('stats');
                
                hideLoading();
                
                // 滚动到结果区域
                setTimeout(() => {
                    scrollToSection('resultsSection');
                }, 100);
            }, 500); // 模拟处理时间
        }

        function prepareDataPoints(product1, product2, product3, observed) {
            // 获取表格数据（包含时间戳）
            const product1Data = getTableData('product1');
            const product2Data = getTableData('product2');
            const product3Data = getTableData('product3');
            const observedData = getTableData('observed');

            // 贝叶斯模型平均(BMA)融合
            const fusedData = calculateBMAFusion(product1, product2, product3, observedData.map(d => d.value));

            allDataPoints = [];
            for (let i = 0; i < product1.length; i++) {
                // 使用表格中的时间戳，如果没有则生成默认时间戳
                let timeStr = '';
                if (product1Data[i] && product1Data[i].timestamp) {
                    timeStr = product1Data[i].timestamp;
                } else {
                    // 回退到默认时间生成
                    const startTime = new Date(document.getElementById('startTime').value || '2024-01-01T09:00');
                    const frequency = parseInt(document.getElementById('frequency').value || 1);
                    const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                    timeStr = currentTime.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }

                const dataPoint = {
                    time: timeStr,
                    product1: product1[i],
                    product2: product2[i],
                    product3: product3[i],
                    fusedResult: fusedData[i]
                };

                if (observed.length > 0) {
                    dataPoint.observed = observed[i];
                }

                allDataPoints.push(dataPoint);
            }

            // 重置分页
            currentPage = 1;
        }        
        
        function displayStatsTable(stats) {
            const container = document.getElementById('statsTableContainer');
            
            let html = `
                <div class="table-container">
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th>数据源</th>
                                <th>均值 (mm)</th>
                                <th>标准差 (mm)</th>
                                <th>最小值 (mm)</th>
                                <th>最大值 (mm)</th>
                                <th>非零数量</th>
                                <th>总数量</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            stats.forEach(stat => {
                html += `
                    <tr>
                        <td style="font-weight: 600;">${stat.name}</td>
                        <td>${stat.mean}</td>
                        <td>${stat.std}</td>
                        <td>${stat.min}</td>
                        <td>${stat.max}</td>
                        <td>${stat.nonZeroCount}</td>
                        <td>${stat.total}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
        }

        // 创建统计分析图表
        let statsCharts = {};

        function createStatsCharts(product1, product2, product3, observed) {
            // 销毁已存在的图表
            Object.values(statsCharts).forEach(chart => {
                if (chart) chart.destroy();
            });
            statsCharts = {};

            // 从表格数据生成时间标签
            const product1Data = getTableData('product1');
            const labels = [];

            if (product1Data.length > 0 && product1Data[0].timestamp) {
                // 使用表格中的时间戳
                for (let i = 0; i < product1.length; i++) {
                    if (product1Data[i] && product1Data[i].timestamp) {
                        const timestamp = product1Data[i].timestamp;
                        // 格式化时间显示
                        const date = new Date(timestamp);
                        labels.push(date.toLocaleString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        }));
                    } else {
                        labels.push(`时次${i + 1}`);
                    }
                }
            } else {
                // 回退到默认时间生成
                const startTime = new Date(document.getElementById('startTime').value || '2024-01-01T09:00');
                const frequency = parseInt(document.getElementById('frequency').value || 1);

                for (let i = 0; i < product1.length; i++) {
                    const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                    labels.push(currentTime.toLocaleString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    }));
                }
            }

            // 创建产品1图表
            createSingleChart('product1Chart', '产品1预报数据', product1, labels, '#ff8c00');

            // 创建产品2图表
            createSingleChart('product2Chart', '产品2预报数据', product2, labels, '#9b59b6');

            // 创建产品3图表
            createSingleChart('product3Chart', '产品3预报数据', product3, labels, '#3498db');

            // 创建实测雨量图表（如果有数据）
            if (observed.length > 0) {
                createSingleChart('observedChart', '实测雨量数据', observed, labels, '#27ae60');
            } else {
                // 如果没有实测数据，显示提示
                const ctx = document.getElementById('observedChart');
                if (ctx) {
                    const container = ctx.parentElement;
                    container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; font-size: 14px;">暂无实测雨量数据</div>';
                }
            }
        }

        function createSingleChart(canvasId, title, data, labels, color) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;

            statsCharts[canvasId] = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: title,
                        data: data,
                        borderColor: color,
                        backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                        borderWidth: 1,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                        pointStyle: false,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '降雨量 (mm)',
                                font: {
                                    size: 11
                                }
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间',
                                font: {
                                    size: 11
                                }
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                font: {
                                    size: 9
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }        function performDataFusion() {
            // 首先检查数据是否已输入
            const product1Data = getTableData('product1');
            const product2Data = getTableData('product2');
            const product3Data = getTableData('product3');

            if (product1Data.length === 0 || product2Data.length === 0 || product3Data.length === 0) {
                showError('请确保至少输入三个预报产品的数据！');
                return;
            }

            if (product1Data.length !== product2Data.length || product2Data.length !== product3Data.length) {
                showError('三个预报产品的数据长度必须一致！');
                return;
            }

            // 显示融合配置弹窗
            showFusionModal();
        }

        // 显示融合配置弹窗
        function showFusionModal() {
            document.getElementById('fusionModal').style.display = 'block';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        // 关闭融合配置弹窗
        function closeFusionModal() {
            document.getElementById('fusionModal').style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        }

        // 选择融合方法
        function selectFusionMethod(element, method) {
            // 移除所有选中状态
            document.querySelectorAll('.fusion-method-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');

            // 选中对应的radio按钮
            element.querySelector('input[type="radio"]').checked = true;

            // 显示/隐藏自定义权重输入区域
            const customWeightsSection = document.getElementById('customWeightsSection');
            if (method === 'custom_weights') {
                customWeightsSection.style.display = 'block';
            } else {
                customWeightsSection.style.display = 'none';
            }
        }

        // 验证自定义权重
        function validateCustomWeights() {
            const weight1 = parseFloat(document.getElementById('weight1').value) || 0;
            const weight2 = parseFloat(document.getElementById('weight2').value) || 0;
            const weight3 = parseFloat(document.getElementById('weight3').value) || 0;

            const sum = weight1 + weight2 + weight3;
            const sumElement = document.getElementById('weightSum');
            const validationElement = document.getElementById('weightValidation');

            sumElement.textContent = sum.toFixed(2);

            if (Math.abs(sum - 1.0) < 0.01) {
                validationElement.style.background = '#e8f5e8';
                validationElement.style.color = '#2d5a2d';
                validationElement.innerHTML = `✅ 权重总和: <span id="weightSum">${sum.toFixed(2)}</span> (应等于1.00)`;
            } else {
                validationElement.style.background = '#ffe8e8';
                validationElement.style.color = '#8b0000';
                validationElement.innerHTML = `❌ 权重总和: <span id="weightSum">${sum.toFixed(2)}</span> (应等于1.00)`;
            }
        }

        // 确认融合配置并开始融合
        function confirmFusionConfig() {
            const watershed = document.getElementById('watershedSelect').value;
            const fusionMethod = document.querySelector('input[name="fusionMethod"]:checked').value;

            // 关闭弹窗
            closeFusionModal();

            // 开始融合处理
            executeFusion(watershed, fusionMethod);
        }

        // 执行数据融合
        function executeFusion(watershed, fusionMethod) {
            const methodNames = {
                'custom_weights': '自定义权重法',
                'weighted_average': '产品误差反比权重法',
                'linear_regression': '多元线性回归法',
                'bma': '贝叶斯模型平均法 (BMA)',
                'random_forest': '随机森林回归法',
                'two_stage_rf': '两阶段随机森林分类回归法',
                'mlp': '深度神经网络 (MLP)'
            };

            const watershedNames = {
                'lianghe': '两河',
                'yantang': '沿塘',
                'yinhe': '银河'
            };

            showLoading(`正在使用${methodNames[fusionMethod]}对${watershedNames[watershed]}流域数据进行融合...`);

            setTimeout(() => {
                // 从表格获取数据
                const product1Data = getTableData('product1');
                const product2Data = getTableData('product2');
                const product3Data = getTableData('product3');
                const observedData = getTableData('observed');

                // 提取数值数组
                const product1 = product1Data.map(item => item.value);
                const product2 = product2Data.map(item => item.value);
                const product3 = product3Data.map(item => item.value);
                const observed = observedData.map(item => item.value);

                if (observed.length > 0 && observed.length !== product1.length) {
                    hideLoading();
                    showError('实测雨量数据长度必须与预报数据一致！');
                    return;
                }

                // 根据选择的方法进行融合
                let fusedData;
                switch(fusionMethod) {
                    case 'custom_weights':
                        const customWeights = [
                            parseFloat(document.getElementById('weight1').value) || 0.33,
                            parseFloat(document.getElementById('weight2').value) || 0.33,
                            parseFloat(document.getElementById('weight3').value) || 0.34
                        ];
                        fusedData = calculateCustomWeightedAverage(product1, product2, product3, customWeights);
                        break;
                    case 'weighted_average':
                        fusedData = calculateErrorBasedWeights(product1, product2, product3, observed);
                        break;
                    case 'linear_regression':
                        fusedData = calculateLinearRegression(product1, product2, product3, observed);
                        break;
                    case 'bma':
                        fusedData = calculateBMAFusion(product1, product2, product3, observed);
                        break;
                    case 'random_forest':
                        fusedData = calculateRandomForest(product1, product2, product3, observed);
                        break;
                    case 'two_stage_rf':
                        fusedData = calculateTwoStageRF(product1, product2, product3, observed);
                        break;
                    case 'mlp':
                        fusedData = calculateMLP(product1, product2, product3, observed);
                        break;
                    default:
                        fusedData = calculateBMAFusion(product1, product2, product3, observed);
                }

                // 准备所有数据点
                prepareDataPoints(product1, product2, product3, observed);
                displayFusionResults(product1, product2, product3, fusedData, observed, fusionMethod, watershed);
                createFusionChart(product1, product2, product3, fusedData, observed);
                displayRawDataPreview();

                if (observed.length > 0) {
                    calculateAndDisplayMetrics(product1, product2, product3, fusedData, observed);
                } else {
                    displayNAMetrics();
                }

                document.getElementById('welcomeMessage').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('currentPage').textContent = '融合结果';
                switchTab('fusion');

                hideLoading();

                // 滚动到结果区域
                setTimeout(() => {
                    scrollToSection('resultsSection');
                }, 100);
            }, 1200); // 模拟处理时间
        }

        function displayFusionResults(product1, product2, product3, fusedData, observed, fusionMethod = 'bma', watershed = 'lianghe') {
            const container = document.getElementById('fusionResults');

            const methodNames = {
                'custom_weights': '自定义权重法',
                'weighted_average': '产品误差反比权重法',
                'linear_regression': '多元线性回归法',
                'bma': '贝叶斯模型平均法 (BMA)',
                'random_forest': '随机森林回归法',
                'two_stage_rf': '两阶段随机森林分类回归法',
                'mlp': '深度神经网络 (MLP)'
            };

            const watershedNames = {
                'lianghe': '两河',
                'yantang': '沿塘',
                'yinhe': '银河'
            };

            // 计算权重用于显示（根据方法不同显示不同信息）
            let weights = [1/3, 1/3, 1/3]; // 默认等权重

            if (fusionMethod === 'custom_weights') {
                weights = [
                    parseFloat(document.getElementById('weight1').value) || 0.33,
                    parseFloat(document.getElementById('weight2').value) || 0.33,
                    parseFloat(document.getElementById('weight3').value) || 0.34
                ];
            } else if (observed && observed.length > 0 && (fusionMethod === 'bma' || fusionMethod === 'linear_regression')) {
                const models = [product1, product2, product3];
                weights = calculateBMAWeights(models, observed);
            } else if (fusionMethod === 'weighted_average' && observed && observed.length > 0) {
                // 计算误差反比权重
                weights = calculateErrorBasedWeightsOnly(product1, product2, product3, observed);
            }

            let html = `
                <div class="fusion-result">
                    <h4>🔄 ${methodNames[fusionMethod]}融合结果</h4>
                    <div style="margin-top: 15px; padding: 15px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #667eea;">
                        <!-- 流域信息和融合方法横向布局 -->
                        <div style="display: flex; gap: 20px; align-items: center;">
                            <div style="flex: 1;">
                                <h5 style="margin: 0 0 10px 0; color: #2c3e50;">🏞️ 流域信息</h5>
                                <div style="background: white; padding: 10px; border-radius: 6px;">
                                    <strong>目标流域：</strong> ${watershedNames[watershed]} (${watershed.charAt(0).toUpperCase() + watershed.slice(1)})
                                </div>
                            </div>
                            <div style="flex: 1;">
                                <h5 style="margin: 0 0 10px 0; color: #2c3e50;">🔧 融合方法</h5>
                                <div style="background: white; padding: 10px; border-radius: 6px;">
                                    <strong>算法：</strong> ${methodNames[fusionMethod]}
                                </div>
                            </div>
                        </div>`;

            html += `
                    </div>
                    <div style="display: flex; gap: 15px; margin-top: 15px; flex-wrap: wrap;">
                        <button type="button" style="background: #28a745; color: white; padding: 10px 20px; border-radius: 8px; border: none; cursor: pointer; font-size: 14px; transition: all 0.3s ease;" onclick="exportFusionData('csv')" onmouseover="this.style.background='#218838'" onmouseout="this.style.background='#28a745'">
                            📊 导出CSV格式
                        </button>
                        <button type="button" style="background: #17a2b8; color: white; padding: 10px 20px; border-radius: 8px; border: none; cursor: pointer; font-size: 14px; transition: all 0.3s ease;" onclick="exportFusionData('json')" onmouseover="this.style.background='#138496'" onmouseout="this.style.background='#17a2b8'">
                            📋 导出JSON格式
                        </button>
                        <button type="button" style="background: #6f42c1; color: white; padding: 10px 20px; border-radius: 8px; border: none; cursor: pointer; font-size: 14px; transition: all 0.3s ease;" onclick="exportFusionData('txt')" onmouseover="this.style.background='#5a32a3'" onmouseout="this.style.background='#6f42c1'">
                            📄 导出TXT格式
                        </button>
                    </div>
                    <div style="margin-top: 15px; font-size: 14px; color: #666;">
                        融合结果包含 ${fusedData.length} 个时次的预报数据
                    </div>
                </div>
            `;

            if (observed.length === 0) {
                html += `
                    <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 10px; border-left: 5px solid #ffc107;">
                        <strong>💡 提示:</strong> 未提供实测雨量数据，仅显示融合结果。如需评估融合效果，请输入实测雨量数据。
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        function createFusionChart(product1, product2, product3, fusedData, observed) {
            const ctx = document.getElementById('fusionChart').getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            // 从表格数据生成时间标签
            const product1Data = getTableData('product1');
            const labels = [];

            if (product1Data.length > 0 && product1Data[0].timestamp) {
                // 使用表格中的时间戳
                for (let i = 0; i < product1.length; i++) {
                    if (product1Data[i] && product1Data[i].timestamp) {
                        const timestamp = product1Data[i].timestamp;
                        // 格式化时间显示
                        const date = new Date(timestamp);
                        labels.push(date.toLocaleString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        }));
                    } else {
                        labels.push(`时次${i + 1}`);
                    }
                }
            } else {
                // 回退到默认时间生成
                const startTime = new Date(document.getElementById('startTime').value || '2024-01-01T09:00');
                const frequency = parseInt(document.getElementById('frequency').value || 1);

                for (let i = 0; i < product1.length; i++) {
                    const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                    labels.push(currentTime.toLocaleString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    }));
                }
            }
            
            const datasets = [
                {
                    label: '产品1',
                    data: product1,
                    borderColor: '#ff8c00',
                    backgroundColor: 'rgba(255, 140, 0, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    borderDash: [5, 5]
                },
                {
                    label: '产品2',
                    data: product2,
                    borderColor: '#9b59b6',
                    backgroundColor: 'rgba(155, 89, 182, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    borderDash: [5, 5]
                },
                {
                    label: '产品3',
                    data: product3,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    borderDash: [5, 5]
                },
                {
                    label: '融合结果',
                    data: fusedData,
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.2)',
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    pointBackgroundColor: '#e74c3c'
                }
            ];

            if (observed.length > 0) {
                datasets.push({
                    label: '实测雨量',
                    data: observed,
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.2)',
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    pointBackgroundColor: '#27ae60'
                });
            }

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '多源降雨产品融合对比图 (BMA)',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '降雨量 (mm)',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        function calculateMetrics(forecast, observed) {
            const n = forecast.length;
            
            // 相关系数
            const meanForecast = forecast.reduce((a, b) => a + b, 0) / n;
            const meanObserved = observed.reduce((a, b) => a + b, 0) / n;
            
            let numerator = 0;
            let denomForecast = 0;
            let denomObserved = 0;
            
            for (let i = 0; i < n; i++) {
                numerator += (forecast[i] - meanForecast) * (observed[i] - meanObserved);
                denomForecast += Math.pow(forecast[i] - meanForecast, 2);
                denomObserved += Math.pow(observed[i] - meanObserved, 2);
            }
            
            const correlation = numerator / Math.sqrt(denomForecast * denomObserved);
            
            // 均方根误差
            const rmse = Math.sqrt(forecast.reduce((sum, val, i) => sum + Math.pow(val - observed[i], 2), 0) / n);
            
            // 纳什效率系数
            const ssRes = forecast.reduce((sum, val, i) => sum + Math.pow(observed[i] - val, 2), 0);
            const ssTot = observed.reduce((sum, val) => sum + Math.pow(val - meanObserved, 2), 0);
            const nash = 1 - (ssRes / ssTot);
            
            return {
                correlation: correlation.toFixed(4),
                rmse: rmse.toFixed(4),
                nash: nash.toFixed(4)
            };
        }

        // 简单加权平均法
        function calculateWeightedAverage(product1, product2, product3) {
            // 等权重融合
            const weights = [1/3, 1/3, 1/3];
            return product1.map((val, index) => {
                return weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];
            });
        }

        // 多元线性回归法
        function calculateLinearRegression(product1, product2, product3, observed) {
            if (observed.length === 0) {
                // 如果没有观测数据，使用等权重
                return calculateWeightedAverage(product1, product2, product3);
            }

            // 简化的线性回归实现
            // 在实际应用中，这里应该使用更复杂的回归算法
            const n = product1.length;
            let sumX1 = 0, sumX2 = 0, sumX3 = 0, sumY = 0;
            let sumX1Y = 0, sumX2Y = 0, sumX3Y = 0;
            let sumX1X1 = 0, sumX2X2 = 0, sumX3X3 = 0;

            for (let i = 0; i < n; i++) {
                sumX1 += product1[i];
                sumX2 += product2[i];
                sumX3 += product3[i];
                sumY += observed[i];
                sumX1Y += product1[i] * observed[i];
                sumX2Y += product2[i] * observed[i];
                sumX3Y += product3[i] * observed[i];
                sumX1X1 += product1[i] * product1[i];
                sumX2X2 += product2[i] * product2[i];
                sumX3X3 += product3[i] * product3[i];
            }

            // 简化的权重计算
            const w1 = Math.max(0, sumX1Y / sumX1X1 || 0);
            const w2 = Math.max(0, sumX2Y / sumX2X2 || 0);
            const w3 = Math.max(0, sumX3Y / sumX3X3 || 0);

            const totalWeight = w1 + w2 + w3;
            const weights = totalWeight > 0 ? [w1/totalWeight, w2/totalWeight, w3/totalWeight] : [1/3, 1/3, 1/3];

            return product1.map((val, index) => {
                return weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];
            });
        }

        // 随机森林回归法（简化实现）
        function calculateRandomForest(product1, product2, product3, observed) {
            if (observed.length === 0) {
                // 如果没有观测数据，使用等权重
                return calculateWeightedAverage(product1, product2, product3);
            }

            // 简化的随机森林实现
            // 实际应用中应该使用专门的机器学习库
            const weights = calculateBMAWeights([product1, product2, product3], observed);

            // 添加一些非线性调整
            return product1.map((val, index) => {
                const linear = weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];
                // 简单的非线性调整
                const adjustment = Math.sin(linear * 0.1) * 0.1;
                return Math.max(0, linear + adjustment);
            });
        }

        // 两阶段随机森林分类回归法（简化实现）
        function calculateTwoStageRF(product1, product2, product3, observed) {
            if (observed.length === 0) {
                return calculateWeightedAverage(product1, product2, product3);
            }

            // 第一阶段：分类（降雨/无降雨）
            // 第二阶段：回归（降雨量预测）
            const threshold = 0.1; // 降雨阈值

            return product1.map((val, index) => {
                const avg = (product1[index] + product2[index] + product3[index]) / 3;

                // 分类阶段：判断是否有降雨
                if (avg < threshold) {
                    return 0;
                }

                // 回归阶段：预测降雨量
                const weights = calculateBMAWeights([product1, product2, product3], observed);
                const regression = weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];

                // 添加分类信息的调整
                const classificationBonus = avg > threshold * 2 ? 1.1 : 1.0;
                return regression * classificationBonus;
            });
        }

        // 深度神经网络MLP方法（简化实现）
        function calculateMLP(product1, product2, product3, observed) {
            if (observed.length === 0) {
                return calculateWeightedAverage(product1, product2, product3);
            }

            // 简化的MLP实现
            // 实际应用中应该使用TensorFlow.js或其他深度学习框架
            const weights = calculateBMAWeights([product1, product2, product3], observed);

            return product1.map((val, index) => {
                // 输入层
                const input1 = product1[index];
                const input2 = product2[index];
                const input3 = product3[index];

                // 隐藏层（简化的激活函数）
                const hidden1 = Math.tanh(weights[0] * input1 + weights[1] * input2 + 0.5);
                const hidden2 = Math.tanh(weights[1] * input2 + weights[2] * input3 + 0.5);
                const hidden3 = Math.tanh(weights[2] * input3 + weights[0] * input1 + 0.5);

                // 输出层
                const output = Math.max(0, (hidden1 + hidden2 + hidden3) / 3 *
                    (weights[0] * input1 + weights[1] * input2 + weights[2] * input3));

                return output;
            });
        }

        // 贝叶斯模型平均(BMA)融合算法
        function calculateBMAFusion(product1, product2, product3, observed) {
            const n = product1.length;

            // 如果没有观测数据，使用等权重
            if (!observed || observed.length === 0) {
                const equalWeight = 1/3;
                return product1.map((val, index) => {
                    return equalWeight * product1[index] + equalWeight * product2[index] + equalWeight * product3[index];
                });
            }

            // 计算每个模型的似然函数和后验权重
            const models = [product1, product2, product3];
            const weights = calculateBMAWeights(models, observed);

            // 使用BMA权重进行融合
            const fusedData = product1.map((val, index) => {
                return weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];
            });

            return fusedData;
        }

        // 计算BMA权重 - 稳健版本
        function calculateBMAWeights(models, observed) {
            const numModels = models.length;
            const n = observed.length;

            if (n === 0) {
                // 如果没有观测数据，返回等权重
                return new Array(numModels).fill(1 / numModels);
            }

            // 计算各模型的性能指标
            const performances = models.map(model => {
                const residuals = model.map((pred, i) => pred - observed[i]);
                const mse = residuals.reduce((sum, r) => sum + r * r, 0) / n;
                const mae = residuals.reduce((sum, r) => sum + Math.abs(r), 0) / n;
                const bias = residuals.reduce((sum, r) => sum + r, 0) / n;

                // 计算相关系数
                const meanObs = observed.reduce((sum, val) => sum + val, 0) / n;
                const meanPred = model.reduce((sum, val) => sum + val, 0) / n;

                let numerator = 0, denomObs = 0, denomPred = 0;
                for (let i = 0; i < n; i++) {
                    const obsDeviation = observed[i] - meanObs;
                    const predDeviation = model[i] - meanPred;
                    numerator += obsDeviation * predDeviation;
                    denomObs += obsDeviation * obsDeviation;
                    denomPred += predDeviation * predDeviation;
                }

                const correlation = denomObs > 0 && denomPred > 0 ?
                    numerator / Math.sqrt(denomObs * denomPred) : 0;

                return { mse, mae, bias: Math.abs(bias), correlation };
            });

            // 基于多个指标计算综合得分
            const scores = performances.map(perf => {
                // 归一化各指标（越小越好的指标取倒数）
                const mseScore = 1 / (1 + perf.mse);
                const maeScore = 1 / (1 + perf.mae);
                const biasScore = 1 / (1 + perf.bias);
                const corrScore = Math.max(0, perf.correlation); // 相关系数越大越好

                // 综合得分（可调整权重）
                return 0.3 * mseScore + 0.3 * maeScore + 0.2 * biasScore + 0.2 * corrScore;
            });

            // 使用流域特定的参数或默认参数
            const basinConfig = window.currentBasinConfig || {
                temperature: 2.0,
                minWeight: 0.15,
                maxWeight: 0.70
            };

            // 使用温度参数控制的softmax计算权重
            const temperature = basinConfig.temperature; // 温度参数，越大权重分布越平均
            const maxScore = Math.max(...scores);
            const expScores = scores.map(score => Math.exp((score - maxScore) / temperature));
            const sumExpScores = expScores.reduce((sum, exp) => sum + exp, 0);

            let weights = expScores.map(exp => exp / sumExpScores);

            // 应用权重约束：确保没有权重过小或过大
            const minWeight = basinConfig.minWeight; // 流域特定的最小权重
            const maxWeight = basinConfig.maxWeight; // 流域特定的最大权重

            // 第一步：确保最小权重
            weights = weights.map(w => Math.max(w, minWeight));
            let weightSum = weights.reduce((sum, w) => sum + w, 0);
            weights = weights.map(w => w / weightSum);

            // 第二步：限制最大权重
            for (let i = 0; i < weights.length; i++) {
                if (weights[i] > maxWeight) {
                    const excess = weights[i] - maxWeight;
                    weights[i] = maxWeight;

                    // 将多余的权重分配给其他模型
                    const otherIndices = weights.map((_, idx) => idx).filter(idx => idx !== i);
                    const redistributeWeight = excess / otherIndices.length;
                    otherIndices.forEach(idx => {
                        weights[idx] += redistributeWeight;
                    });
                }
            }

            // 最终归一化
            weightSum = weights.reduce((sum, w) => sum + w, 0);
            weights = weights.map(w => w / weightSum);

            // 输出调试信息
            console.log('模型性能指标:');
            performances.forEach((perf, i) => {
                console.log(`产品${i+1}: MSE=${perf.mse.toFixed(4)}, MAE=${perf.mae.toFixed(4)}, Bias=${perf.bias.toFixed(4)}, Corr=${perf.correlation.toFixed(4)}`);
            });
            console.log('综合得分:', scores.map(s => s.toFixed(4)));
            console.log('BMA权重计算结果:', weights.map(w => (w * 100).toFixed(1) + '%'));

            return weights;
        }

        function calculateAndDisplayMetrics(product1, product2, product3, fusedData, observed) {
            const metrics1 = calculateMetrics(product1, observed);
            const metrics2 = calculateMetrics(product2, observed);
            const metrics3 = calculateMetrics(product3, observed);
            const metricsFused = calculateMetrics(fusedData, observed);

            const container = document.getElementById('metricsTableContainer');
            
            const html = `
                <div class="table-container">
                    <table class="metrics-table">
                        <thead>
                            <tr>
                                <th>产品</th>
                                <th>纳什效率系数 (NSE)</th>
                                <th>相关系数 (R)</th>
                                <th>均方根误差 (RMSE)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="font-weight: 600;">产品1</td>
                                <td>${metrics1.nash}</td>
                                <td>${metrics1.correlation}</td>
                                <td>${metrics1.rmse}</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600;">产品2</td>
                                <td>${metrics2.nash}</td>
                                <td>${metrics2.correlation}</td>
                                <td>${metrics2.rmse}</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600;">产品3</td>
                                <td>${metrics3.nash}</td>
                                <td>${metrics3.correlation}</td>
                                <td>${metrics3.rmse}</td>
                            </tr>
                            <tr style="background: #e8f5e8; font-weight: 600;">
                                <td style="color: #2e7d32;">融合产品</td>
                                <td style="color: #2e7d32;">${metricsFused.nash}</td>
                                <td style="color: #2e7d32;">${metricsFused.correlation}</td>
                                <td style="color: #2e7d32;">${metricsFused.rmse}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div style="margin-top: 15px; padding: 15px; background: #f0f8ff; border-radius: 10px; border-left: 5px solid #2196f3;">
                    <strong>📊 指标说明:</strong>
                    <ul style="margin-top: 10px; margin-left: 20px;">
                        <li><strong>NSE (纳什效率系数):</strong> 值越接近1表示预报效果越好</li>
                        <li><strong>R (相关系数):</strong> 值越接近1表示相关性越强</li>
                        <li><strong>RMSE (均方根误差):</strong> 值越小表示预报误差越小</li>
                    </ul>
                </div>
            `;

            container.innerHTML = html;
        }

        function displayNAMetrics() {
            const container = document.getElementById('metricsTableContainer');

            const html = `
                <div class="table-container">
                    <table class="metrics-table">
                        <thead>
                            <tr>
                                <th>产品</th>
                                <th>纳什效率系数 (NSE)</th>
                                <th>相关系数 (R)</th>
                                <th>均方根误差 (RMSE)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="font-weight: 600;">产品1</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600;">产品2</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600;">产品3</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                            </tr>
                            <tr style="background: #f5f5f5; font-weight: 600;">
                                <td style="color: #666;">融合产品</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                                <td style="color: #999;">NA</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 10px; border-left: 5px solid #ffc107;">
                    <strong>⚠️ 提示:</strong> 由于未提供实测雨量数据，无法计算精度评估指标。
                    <ul style="margin-top: 10px; margin-left: 20px;">
                        <li><strong>NSE (纳什效率系数):</strong> 需要实测数据进行对比计算</li>
                        <li><strong>R (相关系数):</strong> 需要实测数据进行相关性分析</li>
                        <li><strong>RMSE (均方根误差):</strong> 需要实测数据计算预报误差</li>
                    </ul>
                </div>
            `;

            container.innerHTML = html;
        }        function switchTab(tabName) {
            // 切换标签页样式
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            if (tabName === 'stats') {
                document.querySelector('.tab:nth-child(1)').classList.add('active');
                document.getElementById('statsContent').classList.add('active');
                document.getElementById('currentPage').textContent = '统计分析';
            } else if (tabName === 'fusion') {
                document.querySelector('.tab:nth-child(2)').classList.add('active');
                document.getElementById('fusionContent').classList.add('active');
                document.getElementById('currentPage').textContent = '融合结果';
            } else if (tabName === 'detailed') {
                document.querySelector('.tab:nth-child(3)').classList.add('active');
                document.getElementById('detailedContent').classList.add('active');
                document.getElementById('currentPage').textContent = '详细数据';
                // 显示分页表格
                displayPaginatedTable();
            }
        }

        function showError(message) {
            const resultsSection = document.getElementById('resultsSection');
            resultsSection.innerHTML = `
                <div class="error-message">
                    <strong>❌ 错误:</strong> ${message}
                </div>
            `;
            resultsSection.style.display = 'block';
        }        // 示例数据填充
        function fillSampleData() {
            // 生成更多示例数据以展示分页功能
            const generateRandomData = (baseValue, variation, count) => {
                const data = [];
                for (let i = 0; i < count; i++) {
                    const value = baseValue + (Math.random() - 0.5) * variation;
                    data.push(Math.max(0, value).toFixed(1));
                }
                return data.join('\n');
            };

            document.getElementById('product1Data').value = generateRandomData(1.5, 3.0, 25);
            document.getElementById('product2Data').value = generateRandomData(1.3, 2.8, 25);
            document.getElementById('product3Data').value = generateRandomData(1.7, 3.2, 25);
            document.getElementById('observedData').value = generateRandomData(1.6, 2.9, 25);
        }        // 页面加载完成后填充示例数据
        window.addEventListener('load', function() {
            fillSampleData();
            initScrollEffects();
        });

        // 滚动效果初始化
        function initScrollEffects() {
            const backToTop = document.getElementById('backToTop');
            const indicators = document.querySelectorAll('.indicator-dot');
            
            window.addEventListener('scroll', function() {
                // 回到顶部按钮显示/隐藏
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('visible');
                } else {
                    backToTop.classList.remove('visible');
                }
                
                // 更新页面指示器
                updatePageIndicator();
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId) || document.querySelector('.' + sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        function updatePageIndicator() {
            const indicators = document.querySelectorAll('.indicator-dot');
            const sections = [
                document.querySelector('.header'),
                document.querySelector('.input-section'),
                document.getElementById('resultsSection')
            ];
            
            let currentSection = 0;
            const scrollPosition = window.pageYOffset + window.innerHeight / 2;
            
            sections.forEach((section, index) => {
                if (section && section.offsetTop <= scrollPosition) {
                    currentSection = index;
                }
            });
            
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentSection);
            });
        }

        // 导出融合数据功能
        function exportFusionData(format) {
            const product1 = parseData(document.getElementById('product1Data').value);
            const product2 = parseData(document.getElementById('product2Data').value);
            const product3 = parseData(document.getElementById('product3Data').value);
            const observed = parseData(document.getElementById('observedData').value);

            if (product1.length === 0 || product2.length === 0 || product3.length === 0) {
                alert('请先进行数据融合！');
                return;
            }

            // 重新计算BMA融合数据
            const fusedData = calculateBMAFusion(product1, product2, product3, observed);

            // 生成时间序列
            const startTime = new Date(document.getElementById('startTime').value);
            const frequency = parseInt(document.getElementById('frequency').value);
            const timeLabels = [];
            
            for (let i = 0; i < fusedData.length; i++) {
                const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                timeLabels.push(currentTime.toISOString().slice(0, 16).replace('T', ' '));
            }

            let content = '';
            let filename = '';
            let mimeType = '';

            if (format === 'csv') {
                content = '时间,产品1,产品2,产品3,融合结果';
                if (observed.length > 0) content += ',实测雨量';
                content += '\n';
                
                for (let i = 0; i < fusedData.length; i++) {
                    content += `${timeLabels[i]},${product1[i].toFixed(3)},${product2[i].toFixed(3)},${product3[i].toFixed(3)},${fusedData[i].toFixed(3)}`;
                    if (observed.length > 0) content += `,${observed[i].toFixed(3)}`;
                    content += '\n';
                }
                filename = '降雨融合结果.csv';
                mimeType = 'text/csv;charset=utf-8;';
            } else if (format === 'json') {
                // 计算BMA权重
                let bmaWeights = [1/3, 1/3, 1/3];
                if (observed && observed.length > 0) {
                    const models = [product1, product2, product3];
                    bmaWeights = calculateBMAWeights(models, observed);
                }

                const jsonData = {
                    metadata: {
                        startTime: document.getElementById('startTime').value,
                        endTime: document.getElementById('endTime').value,
                        frequency: frequency + '小时',
                        exportTime: new Date().toISOString(),
                        dataPoints: fusedData.length,
                        fusionMethod: 'Bayesian Model Averaging (BMA)',
                        bmaWeights: {
                            product1: parseFloat(bmaWeights[0].toFixed(4)),
                            product2: parseFloat(bmaWeights[1].toFixed(4)),
                            product3: parseFloat(bmaWeights[2].toFixed(4))
                        }
                    },
                    data: timeLabels.map((time, i) => {
                        const point = {
                            time: time,
                            product1: parseFloat(product1[i].toFixed(3)),
                            product2: parseFloat(product2[i].toFixed(3)),
                            product3: parseFloat(product3[i].toFixed(3)),
                            fusedResult: parseFloat(fusedData[i].toFixed(3))
                        };
                        if (observed.length > 0) {
                            point.observed = parseFloat(observed[i].toFixed(3));
                        }
                        return point;
                    })
                };
                content = JSON.stringify(jsonData, null, 2);
                filename = '降雨融合结果.json';
                mimeType = 'application/json;charset=utf-8;';
            } else if (format === 'txt') {
                // 计算BMA权重
                let bmaWeights = [1/3, 1/3, 1/3];
                if (observed && observed.length > 0) {
                    const models = [product1, product2, product3];
                    bmaWeights = calculateBMAWeights(models, observed);
                }

                content = '多源降雨产品融合结果 (贝叶斯模型平均)\n';
                content += '=' .repeat(60) + '\n';
                content += `起始时间: ${document.getElementById('startTime').value}\n`;
                content += `结束时间: ${document.getElementById('endTime').value}\n`;
                content += `预报频率: ${frequency}小时\n`;
                content += `数据点数: ${fusedData.length}\n`;
                content += `融合方法: 贝叶斯模型平均 (BMA)\n`;
                content += `导出时间: ${new Date().toLocaleString('zh-CN')}\n\n`;
                content += 'BMA权重分配:\n';
                content += `-`.repeat(20) + '\n';
                content += `产品1权重: ${(bmaWeights[0] * 100).toFixed(2)}%\n`;
                content += `产品2权重: ${(bmaWeights[1] * 100).toFixed(2)}%\n`;
                content += `产品3权重: ${(bmaWeights[2] * 100).toFixed(2)}%\n\n`;
                content += '详细数据:\n';
                content += `-`.repeat(20) + '\n';
                
                for (let i = 0; i < fusedData.length; i++) {
                    content += `时间: ${timeLabels[i]}\n`;
                    content += `  产品1: ${product1[i].toFixed(3)} mm\n`;
                    content += `  产品2: ${product2[i].toFixed(3)} mm\n`;
                    content += `  产品3: ${product3[i].toFixed(3)} mm\n`;
                    content += `  融合结果: ${fusedData[i].toFixed(3)} mm\n`;
                    if (observed.length > 0) {
                        content += `  实测雨量: ${observed[i].toFixed(3)} mm\n`;
                    }
                    content += '\n';
                }
                filename = '降雨融合结果.txt';
                mimeType = 'text/plain;charset=utf-8;';
            }

            // 创建下载链接
            const blob = new Blob(['\ufeff' + content], { type: mimeType });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();            document.body.removeChild(link);
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            // Ctrl + 数字键切换标签页
            if (e.ctrlKey) {
                if (e.key === '1') {
                    e.preventDefault();
                    if (document.getElementById('resultsSection').style.display !== 'none') {
                        switchTab('stats');
                    }
                } else if (e.key === '2') {
                    e.preventDefault();
                    if (document.getElementById('resultsSection').style.display !== 'none') {
                        switchTab('fusion');
                    }
                } else if (e.key === '3') {
                    e.preventDefault();
                    if (document.getElementById('resultsSection').style.display !== 'none') {
                        switchTab('detailed');
                    }
                }
            }
            
            // 分页导航
            if (document.getElementById('detailedContent').classList.contains('active')) {
                if (e.key === 'ArrowLeft' && currentPage > 1) {
                    e.preventDefault();
                    changePage(currentPage - 1);
                } else if (e.key === 'ArrowRight') {
                    const totalPages = Math.ceil(allDataPoints.length / itemsPerPage);
                    if (currentPage < totalPages) {
                        e.preventDefault();
                        changePage(currentPage + 1);
                    }
                }
            }
            
            // ESC键返回主页或关闭弹窗
            if (e.key === 'Escape') {
                const fusionModal = document.getElementById('fusionModal');
                if (fusionModal.style.display === 'block') {
                    closeFusionModal();
                } else {
                    showMainView();
                }
            }
        });

        // 点击弹窗外部关闭弹窗
        document.getElementById('fusionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeFusionModal();
            }
        });

        // 自定义权重融合计算
        function calculateCustomWeightedAverage(product1, product2, product3, weights) {
            const fusedData = [];
            const minLength = Math.min(product1.length, product2.length, product3.length);

            for (let i = 0; i < minLength; i++) {
                const fusedValue = weights[0] * product1[i].value +
                                 weights[1] * product2[i].value +
                                 weights[2] * product3[i].value;

                fusedData.push({
                    timestamp: product1[i].timestamp,
                    value: parseFloat(fusedValue.toFixed(3))
                });
            }

            return fusedData;
        }

        // 误差反比权重融合计算
        function calculateErrorBasedWeights(product1, product2, product3, observed) {
            if (!observed || observed.length === 0) {
                // 如果没有实测数据，使用等权重
                return calculateCustomWeightedAverage(product1, product2, product3, [1/3, 1/3, 1/3]);
            }

            const weights = calculateErrorBasedWeightsOnly(product1, product2, product3, observed);
            return calculateCustomWeightedAverage(product1, product2, product3, weights);
        }

        // 仅计算误差反比权重（不进行融合）
        function calculateErrorBasedWeightsOnly(product1, product2, product3, observed) {
            if (!observed || observed.length === 0) {
                return [1/3, 1/3, 1/3];
            }

            const minLength = Math.min(product1.length, product2.length, product3.length, observed.length);

            // 计算每个产品的RMSE
            let rmse1 = 0, rmse2 = 0, rmse3 = 0;

            for (let i = 0; i < minLength; i++) {
                rmse1 += Math.pow(product1[i].value - observed[i].value, 2);
                rmse2 += Math.pow(product2[i].value - observed[i].value, 2);
                rmse3 += Math.pow(product3[i].value - observed[i].value, 2);
            }

            rmse1 = Math.sqrt(rmse1 / minLength);
            rmse2 = Math.sqrt(rmse2 / minLength);
            rmse3 = Math.sqrt(rmse3 / minLength);

            // 避免除零错误，给RMSE加一个小的常数
            const epsilon = 0.001;
            rmse1 += epsilon;
            rmse2 += epsilon;
            rmse3 += epsilon;

            // 计算反比权重
            const invRmse1 = 1 / rmse1;
            const invRmse2 = 1 / rmse2;
            const invRmse3 = 1 / rmse3;

            const totalInvRmse = invRmse1 + invRmse2 + invRmse3;

            return [
                invRmse1 / totalInvRmse,
                invRmse2 / totalInvRmse,
                invRmse3 / totalInvRmse
            ];
        }
    </script>

    <!-- 数据融合配置弹窗 -->
    <div id="fusionModal" class="fusion-modal">
        <div class="fusion-modal-content">
            <div class="fusion-modal-header">
                <h3>🔄 数据融合配置</h3>
                <span class="fusion-modal-close" onclick="closeFusionModal()">&times;</span>
            </div>
            <div class="fusion-modal-body">
                <div class="fusion-form-group">
                    <label class="fusion-form-label">🏞️ 选择流域</label>
                    <select id="watershedSelect" class="fusion-form-select">
                        <option value="lianghe">两河 (Lianghe)</option>
                        <option value="yantang">沿塘 (Yantang)</option>
                        <option value="yinhe">银河 (Yinhe)</option>
                    </select>
                </div>

                <div class="fusion-form-group">
                    <label class="fusion-form-label">🔧 选择融合方法</label>
                    <div class="fusion-method-grid">
                        <div class="fusion-method-option selected" onclick="selectFusionMethod(this, 'custom_weights')">
                            <input type="radio" name="fusionMethod" value="custom_weights" class="fusion-method-radio" checked>
                            <div class="fusion-method-info">
                                <div class="fusion-method-name">自定义权重法</div>
                                <div class="fusion-method-desc">用户自定义三种产品的权重进行线性组合</div>
                            </div>
                        </div>

                        <div class="fusion-method-option" onclick="selectFusionMethod(this, 'weighted_average')">
                            <input type="radio" name="fusionMethod" value="weighted_average" class="fusion-method-radio">
                            <div class="fusion-method-info">
                                <div class="fusion-method-name">产品误差反比权重法</div>
                                <div class="fusion-method-desc">基于各产品历史误差的反比计算权重进行融合</div>
                            </div>
                        </div>

                        <div class="fusion-method-option" onclick="selectFusionMethod(this, 'linear_regression')">
                            <input type="radio" name="fusionMethod" value="linear_regression" class="fusion-method-radio">
                            <div class="fusion-method-info">
                                <div class="fusion-method-name">多元线性回归法</div>
                                <div class="fusion-method-desc">基于历史数据建立线性回归模型进行融合</div>
                            </div>
                        </div>

                        <div class="fusion-method-option" onclick="selectFusionMethod(this, 'bma')">
                            <input type="radio" name="fusionMethod" value="bma" class="fusion-method-radio">
                            <div class="fusion-method-info">
                                <div class="fusion-method-name">贝叶斯模型平均法 (BMA)</div>
                                <div class="fusion-method-desc">基于贝叶斯理论的概率加权融合方法，自动优化权重</div>
                            </div>
                        </div>

                        <div class="fusion-method-option" onclick="selectFusionMethod(this, 'random_forest')">
                            <input type="radio" name="fusionMethod" value="random_forest" class="fusion-method-radio">
                            <div class="fusion-method-info">
                                <div class="fusion-method-name">随机森林回归法</div>
                                <div class="fusion-method-desc">使用随机森林算法进行非线性融合，适用于复杂关系</div>
                            </div>
                        </div>

                        <div class="fusion-method-option" onclick="selectFusionMethod(this, 'two_stage_rf')">
                            <input type="radio" name="fusionMethod" value="two_stage_rf" class="fusion-method-radio">
                            <div class="fusion-method-info">
                                <div class="fusion-method-name">两阶段随机森林分类回归法</div>
                                <div class="fusion-method-desc">先分类后回归的两阶段机器学习方法，提高预测精度</div>
                            </div>
                        </div>

                        <div class="fusion-method-option" onclick="selectFusionMethod(this, 'mlp')">
                            <input type="radio" name="fusionMethod" value="mlp" class="fusion-method-radio">
                            <div class="fusion-method-info">
                                <div class="fusion-method-name">深度神经网络 (MLP)</div>
                                <div class="fusion-method-desc">多层感知器神经网络，能够学习复杂的非线性映射关系</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自定义权重输入区域 -->
                <div class="fusion-form-group" id="customWeightsSection" style="display: block;">
                    <label class="fusion-form-label">⚖️ 自定义权重设置</label>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 10px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #2c3e50;">产品1权重</label>
                            <input type="number" id="weight1" min="0" max="1" step="0.01" value="0.33"
                                   style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;"
                                   onchange="validateCustomWeights()">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #2c3e50;">产品2权重</label>
                            <input type="number" id="weight2" min="0" max="1" step="0.01" value="0.33"
                                   style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;"
                                   onchange="validateCustomWeights()">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #2c3e50;">产品3权重</label>
                            <input type="number" id="weight3" min="0" max="1" step="0.01" value="0.34"
                                   style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;"
                                   onchange="validateCustomWeights()">
                        </div>
                    </div>
                    <div id="weightValidation" style="margin-top: 10px; padding: 8px; background: #e8f5e8; border-radius: 6px; font-size: 12px; color: #2d5a2d;">
                        ✅ 权重总和: <span id="weightSum">1.00</span> (应等于1.00)
                    </div>
                </div>
            </div>
            <div class="fusion-modal-footer">
                <button class="fusion-btn fusion-btn-cancel" onclick="closeFusionModal()">取消</button>
                <button class="fusion-btn fusion-btn-confirm" onclick="confirmFusionConfig()">开始融合</button>
            </div>
        </div>
    </div>
</body>
</html>