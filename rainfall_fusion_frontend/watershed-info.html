<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流域信息 - 多源降雨产品融合系统</title>
    <script src="auth.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1d1d1f;
        }

        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px 30px;
            flex-shrink: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 80px;
            display: flex;
            align-items: center;
        }

        .nav-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-left h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .nav-left p {
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0;
            color: white;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1em;
            color: white;
        }

        .user-welcome {
            opacity: 0.8;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.7;
            font-size: 1.0em;
        }

        .user-role::before {
            content: "(";
        }

        .user-role::after {
            content: ")";
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.2em;
        }

        .dropdown-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .hamburger {
            display: block;
            font-size: 1.1em;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 8px;
            background: white;
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            z-index: 1000;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .dropdown-content.show {
            display: block;
        }

        .dropdown-content a {
            display: block;
            padding: 12px 16px;
            color: #333;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }

        .dropdown-content a:hover {
            background: #f8f9fa;
        }

        .dropdown-content a:first-child {
            border-radius: 12px 12px 0 0;
        }

        .dropdown-content a:last-child {
            border-radius: 0 0 12px 12px;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dropdown-icon {
            font-size: 16px;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 80px;
            bottom: 0;
            width: 325px;
            background: #2c3e50;
            z-index: 999;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .menu-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            position: relative;
        }

        .menu-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #3498db;
        }

        .menu-link.active {
            background: #3498db;
            color: white;
        }

        .menu-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #e74c3c;
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .menu-text {
            flex: 1;
        }

        .menu-arrow {
            font-size: 10px;
            color: #95a5a6;
            transition: transform 0.3s ease;
        }

        .menu-item.expanded .menu-arrow {
            transform: rotate(90deg);
        }

        .submenu {
            background: #34495e;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .menu-item.expanded .submenu {
            max-height: 200px;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 50px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 13px;
            border-left: 3px solid transparent;
        }

        .submenu-link:hover {
            background: rgba(255, 255, 255, 0.05);
            color: #3498db;
            border-left-color: #3498db;
        }

        .submenu-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-left-color: #3498db;
        }

        /* 主内容区域 */
        .main-wrapper {
            margin-left: 325px;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
            transition: margin-left 0.3s ease;
        }

        .main-content {
            padding: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            min-height: calc(100vh - 80px);
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 10px;
        }

        .page-subtitle {
            font-size: 18px;
            color: #86868b;
        }

        /* 流域信息网格 */
        .watershed-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .watershed-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .watershed-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .watershed-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-bottom: 1px solid #f0f0f0;
        }

        .watershed-info {
            padding: 25px;
            text-align: center;
        }

        .watershed-name {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0 0 8px 0;
        }

        .watershed-area {
            font-size: 16px;
            color: #86868b;
            margin: 0 0 20px 0;
        }

        .watershed-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .edit-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .edit-btn:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .edit-icon {
            font-size: 16px;
        }

        .download-btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .download-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        .download-icon {
            font-size: 16px;
        }

        /* 图片模态框样式 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            max-width: 1000px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .modal-image {
            max-width: 100%;
            max-height: 80%;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
        }

        .modal-title {
            color: white;
            font-size: 28px;
            font-weight: 600;
            margin-top: 20px;
            text-align: center;
        }

        .close-modal {
            position: absolute;
            top: 20px;
            right: 30px;
            color: white;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close-modal:hover {
            color: #3498db;
        }

        /* 编辑模态框样式 */
        .edit-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .edit-modal-content {
            position: relative;
            background: white;
            margin: 10% auto;
            padding: 0;
            width: 90%;
            max-width: 500px;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .edit-modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .edit-modal-header h3 {
            margin: 0;
            color: #1d1d1f;
            font-size: 20px;
            font-weight: 600;
        }

        .edit-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #86868b;
            transition: color 0.3s ease;
        }

        .edit-modal-close:hover {
            color: #1d1d1f;
        }

        .edit-modal-body {
            padding: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #1d1d1f;
            font-weight: 500;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #007aff;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .edit-modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn-primary {
            background: #007aff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-wrapper {
                margin-left: 0;
            }

            .main-content {
                padding: 20px;
            }

            .watershed-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .modal-content {
                padding: 10px;
            }

            .modal-image {
                max-height: 70%;
            }

            .modal-title {
                font-size: 24px;
            }

            .close-modal {
                top: 10px;
                right: 20px;
                font-size: 30px;
            }
        }

        @media (max-width: 1024px) and (min-width: 769px) {
            .watershed-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 20px;
            }
            .page-title {
                font-size: 24px;
            }

            .page-subtitle {
                font-size: 16px;
            }
        }

        .mobile-menu-toggle {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }

            .nav-left {
                display: flex;
                align-items: center;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <button class="mobile-menu-toggle" onclick="toggleSidebar()">☰</button>
                    <h1>🏞️ 流域信息</h1>
                    <p>Watershed Information</p>
                </div>
                <div class="nav-right">
                    <div class="user-section">
                        <div class="user-info" id="userInfo" style="display: none;">
                            <span class="user-welcome">欢迎，</span>
                            <span class="user-name" id="userName"></span>
                            <span class="user-role" id="userRole"></span>
                        </div>
                        <div class="dropdown" id="userDropdown" style="display: none;">
                            <button class="dropdown-btn" onclick="toggleDropdown()">
                                <span class="hamburger">☰</span>
                            </button>
                            <div class="dropdown-content" id="dropdownContent">
                                <a href="#" class="dropdown-item" onclick="showPersonalInfo()">
                                    <span class="dropdown-icon">👤</span>
                                    个人信息管理
                                </a>
                                <a href="#" class="dropdown-item" onclick="AuthManager.logout()">
                                    <span class="dropdown-icon">🚪</span>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 左侧菜单栏 -->
        <div class="sidebar" id="sidebar">
            <ul class="sidebar-menu">
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('fusion')">
                        <span class="menu-icon">🌧️</span>
                        <span class="menu-text">降雨产品融合</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="showModule('flow-prediction')">
                        <span class="menu-icon">📊</span>
                        <span class="menu-text">流量预测系统</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link active" onclick="showModule('watershed-info')">
                        <span class="menu-icon">🏞️</span>
                        <span class="menu-text">流域信息</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                        <span class="menu-icon">🔧</span>
                        <span class="menu-text">模型管理</span>
                        <span class="menu-arrow">▶</span>
                    </a>
                    <div class="submenu">
                        <a href="#" class="submenu-link" onclick="showModule('model-fusion')">
                            <span class="menu-text">多源融合模型</span>
                        </a>
                        <a href="#" class="submenu-link" onclick="showModule('model-flow')">
                            <span class="menu-text">流量预测模型</span>
                        </a>
                    </div>
                </li>
                <li class="menu-item admin-only" id="userManagementMenu" style="display: none;">
                    <a href="user-management.html" class="menu-link">
                        <span class="menu-icon">👥</span>
                        <span class="menu-text">用户管理</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-wrapper">
            <div class="main-content">
                <div class="page-header">
                    <h1 class="page-title">流域信息</h1>
                    <p class="page-subtitle">Watershed Information System</p>
                </div>

                <div class="watershed-grid">
                    <!-- 两河流域 -->
                    <div class="watershed-card">
                        <img src="lianghe.png" alt="两河流域" class="watershed-image" onclick="openImageModal('lianghe.png', '两河流域')">
                        <div class="watershed-info">
                            <h2 class="watershed-name">两河流域</h2>
                            <p class="watershed-area">流域面积：157 平方千米</p>
                            <div class="watershed-actions">
                                <button class="edit-btn" onclick="editWatershedInfo('lianghe', '两河流域', '157')">
                                    <span class="edit-icon">✏️</span>
                                    编辑信息
                                </button>
                                <button class="download-btn" onclick="downloadWatershedBoundary('lianghe')">
                                    <span class="download-icon">📥</span>
                                    下载流域边界
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 沿塘流域 -->
                    <div class="watershed-card">
                        <img src="yantang.png" alt="沿塘流域" class="watershed-image" onclick="openImageModal('yantang.png', '沿塘流域')">
                        <div class="watershed-info">
                            <h2 class="watershed-name">沿塘流域</h2>
                            <p class="watershed-area">流域面积：599 平方千米</p>
                            <div class="watershed-actions">
                                <button class="edit-btn" onclick="editWatershedInfo('yantang', '沿塘流域', '599')">
                                    <span class="edit-icon">✏️</span>
                                    编辑信息
                                </button>
                                <button class="download-btn" onclick="downloadWatershedBoundary('yantang')">
                                    <span class="download-icon">📥</span>
                                    下载流域边界
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 银河流域 -->
                    <div class="watershed-card">
                        <img src="yinhe.png" alt="银河流域" class="watershed-image" onclick="openImageModal('yinhe.png', '银河流域')">
                        <div class="watershed-info">
                            <h2 class="watershed-name">银河流域</h2>
                            <p class="watershed-area">流域面积：62.4 平方千米</p>
                            <div class="watershed-actions">
                                <button class="edit-btn" onclick="editWatershedInfo('yinhe', '银河流域', '62.4')">
                                    <span class="edit-icon">✏️</span>
                                    编辑信息
                                </button>
                                <button class="download-btn" onclick="downloadWatershedBoundary('yinhe')">
                                    <span class="download-icon">📥</span>
                                    下载流域边界
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片模态框 -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <span class="close-modal" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <h2 id="modalTitle" class="modal-title"></h2>
        </div>
    </div>

    <!-- 编辑流域信息模态框 -->
    <div id="editModal" class="edit-modal" onclick="closeEditModal()">
        <div class="edit-modal-content" onclick="event.stopPropagation()">
            <div class="edit-modal-header">
                <h3>编辑流域信息</h3>
                <button class="edit-modal-close" onclick="closeEditModal()">&times;</button>
            </div>
            <div class="edit-modal-body">
                <form id="editWatershedForm">
                    <div class="form-group">
                        <label for="editWatershedName">流域名称:</label>
                        <input type="text" id="editWatershedName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="editWatershedArea">流域面积 (平方千米):</label>
                        <input type="number" id="editWatershedArea" class="form-control" step="0.1" required>
                    </div>
                </form>
            </div>
            <div class="edit-modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveWatershedInfo()">保存修改</button>
            </div>
        </div>
    </div>

    <script>
        function displayUserInfo() {
            const user = AuthManager.getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.username;
                document.getElementById('userRole').textContent = user.role === 'admin' ? '管理员' : '普通用户';
                document.getElementById('userInfo').style.display = 'flex';
                document.getElementById('userDropdown').style.display = 'block';

                // 如果是管理员，显示用户管理功能
                if (user.role === 'admin') {
                    // 显示左侧菜单中的用户管理
                    document.getElementById('userManagementMenu').style.display = 'block';
                }
            }
        }

        function initializePermissions() {
            const user = AuthManager.getCurrentUser();
            if (!user) return;

            // 显示管理员菜单
            if (user.role === 'admin') {
                document.getElementById('userManagementMenu').style.display = 'block';
            }
        }

        // 切换下拉菜单
        function toggleDropdown() {
            const dropdown = document.getElementById('dropdownContent');
            if (dropdown.style.display === 'none' || dropdown.style.display === '') {
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('dropdownContent');
            const dropdownBtn = document.querySelector('.dropdown-btn');

            if (!dropdownBtn.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });

        // 切换侧边栏（移动端）
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // 切换子菜单
        function toggleSubmenu(element) {
            const menuItem = element.parentElement;
            menuItem.classList.toggle('expanded');
        }

        // 显示模块
        function showModule(moduleName) {
            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-link, .submenu-link').forEach(link => {
                link.classList.remove('active');
            });

            // 根据模块名称跳转到相应页面
            switch(moduleName) {
                case 'fusion':
                    window.location.href = '11.html';
                    break;
                case 'flow-prediction':
                    window.location.href = 'flow_prediction.html';
                    break;
                case 'watershed-info':
                    // 当前页面，保持active状态
                    document.querySelector('[onclick="showModule(\'watershed-info\')"]').classList.add('active');
                    break;
                case 'model-fusion':
                case 'model-flow':
                    // 跳转到相应的模型管理页面
                    window.location.href = '11.html';
                    break;
                case 'user-management':
                    window.location.href = 'user-management.html';
                    break;
            }

            // 在移动端关闭侧边栏
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        }

        // 显示个人信息管理
        function showPersonalInfo() {
            // 这里可以添加个人信息管理的弹窗或跳转逻辑
            alert('个人信息管理功能');
        }

        // 打开图片模态框
        function openImageModal(imageSrc, title) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');

            modalImage.src = imageSrc;
            modalImage.alt = title;
            modalTitle.textContent = title;
            modal.style.display = 'block';

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        // 关闭图片模态框
        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';

            // 恢复背景滚动
            document.body.style.overflow = 'auto';
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
                closeEditModal();
            }
        });

        // 当前编辑的流域信息
        let currentEditingWatershed = null;

        // 编辑流域信息
        function editWatershedInfo(watershedId, watershedName, watershedArea) {
            currentEditingWatershed = watershedId;

            document.getElementById('editWatershedName').value = watershedName;
            document.getElementById('editWatershedArea').value = watershedArea;

            document.getElementById('editModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭编辑模态框
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            currentEditingWatershed = null;
        }

        // 保存流域信息修改
        function saveWatershedInfo() {
            const newName = document.getElementById('editWatershedName').value.trim();
            const newArea = document.getElementById('editWatershedArea').value.trim();

            if (!newName || !newArea) {
                alert('请填写完整的流域名称和面积');
                return;
            }

            if (isNaN(newArea) || parseFloat(newArea) <= 0) {
                alert('请输入有效的流域面积');
                return;
            }

            // 找到对应的流域卡片并更新信息
            const watershedCards = document.querySelectorAll('.watershed-card');
            watershedCards.forEach(card => {
                const editBtn = card.querySelector('.edit-btn');
                if (editBtn && editBtn.getAttribute('onclick').includes(currentEditingWatershed)) {
                    // 更新流域名称
                    const nameElement = card.querySelector('.watershed-name');
                    if (nameElement) {
                        nameElement.textContent = newName;
                    }

                    // 更新流域面积
                    const areaElement = card.querySelector('.watershed-area');
                    if (areaElement) {
                        areaElement.textContent = `流域面积：${newArea} 平方千米`;
                    }

                    // 更新编辑按钮的参数
                    editBtn.setAttribute('onclick', `editWatershedInfo('${currentEditingWatershed}', '${newName}', '${newArea}')`);
                }
            });

            // 显示成功消息
            alert('流域信息更新成功！');

            // 关闭模态框
            closeEditModal();
        }

        // 下载流域边界文件
        function downloadWatershedBoundary(watershedName) {
            // 阻止事件冒泡，避免触发卡片的点击事件
            event.stopPropagation();

            // 定义流域边界文件映射
            const boundaryFiles = {
                'lianghe': {
                    filename: '两河流域边界.shp',
                    url: 'boundaries/lianghe_boundary.shp'
                },
                'yantang': {
                    filename: '沿塘流域边界.shp',
                    url: 'boundaries/yantang_boundary.shp'
                },
                'yinhe': {
                    filename: '银河流域边界.shp',
                    url: 'boundaries/yinhe_boundary.shp'
                }
            };

            const boundary = boundaryFiles[watershedName];
            if (boundary) {
                // 创建下载链接
                const link = document.createElement('a');
                link.href = boundary.url;
                link.download = boundary.filename;
                link.style.display = 'none';

                // 添加到页面并触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示下载提示
                alert(`正在下载 ${boundary.filename}`);
            } else {
                alert('流域边界文件不存在');
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('userData');
            window.location.href = 'index.html';
        }



        // 认证检查和用户界面初始化
        window.addEventListener('load', function() {
            // 初始化默认用户（仅用于演示）
            AuthManager.initializeDefaultUsers();

            // 如果没有登录用户，自动登录管理员（仅用于演示）
            if (!AuthManager.getCurrentUser()) {
                AuthManager.login('admin', '123456');
            }

            // 检查用户是否已登录
            if (!AuthManager.requireLogin()) {
                return;
            }

            // 显示用户信息
            displayUserInfo();

            // 初始化权限控制
            initializePermissions();
        });
    </script>
</body>
</html>
