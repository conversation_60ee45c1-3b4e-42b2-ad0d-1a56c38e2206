#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制三个流域数据的tp和flow时间序列图
按照要求：tp变量以顶部框线为横轴，从上到下递增绘制
flow变量以底部框线为横轴，从下到上递增绘制
时间作为横轴
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np

# 设置字体，避免中文显示问题
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_watershed_tp_flow(watershed_name, file_path):
    """在一张图上绘制流域tp和flow数据，tp从上到下递增，flow从下到上递增"""
    
    # 读取数据
    try:
        df = pd.read_csv(file_path)
        print(f"Successfully loaded {watershed_name} data with {len(df)} rows")
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    # 转换日期格式
    df['time'] = pd.to_datetime(df['time'], format='%Y/%m/%d %H:%M')
    
    # 创建图形，设置较大的尺寸
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 绘制flow数据（底部，从下到上递增）
    color_flow = 'green'
    ax.plot(df['time'], df['flow'], color=color_flow, linewidth=1.5, label='Flow', alpha=0.8)
    ax.set_xlabel('Time', fontsize=14, fontweight='bold')
    ax.set_ylabel('Flow (m³/s)', color=color_flow, fontsize=14, fontweight='bold')
    ax.tick_params(axis='y', labelcolor=color_flow, labelsize=12)
    ax.tick_params(axis='x', labelsize=12)
    
    # 设置flow的y轴范围，确保从底部开始
    flow_min = df['flow'].min()
    flow_max = df['flow'].max()
    ax.set_ylim(flow_min * 0.95, flow_max * 1.05)
    
    # 创建第二个y轴用于tp数据（顶部）
    ax2 = ax.twinx()
    
    # 绘制tp数据（顶部，从上到下递增）
    color_tp = 'blue'
    ax2.plot(df['time'], df['tp'], color=color_tp, linewidth=1.5, label='Precipitation', alpha=0.8)
    ax2.set_ylabel('Precipitation (mm)', color=color_tp, fontsize=14, fontweight='bold')
    ax2.tick_params(axis='y', labelcolor=color_tp, labelsize=12)
    
    # 反转tp的y轴，使其从上到下递增（顶部框线为横轴）
    ax2.invert_yaxis()
    
    # 设置tp的y轴范围
    tp_max = df['tp'].max()
    if tp_max > 0:
        ax2.set_ylim(tp_max * 1.1, -tp_max * 0.05)
    else:
        ax2.set_ylim(1, -0.1)
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 设置标题
    ax.set_title(f'{watershed_name.title()} Watershed: Time Series of Precipitation (top-down) and Flow (bottom-up)', 
                fontsize=16, fontweight='bold', pad=20)
    
    # 设置x轴日期格式
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 添加图例
    lines1, labels1 = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines1 + lines2, labels1 + labels2, 
             loc='upper left', fontsize=12, framealpha=0.9)
    
    # 添加说明文字
    ax.text(0.02, 0.98, 'Precipitation: Top frame as baseline, increasing downward', 
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
    
    ax.text(0.02, 0.02, 'Flow: Bottom frame as baseline, increasing upward', 
            transform=ax.transAxes, fontsize=10, verticalalignment='bottom',
            bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    
    # 调整布局
    plt.tight_layout()
    
    # 显示统计信息
    print(f"Data time range: {df['time'].min()} to {df['time'].max()}")
    print(f"Precipitation range: {df['tp'].min():.3f} - {df['tp'].max():.3f} mm")
    print(f"Flow range: {df['flow'].min():.2f} - {df['flow'].max():.2f} m³/s")
    print(f"Total data points: {len(df)}")
    
    # 保存图像
    output_filename = f'{watershed_name}_final_plot.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"{watershed_name.title()} image saved as {output_filename}")
    
    # 显示图像
    plt.show()

def create_watershed_summary(watershed_name, file_path):
    """创建流域数据摘要统计"""
    try:
        df = pd.read_csv(file_path)
        df['time'] = pd.to_datetime(df['time'], format='%Y/%m/%d %H:%M')
        
        print(f"\n=== {watershed_name.title()} Watershed Data Summary Statistics ===")
        print(f"Dataset: {file_path}")
        print(f"Time period: {df['time'].min()} to {df['time'].max()}")
        print(f"Total records: {len(df)}")
        print(f"Time span: {(df['time'].max() - df['time'].min()).days} days")
        
        print("\nPrecipitation (tp) Statistics:")
        print(f"  Min: {df['tp'].min():.3f} mm")
        print(f"  Max: {df['tp'].max():.3f} mm")
        print(f"  Mean: {df['tp'].mean():.3f} mm")
        print(f"  Std: {df['tp'].std():.3f} mm")
        print(f"  Non-zero values: {(df['tp'] > 0).sum()} ({(df['tp'] > 0).sum()/len(df)*100:.1f}%)")
        
        print("\nFlow Statistics:")
        print(f"  Min: {df['flow'].min():.2f} m³/s")
        print(f"  Max: {df['flow'].max():.2f} m³/s")
        print(f"  Mean: {df['flow'].mean():.2f} m³/s")
        print(f"  Std: {df['flow'].std():.2f} m³/s")
        
        # 找出最大降水和流量事件
        max_tp_idx = df['tp'].idxmax()
        max_flow_idx = df['flow'].idxmax()
        
        print(f"\nMaximum precipitation event:")
        print(f"  Date: {df.loc[max_tp_idx, 'time']}")
        print(f"  Precipitation: {df.loc[max_tp_idx, 'tp']:.3f} mm")
        print(f"  Flow at that time: {df.loc[max_tp_idx, 'flow']:.2f} m³/s")
        
        print(f"\nMaximum flow event:")
        print(f"  Date: {df.loc[max_flow_idx, 'time']}")
        print(f"  Flow: {df.loc[max_flow_idx, 'flow']:.2f} m³/s")
        print(f"  Precipitation at that time: {df.loc[max_flow_idx, 'tp']:.3f} mm")
        
    except Exception as e:
        print(f"Error in creating summary: {e}")

def plot_separated_charts(watershed_name, file_path):
    """绘制分离式图表（两个子图）"""
    try:
        df = pd.read_csv(file_path)
        df['time'] = pd.to_datetime(df['time'], format='%Y/%m/%d %H:%M')
        
        # 创建图形和子图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
        
        # 绘制tp数据（顶部子图，y轴反转）
        ax1.plot(df['time'], df['tp'], color='blue', linewidth=1, label='Precipitation (tp)')
        ax1.set_ylabel('Precipitation (mm)', fontsize=12)
        ax1.set_title(f'{watershed_name.title()} Watershed: Time Series of Precipitation and Flow', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper right')
        
        # 反转tp的y轴，使其从上到下递增
        ax1.invert_yaxis()
        
        # 设置tp的y轴范围，确保0在顶部
        tp_max = df['tp'].max()
        if tp_max > 0:
            ax1.set_ylim(tp_max * 1.1, -tp_max * 0.05)
        else:
            ax1.set_ylim(1, -0.1)
        
        # 绘制flow数据（底部子图）
        ax2.plot(df['time'], df['flow'], color='green', linewidth=1, label='Flow (flow)')
        ax2.set_ylabel('Flow (m³/s)', fontsize=12)
        ax2.set_xlabel('Time', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='upper right')
        
        # 设置x轴日期格式
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        output_filename = f'{watershed_name}_separated_plot.png'
        plt.savefig(output_filename, dpi=300, bbox_inches='tight')
        print(f"{watershed_name.title()} separated chart saved as {output_filename}")
        
        # 显示图像
        plt.show()
        
    except Exception as e:
        print(f"Error in creating separated charts: {e}")

def plot_all_watersheds():
    """绘制所有三个流域的数据"""
    watersheds = {
        'lianghe': 'Final_data/lianghe_final(时间换算后).csv',
        'yantang': 'Final_data/yantang_final(时间换算后).csv',
        'yinhe': 'Final_data/yinhe_final(时间换算后).csv'
    }
    
    for watershed_name, file_path in watersheds.items():
        print(f"\n{'='*60}")
        print(f"Processing {watershed_name.upper()} watershed...")
        print(f"{'='*60}")
        
        # 创建主要绘图
        print(f"\nCreating {watershed_name} final plot with tp (top-down) and flow (bottom-up)...")
        plot_watershed_tp_flow(watershed_name, file_path)
        
        # 生成统计摘要
        print(f"\nGenerating {watershed_name} summary statistics...")
        create_watershed_summary(watershed_name, file_path)
        
        # 创建分离式图表
        print(f"\nCreating {watershed_name} separated charts...")
        plot_separated_charts(watershed_name, file_path)

if __name__ == "__main__":
    print("Creating watershed plots for Lianghe, Yantang, and Yinhe watersheds...")
    print("Data includes time series of precipitation (tp) and flow data")
    plot_all_watersheds()
