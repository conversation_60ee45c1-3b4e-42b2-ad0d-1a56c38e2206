<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMA融合算法测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .weights-display {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .weight-bar {
            flex: 1;
            height: 30px;
            background: #e9ecef;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .weight-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #17a2b8, #ffc107);
            transition: width 0.3s ease;
        }
        .weight-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BMA融合算法测试</h1>
        
        <div class="test-section">
            <h3>测试场景1：模拟数据测试</h3>
            <button onclick="testScenario1()">运行测试1</button>
            <div id="result1" class="result"></div>
            <div id="weights1" class="weights-display"></div>
        </div>

        <div class="test-section">
            <h3>测试场景2：一个模型明显更好</h3>
            <button onclick="testScenario2()">运行测试2</button>
            <div id="result2" class="result"></div>
            <div id="weights2" class="weights-display"></div>
        </div>

        <div class="test-section">
            <h3>测试场景3：所有模型表现相似</h3>
            <button onclick="testScenario3()">运行测试3</button>
            <div id="result3" class="result"></div>
            <div id="weights3" class="weights-display"></div>
        </div>

        <div class="test-section">
            <h3>测试场景4：真实数据模拟</h3>
            <button onclick="testScenario4()">运行测试4</button>
            <div id="result4" class="result"></div>
            <div id="weights4" class="weights-display"></div>
        </div>
    </div>

    <script>
        // 计算BMA权重 - 稳健版本
        function calculateBMAWeights(models, observed) {
            const numModels = models.length;
            const n = observed.length;

            if (n === 0) {
                // 如果没有观测数据，返回等权重
                return new Array(numModels).fill(1 / numModels);
            }

            // 计算各模型的性能指标
            const performances = models.map(model => {
                const residuals = model.map((pred, i) => pred - observed[i]);
                const mse = residuals.reduce((sum, r) => sum + r * r, 0) / n;
                const mae = residuals.reduce((sum, r) => sum + Math.abs(r), 0) / n;
                const bias = residuals.reduce((sum, r) => sum + r, 0) / n;

                // 计算相关系数
                const meanObs = observed.reduce((sum, val) => sum + val, 0) / n;
                const meanPred = model.reduce((sum, val) => sum + val, 0) / n;

                let numerator = 0, denomObs = 0, denomPred = 0;
                for (let i = 0; i < n; i++) {
                    const obsDeviation = observed[i] - meanObs;
                    const predDeviation = model[i] - meanPred;
                    numerator += obsDeviation * predDeviation;
                    denomObs += obsDeviation * obsDeviation;
                    denomPred += predDeviation * predDeviation;
                }

                const correlation = denomObs > 0 && denomPred > 0 ?
                    numerator / Math.sqrt(denomObs * denomPred) : 0;

                return { mse, mae, bias: Math.abs(bias), correlation };
            });

            // 基于多个指标计算综合得分
            const scores = performances.map(perf => {
                // 归一化各指标（越小越好的指标取倒数）
                const mseScore = 1 / (1 + perf.mse);
                const maeScore = 1 / (1 + perf.mae);
                const biasScore = 1 / (1 + perf.bias);
                const corrScore = Math.max(0, perf.correlation); // 相关系数越大越好

                // 综合得分（可调整权重）
                return 0.3 * mseScore + 0.3 * maeScore + 0.2 * biasScore + 0.2 * corrScore;
            });

            // 使用温度参数控制的softmax计算权重
            const temperature = 2.0; // 温度参数，越大权重分布越平均
            const maxScore = Math.max(...scores);
            const expScores = scores.map(score => Math.exp((score - maxScore) / temperature));
            const sumExpScores = expScores.reduce((sum, exp) => sum + exp, 0);

            let weights = expScores.map(exp => exp / sumExpScores);

            // 应用权重约束：确保没有权重过小或过大
            const minWeight = 0.15; // 最小权重15%
            const maxWeight = 0.70; // 最大权重70%

            // 第一步：确保最小权重
            weights = weights.map(w => Math.max(w, minWeight));
            let weightSum = weights.reduce((sum, w) => sum + w, 0);
            weights = weights.map(w => w / weightSum);

            // 第二步：限制最大权重
            for (let i = 0; i < weights.length; i++) {
                if (weights[i] > maxWeight) {
                    const excess = weights[i] - maxWeight;
                    weights[i] = maxWeight;

                    // 将多余的权重分配给其他模型
                    const otherIndices = weights.map((_, idx) => idx).filter(idx => idx !== i);
                    const redistributeWeight = excess / otherIndices.length;
                    otherIndices.forEach(idx => {
                        weights[idx] += redistributeWeight;
                    });
                }
            }

            // 最终归一化
            weightSum = weights.reduce((sum, w) => sum + w, 0);
            weights = weights.map(w => w / weightSum);

            // 输出调试信息
            console.log('模型性能指标:');
            performances.forEach((perf, i) => {
                console.log(`产品${i+1}: MSE=${perf.mse.toFixed(4)}, MAE=${perf.mae.toFixed(4)}, Bias=${perf.bias.toFixed(4)}, Corr=${perf.correlation.toFixed(4)}`);
            });
            console.log('综合得分:', scores.map(s => s.toFixed(4)));
            console.log('BMA权重计算结果:', weights.map(w => (w * 100).toFixed(1) + '%'));

            return weights;
        }

        function displayWeights(weights, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            weights.forEach((weight, index) => {
                const weightBar = document.createElement('div');
                weightBar.className = 'weight-bar';
                
                const weightFill = document.createElement('div');
                weightFill.className = 'weight-fill';
                weightFill.style.width = (weight * 100) + '%';
                weightFill.style.background = ['#28a745', '#17a2b8', '#ffc107'][index];
                
                const weightLabel = document.createElement('div');
                weightLabel.className = 'weight-label';
                weightLabel.textContent = `产品${index + 1}: ${(weight * 100).toFixed(1)}%`;
                
                weightBar.appendChild(weightFill);
                weightBar.appendChild(weightLabel);
                container.appendChild(weightBar);
            });
        }

        function testScenario1() {
            // 模拟数据：三个产品的预报值和观测值
            const observed = [10, 15, 8, 12, 20, 18, 5, 14, 16, 11];
            const product1 = [9, 14, 7, 11, 19, 17, 4, 13, 15, 10]; // 较好的预报
            const product2 = [12, 18, 10, 15, 23, 21, 8, 17, 19, 14]; // 中等预报
            const product3 = [8, 13, 6, 10, 18, 16, 3, 12, 14, 9]; // 较差的预报
            
            const models = [product1, product2, product3];
            const weights = calculateBMAWeights(models, observed);
            
            document.getElementById('result1').innerHTML = `
                权重分配: 产品1: ${(weights[0]*100).toFixed(1)}%, 产品2: ${(weights[1]*100).toFixed(1)}%, 产品3: ${(weights[2]*100).toFixed(1)}%<br>
                预期: 产品1应该获得最高权重，因为它与观测值最接近
            `;
            
            displayWeights(weights, 'weights1');
        }

        function testScenario2() {
            // 一个模型明显更好的情况
            const observed = [10, 15, 8, 12, 20, 18, 5, 14, 16, 11];
            const product1 = [10.1, 15.1, 7.9, 12.1, 20.1, 17.9, 5.1, 14.1, 15.9, 11.1]; // 非常接近观测
            const product2 = [15, 20, 13, 17, 25, 23, 10, 19, 21, 16]; // 系统性偏高
            const product3 = [5, 10, 3, 7, 15, 13, 0, 9, 11, 6]; // 系统性偏低
            
            const models = [product1, product2, product3];
            const weights = calculateBMAWeights(models, observed);
            
            document.getElementById('result2').innerHTML = `
                权重分配: 产品1: ${(weights[0]*100).toFixed(1)}%, 产品2: ${(weights[1]*100).toFixed(1)}%, 产品3: ${(weights[2]*100).toFixed(1)}%<br>
                预期: 产品1应该获得主导权重，但不应该是100%
            `;
            
            displayWeights(weights, 'weights2');
        }

        function testScenario3() {
            // 所有模型表现相似
            const observed = [10, 15, 8, 12, 20, 18, 5, 14, 16, 11];
            const product1 = [9.5, 14.5, 7.5, 11.5, 19.5, 17.5, 4.5, 13.5, 15.5, 10.5];
            const product2 = [10.5, 15.5, 8.5, 12.5, 20.5, 18.5, 5.5, 14.5, 16.5, 11.5];
            const product3 = [9.8, 14.8, 7.8, 11.8, 19.8, 17.8, 4.8, 13.8, 15.8, 10.8];
            
            const models = [product1, product2, product3];
            const weights = calculateBMAWeights(models, observed);
            
            document.getElementById('result3').innerHTML = `
                权重分配: 产品1: ${(weights[0]*100).toFixed(1)}%, 产品2: ${(weights[1]*100).toFixed(1)}%, 产品3: ${(weights[2]*100).toFixed(1)}%<br>
                预期: 权重应该相对均匀分布
            `;
            
            displayWeights(weights, 'weights3');
        }

        function testScenario4() {
            // 真实数据模拟：包含噪声和不确定性
            const observed = [2.5, 0.8, 4.2, 1.6, 3.1, 0.5, 2.8, 1.9, 3.7, 0.9, 2.2, 1.4, 3.5, 0.7, 2.6];
            const product1 = [2.8, 1.1, 4.5, 1.9, 3.4, 0.8, 3.1, 2.2, 4.0, 1.2, 2.5, 1.7, 3.8, 1.0, 2.9]; // GPM
            const product2 = [2.3, 0.6, 3.9, 1.3, 2.8, 0.3, 2.5, 1.6, 3.4, 0.7, 1.9, 1.1, 3.2, 0.4, 2.3]; // TRMM
            const product3 = [2.7, 0.9, 4.3, 1.7, 3.2, 0.6, 2.9, 2.0, 3.8, 1.0, 2.3, 1.5, 3.6, 0.8, 2.7]; // CMORPH
            
            const models = [product1, product2, product3];
            const weights = calculateBMAWeights(models, observed);
            
            document.getElementById('result4').innerHTML = `
                权重分配: 产品1: ${(weights[0]*100).toFixed(1)}%, 产品2: ${(weights[1]*100).toFixed(1)}%, 产品3: ${(weights[2]*100).toFixed(1)}%<br>
                预期: 基于实际表现的合理权重分配
            `;
            
            displayWeights(weights, 'weights4');
        }
    </script>
</body>
</html>
