#!/usr/bin/env python3
"""
批量处理China_Product/2024_final中的GRB2文件
使用shapefile裁剪每个文件，为三个流域（liangh<PERSON>、yanta<PERSON>、y<PERSON><PERSON>）生成裁剪后的数据
"""

import os
import xarray as xr
import geopandas as gpd
import rioxarray as rxr
from shapely.geometry import mapping
import pandas as pd
import numpy as np
from pathlib import Path
import traceback
import gc

def clip_grib_with_shapefile(grib_file, shapefile_path, output_csv=None):
    """
    使用shapefile裁剪GRIB2文件
    """
    ds = None
    gdf = None
    clipped_ds = None
    df = None

    try:
        ds = xr.open_dataset(grib_file, engine='cfgrib')
        ds = ds.rio.write_crs("EPSG:4326")
        
        gdf = gpd.read_file(shapefile_path)
        
        if gdf.crs is None:
            gdf = gdf.set_crs("EPSG:4326")
        elif gdf.crs.to_string() != "EPSG:4326":
            gdf = gdf.to_crs("EPSG:4326")

        clipped_ds = ds.rio.clip(gdf.geometry.apply(mapping), gdf.crs, drop=False)

        if output_csv:
            df = clipped_ds.to_dataframe().reset_index()
            df = df.dropna()

            if 'surface' in df.columns:
                df = df.drop('surface', axis=1)
            if 'spatial_ref' in df.columns:
                df = df.drop('spatial_ref', axis=1)
            df = df.rename(columns={'unknown': 'tp'})

            df = df[['time', 'step', 'valid_time', 'latitude', 'longitude', 'tp']]
            df.to_csv(output_csv, index=False)
            result = (clipped_ds, df)
        else:
            result = clipped_ds

    finally:
        if ds is not None:
            ds.close()
            del ds
        if gdf is not None:
            del gdf
        if clipped_ds is not None and output_csv:
            del clipped_ds
        if df is not None:
            del df
        gc.collect()

    return result

def check_files_exist(date_folder_path, watersheds, grb2_file):
    """
    检查该日期文件夹中是否已经存在所有流域的CSV文件
    """
    for watershed_name in watersheds.keys():
        output_filename = f"{watershed_name}_{grb2_file.stem}.csv"
        output_file_path = date_folder_path / output_filename
        if not output_file_path.exists():
            return False
    return True

def process_single_date_folder(date_folder_path, watersheds):
    """
    处理单个日期文件夹中的GRB2文件
    """
    results = {
        'date': date_folder_path.name,
        'success': 0,
        'failed': 0,
        'errors': [],
        'skipped': 0
    }

    grb2_files = list(date_folder_path.glob("*.GRB2"))

    if not grb2_files:
        results['errors'].append("未找到GRB2文件")
        results['failed'] = 1
        return results

    if len(grb2_files) > 1:
        results['errors'].append(f"找到多个GRB2文件: {[f.name for f in grb2_files]}")
        results['failed'] = 1
        return results

    grb2_file = grb2_files[0]

    if check_files_exist(date_folder_path, watersheds, grb2_file):
        results['success'] = len(watersheds)
        results['skipped'] = len(watersheds)
        return results
    
    for watershed_name, shapefile_path in watersheds.items():
        try:
            output_filename = f"{watershed_name}_{grb2_file.stem}.csv"
            output_file_path = date_folder_path / output_filename
            
            if output_file_path.exists():
                results['success'] += 1
                results['skipped'] += 1
                continue

            _, clipped_df = clip_grib_with_shapefile(
                str(grb2_file),
                shapefile_path,
                str(output_file_path)
            )

            results['success'] += 1
            
        except Exception as e:
            error_msg = f"{watershed_name}: {str(e)}"
            results['errors'].append(error_msg)
            results['failed'] += 1
    
    return results

def batch_process_grb2_files(source_dir, watersheds, max_folders=None):
    """
    批量处理所有日期文件夹中的GRB2文件
    """
    source_path = Path(source_dir)
    
    if not source_path.exists():
        return None
    
    date_folders = [f for f in source_path.iterdir() if f.is_dir() and f.name.isdigit()]
    date_folders.sort()
    
    if max_folders:
        date_folders = date_folders[:max_folders]
    
    total_stats = {
        'total_folders': len(date_folders),
        'processed_folders': 0,
        'total_success': 0,
        'total_failed': 0,
        'failed_folders': [],
        'watershed_stats': {name: {'success': 0, 'failed': 0} for name in watersheds.keys()}
    }
    
    for i, date_folder in enumerate(date_folders, 1):
        try:
            results = process_single_date_folder(date_folder, watersheds)
            
            total_stats['processed_folders'] += 1
            total_stats['total_success'] += results['success']
            total_stats['total_failed'] += results['failed']
            
            for watershed_name in watersheds.keys():
                watershed_success = sum(1 for error in results['errors'] if not error.startswith(watershed_name))
                watershed_failed = sum(1 for error in results['errors'] if error.startswith(watershed_name))
                
                if results['success'] > 0:
                    total_stats['watershed_stats'][watershed_name]['success'] += 1
                if watershed_failed > 0:
                    total_stats['watershed_stats'][watershed_name]['failed'] += 1
            
            if results['failed'] > 0:
                total_stats['failed_folders'].append({
                    'date': results['date'],
                    'errors': results['errors']
                })
                
        except Exception as e:
            total_stats['failed_folders'].append({
                'date': date_folder.name,
                'errors': [f"文件夹处理失败: {str(e)}"]
            })
    
    return total_stats

if __name__ == "__main__":
    watersheds = {
        'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        'yinhe': '/home/<USER>/Flood_flow_prediction/boundary/yinhe.shp'
    }
    
    source_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    
    test_results = batch_process_grb2_files(source_directory, watersheds, max_folders=5)
    
    if test_results and test_results['total_success'] > 0:
        final_results = batch_process_grb2_files(source_directory, watersheds)
