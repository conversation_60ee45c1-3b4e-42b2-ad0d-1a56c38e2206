"""
BMA (Bayesian Model Averaging) 多源降水产品融合模型
使用贝叶斯模型平均方法融合三个降水产品，以实测雨量作为标签
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class BMAFusion:
    def __init__(self):
        self.weights = None
        self.models = {}
        self.variance = None
        
    def fit(self, X, y):
        """
        训练BMA模型
        X: 输入特征 (产品1, 产品2, 产品3)
        y: 目标变量 (实测雨量)
        """
        n_models = X.shape[1]

        # 直接使用产品预测值，不需要额外的线性回归
        predictions = X.copy()

        # 计算每个产品的性能指标（负MSE作为似然函数）
        likelihoods = np.zeros(n_models)
        for i in range(n_models):
            mse = mean_squared_error(y, predictions[:, i])
            # 使用负MSE作为似然函数的对数
            likelihoods[i] = -mse

        # 计算权重 (softmax归一化)
        exp_likelihoods = np.exp(likelihoods - np.max(likelihoods))
        self.weights = exp_likelihoods / np.sum(exp_likelihoods)

        # 计算模型方差
        weighted_pred = np.sum(predictions * self.weights, axis=1)
        self.variance = np.mean((y - weighted_pred) ** 2)

    def predict(self, X):
        """
        使用BMA进行预测
        """
        # 直接使用产品预测值进行加权平均
        weighted_pred = np.sum(X * self.weights, axis=1)
        return weighted_pred

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    
    # Nash-Sutcliffe Efficiency
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    
    # 相关系数
    corr = np.corrcoef(y_true, y_pred)[0, 1]
    
    return rmse, nse, corr

def main():
    # 读取数据
    data = pd.read_csv('Exp/data/lianghe_final(时间换算后).csv')
    
    # 准备特征和标签
    X = data[['001', '002', '003']].values
    y = data['tp'].values
    
    # 划分训练集和测试集 (80/20)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # 训练BMA模型
    bma_model = BMAFusion()
    bma_model.fit(X_train, y_train)
    
    # 预测
    y_train_pred = bma_model.predict(X_train)
    y_test_pred = bma_model.predict(X_test)
    
    # 计算评估指标
    train_rmse, train_nse, train_corr = calculate_metrics(y_train, y_train_pred)
    test_rmse, test_nse, test_corr = calculate_metrics(y_test, y_test_pred)
    
    # 可视化结果
    plt.figure(figsize=(15, 5))
    
    # 训练集结果
    plt.subplot(1, 3, 1)
    plt.scatter(y_train, y_train_pred, alpha=0.5, s=1)
    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Training Set\nRMSE={train_rmse:.4f}, NSE={train_nse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 测试集结果
    plt.subplot(1, 3, 2)
    plt.scatter(y_test, y_test_pred, alpha=0.5, s=1)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Test Set\nRMSE={test_rmse:.4f}, NSE={test_nse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 时间序列对比 (显示测试集的一部分)
    plt.subplot(1, 3, 3)
    n_show = min(500, len(y_test))
    plt.plot(y_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(y_test_pred[:n_show], 'r-', linewidth=1, label='BMA Fusion', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Time Series Comparison (Test Set)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Exp/bma_fusion_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存详细结果到CSV
    results_df = pd.DataFrame({
        'Actual': y_test,
        'BMA_Prediction': y_test_pred
    })
    results_df.to_csv('Exp/bma_fusion_predictions.csv', index=False)

if __name__ == "__main__":
    main()
