#!/usr/bin/env python3
"""
处理每日文件夹下的三个CSV文件，满足以下要求：
1. 以valid_time的时间为准
2. 将每个时间点的多个坐标的tp数据进行相加，得到总的tp值
3. 只保留当天09:00:00到第二天早上08:00:00的24小时数据
4. 最终仅保留valid_time列和tp列
5. 将每天处理后的数据进行拼接，得到完整的2024年数据
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import traceback

def process_single_csv(csv_file_path):
    """处理单个CSV文件"""
    try:
        df = pd.read_csv(csv_file_path)
        df['valid_time'] = pd.to_datetime(df['valid_time'])
        
        grouped_df = df.groupby('valid_time')['tp'].sum().reset_index()
        
        first_time = grouped_df['valid_time'].min()
        start_date = first_time.date()
        
        start_time = pd.Timestamp(f"{start_date} 09:00:00")
        end_time = start_time + timedelta(hours=23)
        
        mask = (grouped_df['valid_time'] >= start_time) & (grouped_df['valid_time'] <= end_time)
        filtered_df = grouped_df[mask].copy()
        
        result_df = filtered_df[['valid_time', 'tp']].copy()
        result_df = result_df.sort_values('valid_time').reset_index(drop=True)
        
        return result_df
        
    except Exception:
        return None

def process_all_watersheds():
    """处理所有流域的数据并生成最终的拼接文件"""
    
    watersheds = ['lianghe', 'yantang', 'yinhe']
    
    source_dir = Path("China_Product/2025_final")
    output_dir = Path("China_Product")
    
    if not source_dir.exists():
        return None
    
    date_folders = [f for f in source_dir.iterdir() if f.is_dir() and f.name.isdigit()]
    date_folders.sort()
    
    watershed_data = {watershed: [] for watershed in watersheds}
    
    stats = {
        'total_folders': len(date_folders),
        'processed_folders': 0,
        'failed_folders': [],
        'watershed_stats': {name: {'success': 0, 'failed': 0} for name in watersheds}
    }
    
    for i, date_folder in enumerate(date_folders, 1):
        folder_success = True
        
        for watershed in watersheds:
            try:
                csv_files = list(date_folder.glob(f"{watershed}_*.csv"))
                
                if not csv_files:
                    stats['watershed_stats'][watershed]['failed'] += 1
                    folder_success = False
                    continue
                
                if len(csv_files) > 1:
                    stats['watershed_stats'][watershed]['failed'] += 1
                    folder_success = False
                    continue
                
                csv_file = csv_files[0]
                processed_data = process_single_csv(csv_file)
                
                if processed_data is not None and len(processed_data) > 0:
                    watershed_data[watershed].append(processed_data)
                    stats['watershed_stats'][watershed]['success'] += 1
                else:
                    stats['watershed_stats'][watershed]['failed'] += 1
                    folder_success = False
                
            except Exception:
                stats['watershed_stats'][watershed]['failed'] += 1
                folder_success = False
        
        if folder_success:
            stats['processed_folders'] += 1
        else:
            stats['failed_folders'].append(date_folder.name)
    
    for watershed in watersheds:
        if watershed_data[watershed]:
            try:
                combined_df = pd.concat(watershed_data[watershed], ignore_index=True)
                combined_df = combined_df.sort_values('valid_time').reset_index(drop=True)
                
                output_file = output_dir / f"{watershed}_2025_hourly.csv"
                combined_df.to_csv(output_file, index=False)
                
            except Exception:
                pass
    
    return stats

def validate_output_files():
    """验证输出文件的质量"""
    watersheds = ['lianghe', 'yantang', 'yinhe']
    output_dir = Path("China_Product")
    
    validation_results = {}
    
    for watershed in watersheds:
        output_file = output_dir / f"{watershed}_2025_hourly.csv"
        
        if output_file.exists():
            try:
                df = pd.read_csv(output_file)
                df['valid_time'] = pd.to_datetime(df['valid_time'])
                
                time_diff = df['valid_time'].diff().dropna()
                expected_diff = pd.Timedelta(hours=1)
                irregular_intervals = time_diff[time_diff != expected_diff]
                
                tp_stats = df['tp'].describe()
                
                validation_results[watershed] = {
                    'file_size_mb': output_file.stat().st_size / 1024 / 1024,
                    'total_records': len(df),
                    'time_range': (df['valid_time'].min(), df['valid_time'].max()),
                    'time_span_days': (df['valid_time'].max() - df['valid_time'].min()).days,
                    'is_continuous': len(irregular_intervals) == 0,
                    'irregular_intervals': len(irregular_intervals),
                    'tp_stats': {
                        'min': tp_stats['min'],
                        'max': tp_stats['max'],
                        'mean': tp_stats['mean']
                    }
                }
                
            except Exception:
                validation_results[watershed] = {'error': 'validation_failed'}
        else:
            validation_results[watershed] = {'error': 'file_not_found'}
    
    return validation_results

if __name__ == "__main__":
    results = process_all_watersheds()
    
    if results:
        validation_results = validate_output_files()
        
        # Return processing and validation results
        final_results = {
            'processing': results,
            'validation': validation_results
        }
    else:
        final_results = None
