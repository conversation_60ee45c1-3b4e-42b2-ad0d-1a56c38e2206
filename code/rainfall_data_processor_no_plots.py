#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
两河流域雨量数据处理工具 (无图形输出版本)
功能：
1. 处理原始CSV文件（删除前三行和第一列）
2. 合并和排序各站点数据
3. 补全缺失时间点
4. 汇总计算面上雨量

作者：封装自四个独立的处理脚本
注意：此版本移除了所有可能产生图形输出的print语句
"""

import csv
from datetime import datetime, timedelta
from pathlib import Path

class RainfallDataProcessor:
    """两河流域雨量数据处理器"""
    
    def __init__(self, origin_dir="origin", merge_dir="merge", weight_file="weight.csv", output_file="流域雨量汇总.csv"):
        """
        初始化处理器
        
        Args:
            origin_dir (str): 原始数据目录路径
            merge_dir (str): 合并数据输出目录路径
            weight_file (str): 权重文件路径
            output_file (str): 最终汇总文件路径
        """
        self.origin_dir = Path(origin_dir)
        self.merge_dir = Path(merge_dir)
        self.weight_file = Path(weight_file)
        self.output_file = Path(output_file)
        
        # 确保merge目录存在
        self.merge_dir.mkdir(exist_ok=True)
    
    def preprocess_all_csv_file(self):
        """
        处理origin_dir目录下的所有CSV文件：删除前三行以及第一列
        调用RainfallDataProcessor类的全流程处理函数process_rainfall_data时不会自动调用此函数
        如果原文件包含无用表头信息和序号列，需要手动调用该函数，然后使用全流程处理函数
        
        Returns:
            bool: 是否全部处理成功
        """
        try:
            # 获取origin_dir下所有CSV文件
            csv_files = list(self.origin_dir.glob("*.csv"))
            
            if not csv_files:
                return False
                
            successful_count = 0
            failed_count = 0
            
            for csv_file in csv_files:
                try:
                    # 读取原始文件
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        all_rows = list(reader)
                    
                    if len(all_rows) <= 3:
                        failed_count += 1
                        continue
                    
                    # 删除前三行，保留从第4行开始的数据
                    data_rows = all_rows[3:]
                    
                    # 删除第一列（序号列），保留第二列和第三列
                    processed_rows = []
                    for row in data_rows:
                        if len(row) >= 2:
                            processed_row = row[1:]  # 删除第一列
                            processed_rows.append(processed_row)
                    
                    # 添加新的标题行
                    header = ['时间', '时段雨量']
                    final_rows = [header] + processed_rows
                    
                    # 写回文件
                    with open(csv_file, 'w', encoding='utf-8', newline='') as f:
                        writer = csv.writer(f)
                        writer.writerows(final_rows)
                    
                    successful_count += 1
                    
                except Exception as e:
                    failed_count += 1
            
            return failed_count == 0
            
        except Exception as e:
            return False
    
    def process_origin_files(self):
        """
        步骤1：检查原始目录下的CSV文件格式

        Returns:
            bool: 是否处理成功
        """
        if not self.origin_dir.exists():
            return False

        # 查找所有CSV文件
        csv_files = list(self.origin_dir.glob("*.csv"))

        if not csv_files:
            return False

        successful_count = 0
        failed_count = 0

        for csv_file in sorted(csv_files):
            try:
                # 检查文件格式
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    header = next(reader)
                    first_data_row = next(reader, None)

                # 检查是否有正确的标题和数据格式
                if len(header) >= 2 and '时间' in header[0] and '时段雨量' in header[1]:
                    if first_data_row and len(first_data_row) >= 2:
                        successful_count += 1
                    else:
                        failed_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                failed_count += 1

        return failed_count == 0
    
    def parse_time_string(self, time_str, year):
        """
        解析时间字符串，转换为标准时间格式
        
        Args:
            time_str (str): 时间字符串，格式如"12-31 23"
            year (int): 年份
        
        Returns:
            datetime: 标准时间对象
        """
        try:
            # 解析"月-日 小时"格式
            parts = time_str.strip().split()
            if len(parts) != 2:
                return None
            
            date_part = parts[0]  # "12-31"
            hour_part = parts[1]  # "23"
            
            # 分离月和日
            month_day = date_part.split('-')
            if len(month_day) != 2:
                return None
            
            month = int(month_day[0])
            day = int(month_day[1])
            hour = int(hour_part)
            
            # 创建datetime对象
            dt = datetime(year, month, day, hour, 0, 0)
            return dt
            
        except (ValueError, IndexError) as e:
            return None
    
    def process_single_station_csv(self, csv_file_path, year):
        """
        处理单个站点CSV文件
        
        Args:
            csv_file_path (Path): CSV文件路径
            year (int): 年份
        
        Returns:
            list: 处理后的数据列表，每个元素为[datetime, rainfall]
        """
        data_list = []
        
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader)  # 跳过标题行

                for row in reader:
                    if len(row) >= 2:
                        time_str = row[0]
                        rainfall = row[1]

                        # 解析时间
                        dt = self.parse_time_string(time_str, year)
                        if dt:
                            data_list.append([dt, rainfall])

            return data_list

        except Exception as e:
            return []
    
    def merge_single_station_data(self, station_name):
        """
        合并单个水文站的2024上半年、2024下半年和2025年数据

        Args:
            station_name (str): 水文站名称

        Returns:
            bool: 是否处理成功
        """
        # 文件路径
        file_2024_first_half = self.origin_dir / f"{station_name}2024上半年.csv"
        file_2024_second_half = self.origin_dir / f"{station_name}2024下半年.csv"
        file_2025 = self.origin_dir / f"{station_name}2025.csv"

        all_data = []
        processed_files = []

        # 处理2024上半年数据
        if file_2024_first_half.exists():
            data_2024_first = self.process_single_station_csv(file_2024_first_half, 2024)
            all_data.extend(data_2024_first)
            processed_files.append(file_2024_first_half.name)

        # 处理2024下半年数据
        if file_2024_second_half.exists():
            data_2024_second = self.process_single_station_csv(file_2024_second_half, 2024)
            all_data.extend(data_2024_second)
            processed_files.append(file_2024_second_half.name)

        # 处理2025年数据
        if file_2025.exists():
            data_2025 = self.process_single_station_csv(file_2025, 2025)
            all_data.extend(data_2025)
            processed_files.append(file_2025.name)

        if not all_data:
            return False

        # 按时间排序（升序）
        all_data.sort(key=lambda x: x[0])

        # 检查并去除重复时间点（如果有的话）
        unique_data = []
        seen_times = set()
        duplicates_count = 0

        for dt, rainfall in all_data:
            if dt not in seen_times:
                unique_data.append((dt, rainfall))
                seen_times.add(dt)
            else:
                duplicates_count += 1

        all_data = unique_data

        # 生成输出文件名
        output_file = self.merge_dir / f"{station_name}_merged.csv"

        # 写入合并后的数据
        try:
            with open(output_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)

                # 写入标题行
                writer.writerow(['时间', '时段雨量'])

                # 写入数据
                for dt, rainfall in all_data:
                    time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                    writer.writerow([time_str, rainfall])

            return True

        except Exception as e:
            return False

    def get_station_names(self):
        """
        获取所有水文站名称

        Returns:
            list: 水文站名称列表
        """
        csv_files = list(self.origin_dir.glob("*2024下半年.csv"))

        station_names = []
        for file in csv_files:
            # 提取水文站名称（去掉"2024下半年.csv"后缀）
            station_name = file.name.replace("2024下半年.csv", "")
            station_names.append(station_name)

        return sorted(station_names)

    def merge_and_sort_data(self):
        """
        步骤2：合并和排序各站点数据

        Returns:
            bool: 是否处理成功
        """
        # 获取所有水文站名称
        station_names = self.get_station_names()

        if not station_names:
            return False

        successful_count = 0
        failed_count = 0

        for station_name in station_names:
            try:
                if self.merge_single_station_data(station_name):
                    successful_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                failed_count += 1

        return failed_count == 0

    def parse_datetime(self, time_str):
        """
        解析时间字符串为datetime对象

        Args:
            time_str (str): 时间字符串

        Returns:
            datetime: 解析后的时间对象
        """
        return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')

    def generate_complete_time_series(self, start_time, end_time):
        """
        生成完整的小时时间序列

        Args:
            start_time (datetime): 开始时间
            end_time (datetime): 结束时间

        Returns:
            list: 完整的时间序列列表
        """
        time_series = []
        current_time = start_time

        while current_time <= end_time:
            time_series.append(current_time)
            current_time += timedelta(hours=1)

        return time_series

    def fill_missing_timestamps_single_file(self, csv_file_path):
        """
        补全单个CSV文件中缺失的时间点

        Args:
            csv_file_path (Path): CSV文件路径

        Returns:
            bool: 是否处理成功
        """
        try:
            # 读取原始数据
            original_data = {}
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if '时间' in row and '时段雨量' in row:
                        time_obj = self.parse_datetime(row['时间'])
                        original_data[time_obj] = float(row['时段雨量'])

            if not original_data:
                return False

            # 获取时间范围
            start_time = min(original_data.keys())
            end_time = max(original_data.keys())

            # 生成完整的时间序列
            complete_time_series = self.generate_complete_time_series(start_time, end_time)

            # 创建完整的数据集，缺失的时间点用0填充
            complete_data = []
            for time_obj in complete_time_series:
                rainfall = original_data.get(time_obj, 0)  # 缺失的用0填充
                complete_data.append({
                    '时间': time_obj.strftime('%Y-%m-%d %H:%M:%S'),
                    '时段雨量': rainfall
                })

            # 写回文件
            with open(csv_file_path, 'w', encoding='utf-8', newline='') as f:
                fieldnames = ['时间', '时段雨量']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(complete_data)

            return True

        except Exception as e:
            return False

    def fill_missing_timestamps(self):
        """
        步骤3：补全缺失的时间点

        Returns:
            bool: 是否处理成功
        """
        if not self.merge_dir.exists():
            return False

        # 查找所有CSV文件
        csv_files = list(self.merge_dir.glob("*.csv"))

        if not csv_files:
            return False

        successful_count = 0
        failed_count = 0

        for csv_file in sorted(csv_files):
            try:
                # 补全时间点
                if self.fill_missing_timestamps_single_file(csv_file):
                    successful_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                failed_count += 1

        return failed_count == 0

    def load_weights(self):
        """
        加载站点权重数据

        Returns:
            dict: 站点名称到权重的映射
        """
        weights = {}
        try:
            with open(self.weight_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    station_name = row['站名'].strip()
                    weight = float(row['权重'])
                    weights[station_name] = weight

            return weights

        except Exception as e:
            return {}

    def normalize_station_name(self, filename):
        """
        标准化站点名称（从文件名提取）

        Args:
            filename (str): 文件名

        Returns:
            str: 标准化的站点名称
        """
        # 去掉"_merged.csv"后缀
        name = filename.replace('_merged.csv', '')
        return name

    def load_station_data(self, csv_file_path):
        """
        加载单个站点的数据

        Args:
            csv_file_path (Path): CSV文件路径

        Returns:
            dict: 时间戳到雨量值的映射
        """
        data = {}
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    timestamp = row['时间']
                    rainfall = float(row['时段雨量'])
                    data[timestamp] = rainfall

            return data

        except Exception as e:
            return {}

    def aggregate_rainfall_data(self):
        """
        步骤4：汇总所有站点的雨量数据并计算面上雨量

        Returns:
            bool: 是否处理成功
        """
        # 加载权重数据
        weights = self.load_weights()
        if not weights:
            return False

        # 获取merge文件夹中的所有CSV文件
        csv_files = list(self.merge_dir.glob('*_merged.csv'))

        if not csv_files:
            return False

        # 加载所有站点数据
        all_station_data = {}
        all_timestamps = set()

        for csv_file in sorted(csv_files):
            station_name = self.normalize_station_name(csv_file.name)

            station_data = self.load_station_data(csv_file)
            if station_data:
                all_station_data[station_name] = station_data
                all_timestamps.update(station_data.keys())

        # 检查权重文件中的站点是否都有数据
        missing_stations = []
        for station_name in weights.keys():
            if station_name not in all_station_data:
                missing_stations.append(station_name)

        # 获取所有时间戳并排序
        sorted_timestamps = sorted(all_timestamps)

        # 确定要包含的站点（有数据且有权重的站点）
        valid_stations = []
        for station_name in weights.keys():
            if station_name in all_station_data:
                valid_stations.append(station_name)

        # 生成汇总数据
        try:
            with open(self.output_file, 'w', encoding='utf-8-sig', newline='') as f:
                # 创建标题行
                fieldnames = ['时间'] + valid_stations + ['面上雨量']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                # 写入数据
                for timestamp in sorted_timestamps:
                    row = {'时间': timestamp}

                    # 获取各站点数据
                    station_values = []
                    for station in valid_stations:
                        value = all_station_data[station].get(timestamp, 0.0)
                        row[station] = value
                        station_values.append(value)

                    # 计算面上雨量（加权平均）
                    weighted_sum = 0.0
                    total_weight = 0.0

                    for i, station in enumerate(valid_stations):
                        weight = weights[station]
                        value = station_values[i]
                        weighted_sum += weight * value
                        total_weight += weight

                    # 面上雨量
                    area_rainfall = weighted_sum / total_weight if total_weight > 0 else 0.0
                    row['面上雨量'] = round(area_rainfall, 3)  # 保留三位小数

                    writer.writerow(row)

            return True

        except Exception as e:
            return False

    def process_rainfall_data(self):
        """
        主处理函数：按顺序执行所有步骤

        Returns:
            bool: 是否全部处理成功
        """
        # 步骤1：处理原始文件
        if not self.process_origin_files():
            return False

        # 步骤2：合并和排序数据
        if not self.merge_and_sort_data():
            return False

        # 步骤3：补全缺失时间点
        if not self.fill_missing_timestamps():
            return False

        # 步骤4：汇总计算面上雨量
        if not self.aggregate_rainfall_data():
            return False

        return True


def main():
    """
    主函数 - 使用示例
    """
    # 创建处理器实例
    # 可以自定义各个路径参数
    processor = RainfallDataProcessor(
        origin_dir="origin",           # 原始数据目录
        merge_dir="merge",             # 合并数据输出目录
        weight_file="weight.csv",      # 权重文件
        output_file="两河流域雨量汇总.csv"  # 最终输出文件
    )

    # 执行完整的处理流程
    success = processor.process_rainfall_data()

    return success


if __name__ == "__main__":
    main()
