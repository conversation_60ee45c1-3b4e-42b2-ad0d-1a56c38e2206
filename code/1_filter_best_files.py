#!/usr/bin/env python3
"""
筛选China_Procduct/2024中的最佳文件
筛选规则：保留预测发布时间早于且最接近早上八点整，同时预报开始时间是早上八点整的文件
"""

import os
import shutil
import re
from pathlib import Path
from datetime import datetime, time
import xarray as xr
import numpy as np

def parse_filename(filename):
    """
    解析文件名，提取预测发布时间和预报开始时间
    文件名格式：Z_NWGD_C_BABJ_20240328043341_P_RFFC_SCMOC-ER01_202403280800_07201.GRB2
    返回：(预测发布时间, 预报开始时间) 或 None
    """
    pattern = r'Z_NWGD_C_BABJ_(\d{14})_P_RFFC_SCMOC-ER01_(\d{12})_\d+\.GRB2$'
    match = re.match(pattern, filename)
    
    if not match:
        return None
    
    try:
        forecast_issue_str = match.group(1)
        forecast_start_str = match.group(2)
        
        forecast_issue = datetime.strptime(forecast_issue_str, '%Y%m%d%H%M%S')
        forecast_start = datetime.strptime(forecast_start_str, '%Y%m%d%H%M')
        
        return forecast_issue, forecast_start
        
    except ValueError:
        return None

def is_valid_forecast_start_time(forecast_start):
    """
    检查预报开始时间是否为早上8点整
    """
    return forecast_start.hour == 8 and forecast_start.minute == 0

def check_tp_values(file_path):
    """
    检查GRB2文件中的tp列是否包含9999数值
    
    Parameters:
    -----------
    file_path : str or Path
        GRB2文件路径
    
    Returns:
    --------
    bool : True表示文件有效（不包含9999），False表示文件无效（包含9999）
    """
    try:
        ds = xr.open_dataset(file_path, engine='cfgrib')
        
        tp_var = None
        for var_name in ['tp', 'unknown', 'precipitation']:
            if var_name in ds.variables:
                tp_var = ds[var_name]
                break
        
        if tp_var is None:
            ds.close()
            return True
        
        has_9999 = np.any(np.isclose(tp_var.values, 9999.0, rtol=1e-5))
        ds.close()
        
        return not has_9999
        
    except Exception:
        return False

def find_best_file(files, target_date, date_folder_path):
    """
    从文件列表中找到最佳文件
    规则：
    1. 优先选择预测发布时间早于且最接近早上八点整，同时预报开始时间是早上八点整的文件
    2. 如果没有早于八点的文件，选择最接近早上八点的文件（可以晚于八点）
    3. 检查选中文件的tp列是否包含9999数值，如果包含则选择前一个时间点的文件
    """
    before_8am_files = []
    after_8am_files = []
    target_datetime = datetime.combine(target_date, time(8, 0))
    
    for filename in files:
        if not filename.endswith('.GRB2') or filename.endswith('~'):
            continue
        
        parsed = parse_filename(filename)
        if parsed is None:
            continue
        
        forecast_issue, forecast_start = parsed
        
        if not is_valid_forecast_start_time(forecast_start):
            continue
        
        if forecast_issue < target_datetime:
            time_diff = target_datetime - forecast_issue
            before_8am_files.append((filename, forecast_issue, forecast_start, time_diff, 'before'))
        else:
            time_diff = forecast_issue - target_datetime
            after_8am_files.append((filename, forecast_issue, forecast_start, time_diff, 'after'))
    
    if before_8am_files:
        before_8am_files.sort(key=lambda x: x[3])
        
        for best_file in before_8am_files:
            file_path = date_folder_path / best_file[0]
            if check_tp_values(file_path):
                return {
                    'filename': best_file[0],
                    'forecast_issue': best_file[1],
                    'forecast_start': best_file[2],
                    'time_diff': best_file[3],
                    'selection_type': 'before_8am'
                }
    
    if after_8am_files:
        after_8am_files.sort(key=lambda x: x[3])
        
        for best_file in after_8am_files:
            file_path = date_folder_path / best_file[0]
            if check_tp_values(file_path):
                return {
                    'filename': best_file[0],
                    'forecast_issue': best_file[1],
                    'forecast_start': best_file[2],
                    'time_diff': best_file[3],
                    'selection_type': 'after_8am'
                }
    
    return None

def process_directory(source_dir, target_dir):
    """
    处理整个目录，筛选每个日期文件夹中的最佳文件
    """
    source_path = Path(source_dir)
    target_path = Path(target_dir)
    
    if not source_path.exists():
        return False
    
    target_path.mkdir(parents=True, exist_ok=True)
    
    total_folders = 0
    processed_folders = 0
    failed_folders = []
    before_8am_count = 0
    after_8am_count = 0
    
    for date_folder in sorted(source_path.iterdir()):
        if not date_folder.is_dir():
            continue
            
        if not (date_folder.name.isdigit() and len(date_folder.name) == 8):
            continue
            
        total_folders += 1
        date_str = date_folder.name
        
        try:
            target_date = datetime.strptime(date_str, '%Y%m%d').date()
            files = [f.name for f in date_folder.iterdir() if f.is_file()]
            
            if not files:
                failed_folders.append((date_str, "文件夹为空"))
                continue
            
            best_file_info = find_best_file(files, target_date, date_folder)
            
            if best_file_info is None:
                failed_folders.append((date_str, "未找到符合条件的文件"))
                continue
            
            target_date_folder = target_path / date_str
            target_date_folder.mkdir(exist_ok=True)
            
            source_file = date_folder / best_file_info['filename']
            target_file = target_date_folder / best_file_info['filename']
            
            shutil.copy2(source_file, target_file)
            
            if best_file_info.get('selection_type') == 'before_8am':
                before_8am_count += 1
            else:
                after_8am_count += 1
            
            processed_folders += 1
            
        except Exception as e:
            failed_folders.append((date_str, str(e)))
    
    return {
        'total_folders': total_folders,
        'processed_folders': processed_folders,
        'failed_folders': failed_folders,
        'before_8am_count': before_8am_count,
        'after_8am_count': after_8am_count
    }

if __name__ == "__main__":
    source_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2025"
    target_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    
    result = process_directory(source_directory, target_directory)
