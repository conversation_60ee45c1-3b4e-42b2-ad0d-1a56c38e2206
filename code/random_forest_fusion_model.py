"""
基于随机森林回归的多源降水产品融合模型
使用随机森林回归方法融合三个降水产品，以实测雨量作为标签
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def add_time_features(data):
    """添加时间特征"""
    data['time'] = pd.to_datetime(data['time'])
    data['hour'] = data['time'].dt.hour
    data['month'] = data['time'].dt.month
    data['season'] = data['month'].apply(lambda x: (x-1)//3 + 1)  # 1:春, 2:夏, 3:秋, 4:冬
    
    # 周期性编码
    data['hour_sin'] = np.sin(2 * np.pi * data['hour'] / 24)
    data['hour_cos'] = np.cos(2 * np.pi * data['hour'] / 24)
    data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
    data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)
    
    return data

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    
    # Nash-Sutcliffe Efficiency
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    
    # 相关系数
    corr = np.corrcoef(y_true, y_pred)[0, 1]
    
    return rmse, nse, corr

def main():
    # 读取数据
    data = pd.read_csv('Exp/data/lianghe_final(时间换算后).csv')
    
    # 添加时间特征
    data = add_time_features(data)
    
    # 准备特征和标签
    feature_columns = ['001', '002', '003', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'season']
    X = data[feature_columns].values
    y = data['tp'].values
    
    # 划分训练集和测试集 (80/20)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 训练随机森林模型
    rf_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    
    rf_model.fit(X_train_scaled, y_train)
    
    # 预测
    y_train_pred = rf_model.predict(X_train_scaled)
    y_test_pred = rf_model.predict(X_test_scaled)
    
    # 计算评估指标
    train_rmse, train_nse, train_corr = calculate_metrics(y_train, y_train_pred)
    test_rmse, test_nse, test_corr = calculate_metrics(y_test, y_test_pred)
    
    # 特征重要性
    feature_importance = rf_model.feature_importances_
    importance_df = pd.DataFrame({
        'Feature': feature_columns,
        'Importance': feature_importance
    }).sort_values('Importance', ascending=False)
    
    # 可视化结果
    plt.figure(figsize=(20, 10))
    
    # 训练集结果
    plt.subplot(2, 4, 1)
    plt.scatter(y_train, y_train_pred, alpha=0.5, s=1)
    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Training Set\nRMSE={train_rmse:.4f}, NSE={train_nse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 测试集结果
    plt.subplot(2, 4, 2)
    plt.scatter(y_test, y_test_pred, alpha=0.5, s=1)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Test Set\nRMSE={test_rmse:.4f}, NSE={test_nse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 时间序列对比 (显示测试集的一部分)
    plt.subplot(2, 4, 3)
    n_show = min(500, len(y_test))
    plt.plot(y_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(y_test_pred[:n_show], 'r-', linewidth=1, label='RF Fusion', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Time Series Comparison (Test Set)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 特征重要性图
    plt.subplot(2, 4, 4)
    plt.barh(importance_df['Feature'], importance_df['Importance'])
    plt.xlabel('Feature Importance')
    plt.title('Feature Importance')
    plt.grid(True, alpha=0.3)
    
    # 残差分析
    plt.subplot(2, 4, 5)
    residuals = y_test - y_test_pred
    plt.scatter(y_test_pred, residuals, alpha=0.5, s=1)
    plt.axhline(y=0, color='r', linestyle='--', lw=1)
    plt.xlabel('Predicted Rainfall (mm)')
    plt.ylabel('Residuals (mm)')
    plt.title('Residual Plot')
    plt.grid(True, alpha=0.3)
    
    # 残差直方图
    plt.subplot(2, 4, 6)
    plt.hist(residuals, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('Residuals (mm)')
    plt.ylabel('Frequency')
    plt.title('Residual Distribution')
    plt.grid(True, alpha=0.3)
    
    # 产品对比
    plt.subplot(2, 4, 7)
    n_show = min(200, len(y_test))
    plt.plot(y_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(X_test[:n_show, 0], '--', linewidth=1, label='Product 1', alpha=0.7, color='orange')
    plt.plot(X_test[:n_show, 1], '--', linewidth=1, label='Product 2', alpha=0.7, color='purple')
    plt.plot(X_test[:n_show, 2], '--', linewidth=1, label='Product 3', alpha=0.7, color='blue')
    plt.plot(y_test_pred[:n_show], 'r-', linewidth=2, label='RF Fusion', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Products vs Fusion Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 模型性能对比
    plt.subplot(2, 4, 8)
    metrics = ['RMSE', 'NSE', 'Correlation']
    train_values = [train_rmse, train_nse, train_corr]
    test_values = [test_rmse, test_nse, test_corr]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    plt.bar(x - width/2, train_values, width, label='Training', alpha=0.8)
    plt.bar(x + width/2, test_values, width, label='Testing', alpha=0.8)
    plt.xlabel('Metrics')
    plt.ylabel('Values')
    plt.title('Model Performance')
    plt.xticks(x, metrics)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Exp/random_forest_fusion_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存结果
    results_df = pd.DataFrame({
        'Actual': y_test,
        'RF_Prediction': y_test_pred,
        'Product1': X_test[:, 0],
        'Product2': X_test[:, 1],
        'Product3': X_test[:, 2]
    })
    results_df.to_csv('Exp/random_forest_fusion_predictions.csv', index=False)
    
    # 保存特征重要性
    importance_df.to_csv('Exp/random_forest_feature_importance.csv', index=False)

if __name__ == "__main__":
    main()
