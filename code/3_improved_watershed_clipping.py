#!/usr/bin/env python3
"""
改进的流域裁剪功能 - 实现精确的空间相交和面积加权降雨量计算
"""

import xarray as xr
import geopandas as gpd
from shapely.geometry import box
from shapely.ops import transform
import pandas as pd
from pathlib import Path
import traceback
import gc
import warnings
from pyproj import Transformer
import math

warnings.filterwarnings('ignore')

class ImprovedWatershedClipper:
    """改进的流域裁剪器类"""
    
    def __init__(self, grid_resolution=0.25):
        self.grid_resolution = grid_resolution
        self.earth_radius = 6371000
        
    def calculate_grid_cell_area(self, center_lat, center_lon=None, grid_size=None):
        """计算网格单元的面积（平方公里）"""
        _ = center_lon
        
        if grid_size is None:
            grid_size = self.grid_resolution

        lat_rad = math.radians(center_lat)
        grid_rad = math.radians(grid_size)

        lat_dist = self.earth_radius * grid_rad
        lon_dist = self.earth_radius * grid_rad * math.cos(lat_rad)

        return (lat_dist * lon_dist) / 1_000_000
    
    def create_grid_polygon(self, center_lat, center_lon, grid_size=None):
        """根据网格中心点创建网格多边形"""
        if grid_size is None:
            grid_size = self.grid_resolution
            
        half_size = grid_size / 2
        
        min_lat = center_lat - half_size
        max_lat = center_lat + half_size
        min_lon = center_lon - half_size
        max_lon = center_lon + half_size
        
        return box(min_lon, min_lat, max_lon, max_lat)
    
    def calculate_intersection_area(self, grid_polygon, watershed_geometry):
        """计算网格多边形与流域几何体的相交面积"""
        try:
            target_crs = "+proj=aea +lat_1=25 +lat_2=47 +lat_0=36 +lon_0=105 +x_0=0 +y_0=0 +datum=WGS84 +units=m +no_defs"
            
            transformer = Transformer.from_crs("EPSG:4326", target_crs, always_xy=True)
            
            grid_projected = transform(transformer.transform, grid_polygon)
            watershed_projected = transform(transformer.transform, watershed_geometry.unary_union)
            
            intersection = grid_projected.intersection(watershed_projected)
            
            if intersection.is_empty:
                return 0.0
            
            return intersection.area / 1_000_000
            
        except Exception:
            return 0.0

    def get_watershed_total_area(self, watershed_gdf):
        """计算流域总面积"""
        try:
            target_crs = "+proj=aea +lat_1=25 +lat_2=47 +lat_0=36 +lon_0=105 +x_0=0 +y_0=0 +datum=WGS84 +units=m +no_defs"
            
            if watershed_gdf.crs is None:
                watershed_gdf = watershed_gdf.set_crs("EPSG:4326")
            elif watershed_gdf.crs.to_string() != "EPSG:4326":
                watershed_gdf = watershed_gdf.to_crs("EPSG:4326")
            
            watershed_projected = watershed_gdf.to_crs(target_crs)
            total_area = watershed_projected.geometry.area.sum() / 1_000_000
            
            return total_area
            
        except Exception:
            return 0.0

    def clip_grib_with_improved_method(self, grib_file, shapefile_path, output_csv=None):
        """使用改进方法裁剪GRIB2文件"""
        ds = None
        gdf = None
        
        try:
            ds = xr.open_dataset(grib_file, engine='cfgrib')
            ds = ds.rio.write_crs("EPSG:4326")
            
            gdf = gpd.read_file(shapefile_path)
            
            if gdf.crs is None:
                gdf = gdf.set_crs("EPSG:4326")
            elif gdf.crs.to_string() != "EPSG:4326":
                gdf = gdf.to_crs("EPSG:4326")
            
            watershed_total_area = self.get_watershed_total_area(gdf)
            
            df = ds.to_dataframe().reset_index()
            df = df.dropna()
            
            if 'unknown' in df.columns:
                df = df.rename(columns={'unknown': 'tp'})

            unique_times = sorted(df['valid_time'].unique())

            bounds = gdf.total_bounds
            buffer = self.grid_resolution

            mask = (
                (df['longitude'] >= bounds[0] - buffer) &
                (df['longitude'] <= bounds[2] + buffer) &
                (df['latitude'] >= bounds[1] - buffer) &
                (df['latitude'] <= bounds[3] + buffer)
            )

            df_filtered = df[mask].copy()

            time_series_results = []

            for time_idx, current_time in enumerate(unique_times):
                time_df = df_filtered[df_filtered['valid_time'] == current_time].copy()

                if len(time_df) == 0:
                    continue

                total_weighted_rainfall = 0.0
                valid_grids = 0

                for i, (_, row) in enumerate(time_df.iterrows()):
                    lat, lon = row['latitude'], row['longitude']
                    rainfall_value = row['tp']

                    grid_polygon = self.create_grid_polygon(lat, lon)
                    intersection_area = self.calculate_intersection_area(grid_polygon, gdf.geometry)

                    if intersection_area > 0:
                        area_weight = intersection_area / watershed_total_area
                        weighted_contribution = rainfall_value * area_weight
                        total_weighted_rainfall += weighted_contribution
                        valid_grids += 1

                time_series_results.append({
                    'valid_time': current_time,
                    'tp': total_weighted_rainfall
                })

            if len(time_series_results) > 0:
                result_df = pd.DataFrame(time_series_results)
                
                if output_csv:
                    result_df.to_csv(output_csv, index=False)
                
                total_valid_grids = sum(1 for result in time_series_results if result['tp'] > 0)
                avg_rainfall = sum(result['tp'] for result in time_series_results) / len(time_series_results)

                processing_result = {
                    'success': True,
                    'watershed_area': watershed_total_area,
                    'valid_grids': total_valid_grids,
                    'total_grids': len(df),
                    'weighted_rainfall': avg_rainfall,
                    'time_steps': len(time_series_results)
                }
                
                return processing_result, result_df
            
            else:
                return {'success': False, 'error': '没有相交的网格单元'}, None
                
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            return {'success': False, 'error': error_msg}, None
            
        finally:
            if ds is not None:
                ds.close()
                del ds
            if gdf is not None:
                del gdf
            gc.collect()


def process_single_date_folder_improved(date_folder_path, watersheds, output_base_dir):
    """使用改进方法处理单个日期文件夹中的GRB2文件"""
    results = {
        'date': date_folder_path.name,
        'success': 0,
        'failed': 0,
        'errors': [],
        'skipped': 0,
        'watershed_results': {}
    }

    output_date_dir = Path(output_base_dir) / date_folder_path.name
    output_date_dir.mkdir(parents=True, exist_ok=True)

    grb2_files = list(date_folder_path.glob("*.GRB2"))

    if not grb2_files:
        results['errors'].append("未找到GRB2文件")
        results['failed'] = 1
        return results

    if len(grb2_files) > 1:
        results['errors'].append(f"找到多个GRB2文件: {[f.name for f in grb2_files]}")
        results['failed'] = 1
        return results

    grb2_file = grb2_files[0]
    clipper = ImprovedWatershedClipper()

    for watershed_name, shapefile_path in watersheds.items():
        try:
            output_filename = f"{watershed_name}.csv"
            output_file_path = output_date_dir / output_filename

            if output_file_path.exists():
                results['success'] += 1
                results['skipped'] += 1
                continue

            processing_result, _ = clipper.clip_grib_with_improved_method(
                str(grb2_file),
                shapefile_path,
                str(output_file_path)
            )

            if processing_result['success']:
                results['success'] += 1
                results['watershed_results'][watershed_name] = processing_result
            else:
                error_msg = f"{watershed_name}: {processing_result['error']}"
                results['errors'].append(error_msg)
                results['failed'] += 1

        except Exception as e:
            error_msg = f"{watershed_name}: {str(e)}"
            results['errors'].append(error_msg)
            results['failed'] += 1

    return results


def batch_process_grb2_files_improved(source_dir, watersheds, output_dir, max_folders=None):
    """使用改进方法批量处理所有日期文件夹中的GRB2文件"""
    source_path = Path(source_dir)
    output_path = Path(output_dir)

    if not source_path.exists():
        return None

    output_path.mkdir(parents=True, exist_ok=True)

    date_folders = [f for f in source_path.iterdir() if f.is_dir() and f.name.isdigit()]
    date_folders.sort()

    if max_folders:
        date_folders = date_folders[:max_folders]

    total_stats = {
        'total_folders': len(date_folders),
        'processed_folders': 0,
        'total_success': 0,
        'total_failed': 0,
        'failed_folders': [],
        'watershed_stats': {name: {'success': 0, 'failed': 0, 'total_rainfall': 0.0}
                           for name in watersheds.keys()}
    }

    for i, date_folder in enumerate(date_folders, 1):
        try:
            results = process_single_date_folder_improved(date_folder, watersheds, output_dir)

            total_stats['processed_folders'] += 1
            total_stats['total_success'] += results['success']
            total_stats['total_failed'] += results['failed']

            for watershed_name in watersheds.keys():
                if watershed_name in results['watershed_results']:
                    watershed_result = results['watershed_results'][watershed_name]
                    total_stats['watershed_stats'][watershed_name]['success'] += 1
                    total_stats['watershed_stats'][watershed_name]['total_rainfall'] += watershed_result['weighted_rainfall']
                else:
                    watershed_errors = [error for error in results['errors'] if error.startswith(watershed_name)]
                    if watershed_errors:
                        total_stats['watershed_stats'][watershed_name]['failed'] += 1

            if results['failed'] > 0:
                total_stats['failed_folders'].append({
                    'date': results['date'],
                    'errors': results['errors']
                })

        except Exception as e:
            total_stats['failed_folders'].append({
                'date': date_folder.name,
                'errors': [f"文件夹处理失败: {str(e)}"]
            })

    return total_stats


if __name__ == "__main__":
    watersheds = {
        'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        'yinhe': '/home/<USER>/Flood_flow_prediction/boundary/yinhe.shp'
    }

    source_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    output_directory = "/home/<USER>/Flood_flow_prediction/China_Product/test"

    test_results = batch_process_grb2_files_improved(
        source_directory,
        watersheds,
        output_directory,
        max_folders=3
    )

    if test_results and test_results['total_success'] > 0:
        final_results = batch_process_grb2_files_improved(
            source_directory,
            watersheds,
            output_directory
        )
