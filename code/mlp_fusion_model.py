"""
基于MLP (多层感知机) 的多源降水产品融合模型
使用深度神经网络方法融合三个降水产品，以实测雨量作为标签
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError as e:
    from sklearn.neural_network import MLPRegressor
    TORCH_AVAILABLE = False
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def add_time_features(data):
    """添加时间特征"""
    data['time'] = pd.to_datetime(data['time'])
    data['hour'] = data['time'].dt.hour
    data['month'] = data['time'].dt.month
    data['season'] = data['month'].apply(lambda x: (x-1)//3 + 1)  # 1:春, 2:夏, 3:秋, 4:冬

    # 周期性编码
    data['hour_sin'] = np.sin(2 * np.pi * data['hour'] / 24)
    data['hour_cos'] = np.cos(2 * np.pi * data['hour'] / 24)
    data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
    data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)

    return data

if TORCH_AVAILABLE:
    class MLPModel(nn.Module):
        """MLP模型类"""
        def __init__(self, input_dim):
            super(MLPModel, self).__init__()
            self.layers = nn.Sequential(
                nn.Linear(input_dim, 32),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(32, 16),
                nn.ReLU(),
                nn.Linear(16, 1)
            )

        def forward(self, x):
            return self.layers(x)

    def create_mlp_model(input_dim):
        """创建MLP模型"""
        model = MLPModel(input_dim)
        return model

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    
    # Nash-Sutcliffe Efficiency
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    
    # 相关系数
    corr = np.corrcoef(y_true, y_pred)[0, 1]
    
    return rmse, nse, corr

if TORCH_AVAILABLE:
    class EarlyStopping:
        """自定义早停类"""
        def __init__(self, patience=10, min_delta=0.001):
            self.patience = patience
            self.min_delta = min_delta
            self.best_loss = np.inf
            self.wait = 0
            self.stop_training = False

        def __call__(self, val_loss):
            if val_loss < self.best_loss - self.min_delta:
                self.best_loss = val_loss
                self.wait = 0
            else:
                self.wait += 1
                if self.wait >= self.patience:
                    self.stop_training = True
            return self.stop_training

    def train_model(model, train_loader, val_loader, num_epochs=100, learning_rate=0.001, device='cpu'):
        """训练模型"""
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10, min_lr=1e-6)
        early_stopping = EarlyStopping(patience=15, min_delta=0.001)

        train_losses = []
        val_losses = []

        model.to(device)

        for epoch in range(num_epochs):
            # 训练阶段
            model.train()
            train_loss = 0.0
            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)

                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

            train_loss /= len(train_loader)

            # 验证阶段
            model.eval()
            val_loss = 0.0
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                    outputs = model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    val_loss += loss.item()

            val_loss /= len(val_loader)

            train_losses.append(train_loss)
            val_losses.append(val_loss)

            # 学习率调度
            scheduler.step(val_loss)

            # 早停检查
            if early_stopping(val_loss):
                break

        return train_losses, val_losses

def main():
    global TORCH_AVAILABLE

    # 设置随机种子
    np.random.seed(42)

    if TORCH_AVAILABLE:
        try:
            torch.manual_seed(42)
            # 设置设备，强制使用CPU避免CUDA问题
            device = torch.device('cpu')  # 强制使用CPU
        except Exception as e:
            TORCH_AVAILABLE = False

    # 读取数据
    data = pd.read_csv('Exp/data/lianghe_final(时间换算后).csv')

    # 添加时间特征
    data = add_time_features(data)

    # 准备特征和标签
    feature_columns = ['001', '002', '003', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'season']
    X = data[feature_columns].values
    y = data['tp'].values

    # 划分训练集和测试集 (80/20)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 进一步划分验证集
    X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)

    # 特征标准化
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()

    X_train_scaled = scaler_X.fit_transform(X_train)
    X_val_scaled = scaler_X.transform(X_val)
    X_test_scaled = scaler_X.transform(X_test)

    y_train_scaled = scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
    y_val_scaled = scaler_y.transform(y_val.reshape(-1, 1)).flatten()

    if TORCH_AVAILABLE:
        # 转换为PyTorch张量
        X_train_tensor = torch.FloatTensor(X_train_scaled)
        y_train_tensor = torch.FloatTensor(y_train_scaled)
        X_val_tensor = torch.FloatTensor(X_val_scaled)
        y_val_tensor = torch.FloatTensor(y_val_scaled)
        X_test_tensor = torch.FloatTensor(X_test_scaled)

        # 创建数据加载器
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        val_dataset = TensorDataset(X_val_tensor, y_val_tensor)

        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    else:
        # sklearn不需要数据加载器
        train_loader = val_loader = None

    # 创建和训练MLP模型
    if TORCH_AVAILABLE:
        # PyTorch版本
        model = create_mlp_model(X_train_scaled.shape[1])
        train_losses, val_losses = train_model(model, train_loader, val_loader, num_epochs=100, device=device)
    else:
        # sklearn版本
        model = MLPRegressor(
            hidden_layer_sizes=(32, 16),
            activation='relu',
            solver='adam',
            alpha=0.001,
            learning_rate_init=0.001,
            max_iter=1000,
            early_stopping=True,
            validation_fraction=0.2,
            n_iter_no_change=15,
            random_state=42
        )
        model.fit(X_train_scaled, y_train_scaled)
        train_losses, val_losses = [], []  # sklearn doesn't provide training history
    
    # 预测
    if TORCH_AVAILABLE:
        # PyTorch版本
        model.eval()
        with torch.no_grad():
            X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
            X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
            X_test_tensor = torch.FloatTensor(X_test_scaled).to(device)

            y_train_pred_scaled = model(X_train_tensor).cpu().numpy().flatten()
            y_val_pred_scaled = model(X_val_tensor).cpu().numpy().flatten()
            y_test_pred_scaled = model(X_test_tensor).cpu().numpy().flatten()
    else:
        # sklearn版本
        y_train_pred_scaled = model.predict(X_train_scaled)
        y_val_pred_scaled = model.predict(X_val_scaled)
        y_test_pred_scaled = model.predict(X_test_scaled)

    # 反标准化
    y_train_pred = scaler_y.inverse_transform(y_train_pred_scaled.reshape(-1, 1)).flatten()
    y_val_pred = scaler_y.inverse_transform(y_val_pred_scaled.reshape(-1, 1)).flatten()
    y_test_pred = scaler_y.inverse_transform(y_test_pred_scaled.reshape(-1, 1)).flatten()
    
    # 计算评估指标
    train_rmse, train_nse, train_corr = calculate_metrics(y_train, y_train_pred)
    val_rmse, val_nse, val_corr = calculate_metrics(y_val, y_val_pred)
    test_rmse, test_nse, test_corr = calculate_metrics(y_test, y_test_pred)
    
    # 保存结果
    results_df = pd.DataFrame({
        'Actual': y_test,
        'MLP_Prediction': y_test_pred,
        'Product1': X_test[:, 0],
        'Product2': X_test[:, 1],
        'Product3': X_test[:, 2]
    })
    results_df.to_csv('Exp/mlp_fusion_predictions.csv', index=False)

if __name__ == "__main__":
    main()
