"""
基于两阶段随机森林的多源降水产品融合模型
第一阶段：分类模型判断是否有降水
第二阶段：回归模型预测降水量
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.metrics import mean_squared_error, classification_report, confusion_matrix
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def add_time_features(data):
    """添加时间特征"""
    data['time'] = pd.to_datetime(data['time'])
    data['hour'] = data['time'].dt.hour
    data['month'] = data['time'].dt.month
    data['season'] = data['month'].apply(lambda x: (x-1)//3 + 1)  # 1:春, 2:夏, 3:秋, 4:冬
    data['day_of_year'] = data['time'].dt.dayofyear
    
    # 周期性编码
    data['hour_sin'] = np.sin(2 * np.pi * data['hour'] / 24)
    data['hour_cos'] = np.cos(2 * np.pi * data['hour'] / 24)
    data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
    data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)
    
    return data

def calculate_regression_metrics(y_true, y_pred):
    """计算回归评估指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    
    # Nash-Sutcliffe Efficiency
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    
    # 相关系数
    corr = np.corrcoef(y_true, y_pred)[0, 1]
    
    # 平均绝对误差
    mae = np.mean(np.abs(y_true - y_pred))
    
    return rmse, nse, corr, mae

def create_precipitation_labels(data, threshold=0.1):
    """创建降水分类标签"""
    # 0: 无降水, 1: 有降水
    data['has_precipitation'] = (data['tp'] > threshold).astype(int)
    
    # 统计降水事件
    total_samples = len(data)
    precipitation_samples = np.sum(data['has_precipitation'])
    no_precipitation_samples = total_samples - precipitation_samples
    
    return data

class TwoStageRandomForest:
    """两阶段随机森林模型"""
    
    def __init__(self, classification_params=None, regression_params=None):
        # 分类器参数
        if classification_params is None:
            classification_params = {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42,
                'n_jobs': -1,
                'class_weight': 'balanced'  # 处理类别不平衡
            }
        
        # 回归器参数
        if regression_params is None:
            regression_params = {
                'n_estimators': 100,
                'max_depth': 15,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42,
                'n_jobs': -1
            }
        
        self.classifier = RandomForestClassifier(**classification_params)
        self.regressor = RandomForestRegressor(**regression_params)
        self.scaler = StandardScaler()
        
    def fit(self, X, y_continuous, y_binary):
        """训练两阶段模型"""
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        # 第一阶段：训练分类器
        self.classifier.fit(X_scaled, y_binary)
        
        # 第二阶段：只用有降水的样本训练回归器
        precipitation_mask = y_binary == 1
        X_precipitation = X_scaled[precipitation_mask]
        y_precipitation = y_continuous[precipitation_mask]
        
        if len(X_precipitation) > 0:
            self.regressor.fit(X_precipitation, y_precipitation)
        
    def predict(self, X):
        """两阶段预测"""
        X_scaled = self.scaler.transform(X)
        
        # 第一阶段：分类预测
        precipitation_prob = self.classifier.predict_proba(X_scaled)[:, 1]
        precipitation_pred = self.classifier.predict(X_scaled)
        
        # 第二阶段：回归预测
        regression_pred = np.zeros(len(X))
        
        # 只对预测有降水的样本进行回归
        precipitation_mask = precipitation_pred == 1
        if np.any(precipitation_mask):
            regression_pred[precipitation_mask] = self.regressor.predict(X_scaled[precipitation_mask])
        
        return precipitation_pred, precipitation_prob, regression_pred
    
    def get_feature_importance(self, feature_names):
        """获取特征重要性"""
        clf_importance = self.classifier.feature_importances_
        reg_importance = self.regressor.feature_importances_
        
        importance_df = pd.DataFrame({
            'Feature': feature_names,
            'Classification_Importance': clf_importance,
            'Regression_Importance': reg_importance
        })
        
        return importance_df.sort_values('Classification_Importance', ascending=False)

def main():
    # 读取数据
    data = pd.read_csv('Exp/data/lianghe_final(时间换算后).csv')
    
    # 添加时间特征
    data = add_time_features(data)
    
    # 创建降水分类标签
    data = create_precipitation_labels(data, threshold=0.1)
    
    # 准备特征和标签
    feature_columns = ['001', '002', '003', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'season']
    X = data[feature_columns].values
    y_continuous = data['tp'].values  # 连续降水量
    y_binary = data['has_precipitation'].values  # 二分类标签
    
    # 划分训练集和测试集 (80/20)
    X_train, X_test, y_cont_train, y_cont_test, y_bin_train, y_bin_test = train_test_split(
        X, y_continuous, y_binary, test_size=0.2, random_state=42, stratify=y_binary
    )
    
    # 训练两阶段随机森林模型
    model = TwoStageRandomForest()
    model.fit(X_train, y_cont_train, y_bin_train)
    
    # 预测
    y_bin_pred, y_bin_prob, y_cont_pred = model.predict(X_test)
    
    # 分类性能评估
    clf_accuracy = accuracy_score(y_bin_test, y_bin_pred)
    clf_precision = precision_score(y_bin_test, y_bin_pred)
    clf_recall = recall_score(y_bin_test, y_bin_pred)
    clf_f1 = f1_score(y_bin_test, y_bin_pred)
    
    # 回归性能评估（只评估有降水的样本）
    precipitation_mask = y_bin_test == 1
    if np.any(precipitation_mask):
        y_cont_test_rain = y_cont_test[precipitation_mask]
        y_cont_pred_rain = y_cont_pred[precipitation_mask]
        
        reg_rmse, reg_nse, reg_corr, reg_mae = calculate_regression_metrics(y_cont_test_rain, y_cont_pred_rain)
    
    # 整体性能评估
    overall_rmse, overall_nse, overall_corr, overall_mae = calculate_regression_metrics(y_cont_test, y_cont_pred)
    
    # 特征重要性
    importance_df = model.get_feature_importance(feature_columns)
    
    # 可视化结果
    plt.figure(figsize=(20, 15))
    
    # 混淆矩阵
    plt.subplot(3, 4, 1)
    cm = confusion_matrix(y_bin_test, y_bin_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['No Rain', 'Rain'], yticklabels=['No Rain', 'Rain'])
    plt.title('Classification Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    
    # ROC曲线相关 - 降水概率分布
    plt.subplot(3, 4, 2)
    plt.hist(y_bin_prob[y_bin_test == 0], bins=50, alpha=0.7, label='No Rain', density=True)
    plt.hist(y_bin_prob[y_bin_test == 1], bins=50, alpha=0.7, label='Rain', density=True)
    plt.xlabel('Precipitation Probability')
    plt.ylabel('Density')
    plt.title('Precipitation Probability Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 回归散点图（仅降水样本）
    if np.any(precipitation_mask):
        plt.subplot(3, 4, 3)
        plt.scatter(y_cont_test_rain, y_cont_pred_rain, alpha=0.6, s=2)
        plt.plot([y_cont_test_rain.min(), y_cont_test_rain.max()], 
                 [y_cont_test_rain.min(), y_cont_test_rain.max()], 'r--', lw=1)
        plt.xlabel('Actual Rainfall (mm)')
        plt.ylabel('Predicted Rainfall (mm)')
        plt.title(f'Regression (Rain Only)\nRMSE={reg_rmse:.4f}')
        plt.grid(True, alpha=0.3)
    
    # 整体散点图
    plt.subplot(3, 4, 4)
    plt.scatter(y_cont_test, y_cont_pred, alpha=0.6, s=2)
    plt.plot([y_cont_test.min(), y_cont_test.max()], 
             [y_cont_test.min(), y_cont_test.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Overall Performance\nRMSE={overall_rmse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 时间序列对比
    plt.subplot(3, 4, 5)
    n_show = min(500, len(y_cont_test))
    plt.plot(y_cont_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(y_cont_pred[:n_show], 'r-', linewidth=1, label='Two-Stage RF', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Time Series Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 分类特征重要性
    plt.subplot(3, 4, 6)
    plt.barh(importance_df['Feature'], importance_df['Classification_Importance'])
    plt.xlabel('Classification Importance')
    plt.title('Classification Feature Importance')
    plt.grid(True, alpha=0.3)
    
    # 回归特征重要性
    plt.subplot(3, 4, 7)
    plt.barh(importance_df['Feature'], importance_df['Regression_Importance'])
    plt.xlabel('Regression Importance')
    plt.title('Regression Feature Importance')
    plt.grid(True, alpha=0.3)
    
    # 残差分析
    plt.subplot(3, 4, 8)
    residuals = y_cont_test - y_cont_pred
    plt.scatter(y_cont_pred, residuals, alpha=0.5, s=1)
    plt.axhline(y=0, color='r', linestyle='--', lw=1)
    plt.xlabel('Predicted Rainfall (mm)')
    plt.ylabel('Residuals (mm)')
    plt.title('Residual Plot')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Exp/two_stage_random_forest_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存结果
    results_df = pd.DataFrame({
        'Actual_Continuous': y_cont_test,
        'Actual_Binary': y_bin_test,
        'Predicted_Continuous': y_cont_pred,
        'Predicted_Binary': y_bin_pred,
        'Precipitation_Probability': y_bin_prob,
        'Product1': X_test[:, 0],
        'Product2': X_test[:, 1],
        'Product3': X_test[:, 2]
    })
    results_df.to_csv('Exp/two_stage_random_forest_predictions.csv', index=False)
    
    # 保存特征重要性
    importance_df.to_csv('Exp/two_stage_random_forest_feature_importance.csv', index=False)

if __name__ == "__main__":
    main()
