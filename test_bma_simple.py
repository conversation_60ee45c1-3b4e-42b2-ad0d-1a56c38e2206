#!/usr/bin/env python3
"""
简化的BMA算法测试脚本
不依赖复杂的库，只使用基础的数学运算
"""

import math
import random

def calculate_bma_weights_robust(models, observed):
    """
    稳健的BMA权重计算算法
    """
    num_models = len(models)
    n = len(observed)

    if n == 0:
        return [1/num_models] * num_models

    # 计算各模型的性能指标
    performances = []
    for model in models:
        residuals = [model[i] - observed[i] for i in range(n)]
        mse = sum(r*r for r in residuals) / n
        mae = sum(abs(r) for r in residuals) / n
        bias = abs(sum(residuals) / n)

        # 计算相关系数
        mean_obs = sum(observed) / n
        mean_pred = sum(model) / n

        numerator = sum((observed[i] - mean_obs) * (model[i] - mean_pred) for i in range(n))
        denom_obs = sum((observed[i] - mean_obs)**2 for i in range(n))
        denom_pred = sum((model[i] - mean_pred)**2 for i in range(n))

        correlation = numerator / math.sqrt(denom_obs * denom_pred) if denom_obs > 0 and denom_pred > 0 else 0

        performances.append({
            'mse': mse,
            'mae': mae,
            'bias': bias,
            'correlation': max(0, correlation)
        })

    # 基于多个指标计算综合得分
    scores = []
    for perf in performances:
        mse_score = 1 / (1 + perf['mse'])
        mae_score = 1 / (1 + perf['mae'])
        bias_score = 1 / (1 + perf['bias'])
        corr_score = perf['correlation']

        # 综合得分（可调整权重）
        score = 0.3 * mse_score + 0.3 * mae_score + 0.2 * bias_score + 0.2 * corr_score
        scores.append(score)

    # 使用温度参数控制的softmax计算权重
    temperature = 2.0  # 温度参数，越大权重分布越平均
    max_score = max(scores)
    exp_scores = [math.exp((score - max_score) / temperature) for score in scores]
    sum_exp_scores = sum(exp_scores)

    weights = [exp_score / sum_exp_scores for exp_score in exp_scores]

    # 应用权重约束：确保没有权重过小或过大
    min_weight = 0.15  # 最小权重15%
    max_weight = 0.70  # 最大权重70%

    # 第一步：确保最小权重
    weights = [max(w, min_weight) for w in weights]
    weight_sum = sum(weights)
    weights = [w / weight_sum for w in weights]

    # 第二步：限制最大权重
    for i in range(len(weights)):
        if weights[i] > max_weight:
            excess = weights[i] - max_weight
            weights[i] = max_weight

            # 将多余的权重分配给其他模型
            other_indices = [idx for idx in range(len(weights)) if idx != i]
            redistribute_weight = excess / len(other_indices)
            for idx in other_indices:
                weights[idx] += redistribute_weight

    # 最终归一化
    weight_sum = sum(weights)
    weights = [w / weight_sum for w in weights]

    return weights, performances

def test_scenarios():
    """测试不同场景"""
    
    print("=" * 60)
    print("BMA算法测试")
    print("=" * 60)
    
    # 测试场景1：模拟数据
    print("\n测试场景1：基础模拟数据")
    observed = [10, 15, 8, 12, 20, 18, 5, 14, 16, 11]
    product1 = [9, 14, 7, 11, 19, 17, 4, 13, 15, 10]  # 较好的预报
    product2 = [12, 18, 10, 15, 23, 21, 8, 17, 19, 14]  # 中等预报
    product3 = [8, 13, 6, 10, 18, 16, 3, 12, 14, 9]  # 较差的预报
    
    models = [product1, product2, product3]
    weights, performances = calculate_bma_weights_robust(models, observed)

    print(f"权重分配: 产品1: {weights[0]*100:.1f}%, 产品2: {weights[1]*100:.1f}%, 产品3: {weights[2]*100:.1f}%")
    print("性能指标:")
    for i, perf in enumerate(performances):
        print(f"  产品{i+1}: MSE={perf['mse']:.4f}, MAE={perf['mae']:.4f}, Bias={perf['bias']:.4f}, Corr={perf['correlation']:.4f}")

    # 测试场景2：一个模型明显更好
    print("\n测试场景2：一个模型明显更好")
    observed = [10, 15, 8, 12, 20, 18, 5, 14, 16, 11]
    product1 = [10.1, 15.1, 7.9, 12.1, 20.1, 17.9, 5.1, 14.1, 15.9, 11.1]  # 非常接近
    product2 = [15, 20, 13, 17, 25, 23, 10, 19, 21, 16]  # 系统性偏高
    product3 = [5, 10, 3, 7, 15, 13, 0, 9, 11, 6]  # 系统性偏低

    models = [product1, product2, product3]
    weights, performances = calculate_bma_weights_robust(models, observed)

    print(f"权重分配: 产品1: {weights[0]*100:.1f}%, 产品2: {weights[1]*100:.1f}%, 产品3: {weights[2]*100:.1f}%")
    print("性能指标:")
    for i, perf in enumerate(performances):
        print(f"  产品{i+1}: MSE={perf['mse']:.4f}, MAE={perf['mae']:.4f}, Bias={perf['bias']:.4f}, Corr={perf['correlation']:.4f}")

    # 测试场景3：所有模型表现相似
    print("\n测试场景3：所有模型表现相似")
    observed = [10, 15, 8, 12, 20, 18, 5, 14, 16, 11]
    product1 = [9.5, 14.5, 7.5, 11.5, 19.5, 17.5, 4.5, 13.5, 15.5, 10.5]
    product2 = [10.5, 15.5, 8.5, 12.5, 20.5, 18.5, 5.5, 14.5, 16.5, 11.5]
    product3 = [9.8, 14.8, 7.8, 11.8, 19.8, 17.8, 4.8, 13.8, 15.8, 10.8]

    models = [product1, product2, product3]
    weights, performances = calculate_bma_weights_robust(models, observed)

    print(f"权重分配: 产品1: {weights[0]*100:.1f}%, 产品2: {weights[1]*100:.1f}%, 产品3: {weights[2]*100:.1f}%")
    print("性能指标:")
    for i, perf in enumerate(performances):
        print(f"  产品{i+1}: MSE={perf['mse']:.4f}, MAE={perf['mae']:.4f}, Bias={perf['bias']:.4f}, Corr={perf['correlation']:.4f}")

    # 测试场景4：真实数据模拟
    print("\n测试场景4：真实降雨数据模拟")
    observed = [2.5, 0.8, 4.2, 1.6, 3.1, 0.5, 2.8, 1.9, 3.7, 0.9, 2.2, 1.4, 3.5, 0.7, 2.6]
    product1 = [2.8, 1.1, 4.5, 1.9, 3.4, 0.8, 3.1, 2.2, 4.0, 1.2, 2.5, 1.7, 3.8, 1.0, 2.9]  # GPM
    product2 = [2.3, 0.6, 3.9, 1.3, 2.8, 0.3, 2.5, 1.6, 3.4, 0.7, 1.9, 1.1, 3.2, 0.4, 2.3]  # TRMM
    product3 = [2.7, 0.9, 4.3, 1.7, 3.2, 0.6, 2.9, 2.0, 3.8, 1.0, 2.3, 1.5, 3.6, 0.8, 2.7]  # CMORPH

    models = [product1, product2, product3]
    weights, performances = calculate_bma_weights_robust(models, observed)

    print(f"权重分配: 产品1: {weights[0]*100:.1f}%, 产品2: {weights[1]*100:.1f}%, 产品3: {weights[2]*100:.1f}%")
    print("性能指标:")
    for i, perf in enumerate(performances):
        print(f"  产品{i+1}: MSE={perf['mse']:.4f}, MAE={perf['mae']:.4f}, Bias={perf['bias']:.4f}, Corr={perf['correlation']:.4f}")
    
    # 计算各模型的RMSE用于对比
    print("\n各模型RMSE对比:")
    for i, model in enumerate(models):
        rmse = math.sqrt(sum((model[j] - observed[j])**2 for j in range(len(observed))) / len(observed))
        print(f"产品{i+1} RMSE: {rmse:.4f}")

if __name__ == "__main__":
    test_scenarios()
