<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流域选择功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .basin-selection {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }
        .config-display {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>流域选择功能测试</h1>

        <!-- 模拟input-grid布局 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
            <!-- 流域选择 -->
            <div>
                <label for="basinSelect" style="font-weight: bold; color: #2c3e50; margin-bottom: 8px; display: block;">
                    🏞️ 选择流域:
                </label>
                <select id="basinSelect" onchange="onBasinChange()" style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px; background: white; cursor: pointer;">
                    <option value="">请选择流域...</option>
                    <option value="lianghe">两河 (Lianghe)</option>
                    <option value="yantang">沿塘 (Yantang)</option>
                    <option value="yinhe">银河 (Yinhe)</option>
                </select>
            </div>
            <!-- 模拟其他输入框 -->
            <div>
                <label style="font-weight: bold; color: #2c3e50; margin-bottom: 8px; display: block;">起始时间:</label>
                <input type="datetime-local" style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px;" value="2024-01-01T00:00">
            </div>
            <div>
                <label style="font-weight: bold; color: #2c3e50; margin-bottom: 8px; display: block;">结束时间:</label>
                <input type="datetime-local" style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px;" value="2024-01-01T23:00">
            </div>
        </div>

        <!-- 配置显示 -->
        <div class="config-display" id="configDisplay" style="display: none;">
            <h3>当前流域配置</h3>
            <div id="configContent"></div>
        </div>

        <!-- 测试按钮 -->
        <div class="test-section">
            <h3>测试功能</h3>
            <button onclick="testBMAWeights()" style="margin: 5px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                测试BMA权重计算
            </button>
            <button onclick="showCurrentConfig()" style="margin: 5px; padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                显示当前配置
            </button>
            <div id="testResults" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; white-space: pre-wrap;"></div>
        </div>
    </div>

    <script>
        // 流域选择处理函数
        function onBasinChange() {
            const basinSelect = document.getElementById('basinSelect');
            const selectedBasin = basinSelect.value;
            
            if (selectedBasin) {
                // 显示选择的流域信息
                showBasinInfo(selectedBasin);
                
                // 根据流域调整默认参数
                adjustParametersForBasin(selectedBasin);
                
                // 显示配置
                showCurrentConfig();
            } else {
                // 隐藏流域信息
                const infoDiv = document.getElementById('basinInfo');
                if (infoDiv) {
                    infoDiv.remove();
                }
                document.getElementById('configDisplay').style.display = 'none';
            }
        }

        function showBasinInfo(basin) {
            const basinNames = {
                'lianghe': '两河流域',
                'yantang': '沿塘流域', 
                'yinhe': '银河流域'
            };
            
            const basinDescriptions = {
                'lianghe': '两河流域位于长江中下游地区，流域面积约1200平方公里，主要河流包括东河和西河。',
                'yantang': '沿塘流域位于太湖流域南部，流域面积约800平方公里，地势平坦，河网密布。',
                'yinhe': '银河流域位于珠江流域北部，流域面积约950平方公里，山地丘陵地形为主。'
            };
            
            // 创建或更新流域信息显示
            let infoDiv = document.getElementById('basinInfo');
            if (!infoDiv) {
                infoDiv = document.createElement('div');
                infoDiv.id = 'basinInfo';
                infoDiv.style.cssText = `
                    margin: 15px 0;
                    padding: 12px;
                    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
                    border-radius: 8px;
                    border-left: 4px solid #2196f3;
                    font-size: 13px;
                    line-height: 1.4;
                `;
                // 将信息显示在模拟的input-grid后面
                const inputGrid = document.querySelector('div[style*="grid-template-columns"]');
                inputGrid.parentNode.insertBefore(infoDiv, inputGrid.nextSibling);
            }
            
            infoDiv.innerHTML = `
                <div style="font-weight: bold; color: #1976d2; margin-bottom: 5px;">
                    📍 已选择：${basinNames[basin]}
                </div>
                <div style="color: #555;">
                    ${basinDescriptions[basin]}
                </div>
            `;
        }

        function adjustParametersForBasin(basin) {
            // 根据不同流域调整BMA算法参数
            const basinConfigs = {
                'lianghe': {
                    temperature: 2.0,
                    minWeight: 0.15,
                    maxWeight: 0.70,
                    description: '山区流域，降雨变化较大'
                },
                'yantang': {
                    temperature: 1.8,
                    minWeight: 0.18,
                    maxWeight: 0.65,
                    description: '平原流域，降雨相对均匀'
                },
                'yinhe': {
                    temperature: 2.2,
                    minWeight: 0.12,
                    maxWeight: 0.75,
                    description: '丘陵流域，降雨时空分布不均'
                }
            };
            
            // 存储当前流域配置供后续使用
            window.currentBasinConfig = basinConfigs[basin];
            
            console.log(`已切换到${basin}流域配置:`, window.currentBasinConfig);
            
            // 显示成功消息
            showMessage(`✅ 已切换到${basin === 'lianghe' ? '两河' : basin === 'yantang' ? '沿塘' : '银河'}流域配置`);
        }

        function showCurrentConfig() {
            const configDisplay = document.getElementById('configDisplay');
            const configContent = document.getElementById('configContent');
            
            if (window.currentBasinConfig) {
                configContent.innerHTML = `
                    <p><strong>温度参数:</strong> ${window.currentBasinConfig.temperature}</p>
                    <p><strong>最小权重:</strong> ${(window.currentBasinConfig.minWeight * 100).toFixed(1)}%</p>
                    <p><strong>最大权重:</strong> ${(window.currentBasinConfig.maxWeight * 100).toFixed(1)}%</p>
                    <p><strong>描述:</strong> ${window.currentBasinConfig.description}</p>
                `;
                configDisplay.style.display = 'block';
            } else {
                configContent.innerHTML = '<p style="color: #666;">请先选择流域</p>';
                configDisplay.style.display = 'block';
            }
        }

        function testBMAWeights() {
            if (!window.currentBasinConfig) {
                alert('请先选择流域！');
                return;
            }

            // 模拟测试数据
            const observed = [2.5, 1.8, 3.2, 1.6, 2.9];
            const models = [
                [2.3, 1.9, 3.0, 1.7, 2.8], // 产品1
                [2.7, 1.6, 3.5, 1.4, 3.1], // 产品2
                [2.4, 1.8, 3.1, 1.6, 2.9]  // 产品3
            ];

            const weights = calculateBMAWeights(models, observed);
            
            const results = `
测试结果：
流域配置: ${JSON.stringify(window.currentBasinConfig, null, 2)}

BMA权重分配:
产品1: ${(weights[0] * 100).toFixed(1)}%
产品2: ${(weights[1] * 100).toFixed(1)}%
产品3: ${(weights[2] * 100).toFixed(1)}%

权重总和: ${weights.reduce((sum, w) => sum + w, 0).toFixed(3)}
            `;
            
            document.getElementById('testResults').textContent = results;
        }

        // 简化的BMA权重计算函数（用于测试）
        function calculateBMAWeights(models, observed) {
            const numModels = models.length;
            const n = observed.length;

            if (n === 0) {
                return new Array(numModels).fill(1 / numModels);
            }

            // 使用流域特定的参数或默认参数
            const basinConfig = window.currentBasinConfig || {
                temperature: 2.0,
                minWeight: 0.15,
                maxWeight: 0.70
            };

            // 计算简化的性能得分
            const scores = models.map(model => {
                const mse = model.reduce((sum, pred, i) => sum + Math.pow(pred - observed[i], 2), 0) / n;
                return 1 / (1 + mse); // 简化得分
            });

            // 使用温度参数控制的softmax计算权重
            const temperature = basinConfig.temperature;
            const maxScore = Math.max(...scores);
            const expScores = scores.map(score => Math.exp((score - maxScore) / temperature));
            const sumExpScores = expScores.reduce((sum, exp) => sum + exp, 0);
            
            let weights = expScores.map(exp => exp / sumExpScores);

            // 应用权重约束
            const minWeight = basinConfig.minWeight;
            const maxWeight = basinConfig.maxWeight;
            
            weights = weights.map(w => Math.max(w, minWeight));
            let weightSum = weights.reduce((sum, w) => sum + w, 0);
            weights = weights.map(w => w / weightSum);
            
            // 限制最大权重
            for (let i = 0; i < weights.length; i++) {
                if (weights[i] > maxWeight) {
                    const excess = weights[i] - maxWeight;
                    weights[i] = maxWeight;
                    
                    const otherIndices = weights.map((_, idx) => idx).filter(idx => idx !== i);
                    const redistributeWeight = excess / otherIndices.length;
                    otherIndices.forEach(idx => {
                        weights[idx] += redistributeWeight;
                    });
                }
            }
            
            // 最终归一化
            weightSum = weights.reduce((sum, w) => sum + w, 0);
            return weights.map(w => w / weightSum);
        }

        function showMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4caf50;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                animation: slideInRight 0.3s ease-out;
            `;
            messageDiv.innerHTML = message;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
