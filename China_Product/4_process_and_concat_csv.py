#!/usr/bin/env python3
"""
处理每日文件夹下的三个CSV文件，满足以下要求：
1. 以valid_time的时间为准
2. 将每个时间点的多个坐标的tp数据进行相加，得到总的tp值
3. 只保留当天09:00:00到第二天早上08:00:00的24小时数据
4. 最终仅保留valid_time列和tp列
5. 将每天处理后的数据进行拼接，得到完整的2024年数据
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import traceback

def process_single_csv(csv_file_path):
    """
    处理单个CSV文件
    
    Parameters:
    -----------
    csv_file_path : str or Path
        CSV文件路径
    
    Returns:
    --------
    pd.DataFrame : 处理后的数据，包含valid_time和tp列
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file_path)
        
        # 转换valid_time为datetime类型
        df['valid_time'] = pd.to_datetime(df['valid_time'])
        
        # 按valid_time分组，对tp进行求和
        # 这样每个时间点只有一个tp值（多个坐标的总和）
        grouped_df = df.groupby('valid_time')['tp'].sum().reset_index()
        
        # 获取第一个时间点的日期，用于确定24小时范围
        first_time = grouped_df['valid_time'].min()
        start_date = first_time.date()
        
        # 定义24小时时间范围：当天09:00到第二天08:00
        start_time = pd.Timestamp(f"{start_date} 09:00:00")
        end_time = start_time + timedelta(hours=23)  # 第二天08:00
        
        # 筛选24小时数据
        mask = (grouped_df['valid_time'] >= start_time) & (grouped_df['valid_time'] <= end_time)
        filtered_df = grouped_df[mask].copy()
        
        # 只保留valid_time和tp列
        result_df = filtered_df[['valid_time', 'tp']].copy()
        
        # 按时间排序
        result_df = result_df.sort_values('valid_time').reset_index(drop=True)
        
        return result_df
        
    except Exception as e:
        print(f"处理文件 {csv_file_path} 时出错: {e}")
        return None

def process_all_watersheds():
    """
    处理所有流域的数据并生成最终的拼接文件
    """
    
    # 定义流域名称
    # watersheds = ['lianghe', 'yantang', 'yinhe']
    # watersheds = ['chengkou', 'guojia']
    watersheds = ['mituo', 'dongxi']
    
    # 源目录
    source_dir = Path("China_Product/2024_final")
    output_dir = Path("China_Product")
    
    if not source_dir.exists():
        print(f"错误：源目录 {source_dir} 不存在！")
        return
    
    # 获取所有日期文件夹
    date_folders = [f for f in source_dir.iterdir() if f.is_dir() and f.name.isdigit()]
    date_folders.sort()
    
    print(f"🚀 开始处理 {len(date_folders)} 个日期文件夹...")
    print(f"流域: {watersheds}")
    print("=" * 60)
    
    # 为每个流域创建数据列表
    watershed_data = {watershed: [] for watershed in watersheds}
    
    # 统计信息
    stats = {
        'total_folders': len(date_folders),
        'processed_folders': 0,
        'failed_folders': [],
        'watershed_stats': {name: {'success': 0, 'failed': 0} for name in watersheds}
    }
    
    # 处理每个日期文件夹
    for i, date_folder in enumerate(date_folders, 1):
        print(f"📁 处理 {i}/{len(date_folders)}: {date_folder.name}")
        
        folder_success = True
        
        # 处理每个流域
        for watershed in watersheds:
            try:
                # 查找对应流域的CSV文件
                csv_files = list(date_folder.glob(f"{watershed}_*.csv"))
                
                if not csv_files:
                    print(f"  ❌ {watershed}: 未找到CSV文件")
                    stats['watershed_stats'][watershed]['failed'] += 1
                    folder_success = False
                    continue
                
                if len(csv_files) > 1:
                    print(f"  ❌ {watershed}: 找到多个CSV文件")
                    stats['watershed_stats'][watershed]['failed'] += 1
                    folder_success = False
                    continue
                
                csv_file = csv_files[0]
                
                # 处理CSV文件
                processed_data = process_single_csv(csv_file)
                
                if processed_data is not None and len(processed_data) > 0:
                    watershed_data[watershed].append(processed_data)
                    stats['watershed_stats'][watershed]['success'] += 1
                    print(f"  ✅ {watershed}: 成功处理，{len(processed_data)} 条记录")
                else:
                    print(f"  ❌ {watershed}: 处理失败或无数据")
                    stats['watershed_stats'][watershed]['failed'] += 1
                    folder_success = False
                
            except Exception as e:
                print(f"  ❌ {watershed}: 处理异常 - {e}")
                stats['watershed_stats'][watershed]['failed'] += 1
                folder_success = False
        
        if folder_success:
            stats['processed_folders'] += 1
        else:
            stats['failed_folders'].append(date_folder.name)
    
    print("\n" + "=" * 60)
    print("📊 处理统计结果：")
    print(f"总文件夹数: {stats['total_folders']}")
    print(f"成功处理: {stats['processed_folders']}")
    print(f"失败文件夹: {len(stats['failed_folders'])}")
    
    print("\n📈 各流域统计：")
    for watershed, stat in stats['watershed_stats'].items():
        total = stat['success'] + stat['failed']
        success_rate = stat['success']/total*100 if total > 0 else 0
        print(f"  {watershed}: 成功 {stat['success']}, 失败 {stat['failed']}, 成功率 {success_rate:.1f}%")
    
    # 拼接每个流域的数据
    print("\n🔗 开始拼接数据...")
    for watershed in watersheds:
        if watershed_data[watershed]:
            try:
                # 拼接所有数据
                combined_df = pd.concat(watershed_data[watershed], ignore_index=True)
                
                # 按时间排序
                combined_df = combined_df.sort_values('valid_time').reset_index(drop=True)
                
                # 保存到文件
                output_file = output_dir / f"{watershed}_2024_hourly.csv"
                combined_df.to_csv(output_file, index=False)
                
                print(f"✅ {watershed}: 拼接完成，共 {len(combined_df)} 条记录")
                print(f"   时间范围: {combined_df['valid_time'].min()} 到 {combined_df['valid_time'].max()}")
                print(f"   保存到: {output_file}")
                
                # 显示数据预览
                print(f"   数据预览:")
                print(f"     前5行: {combined_df.head()['valid_time'].tolist()}")
                print(f"     后5行: {combined_df.tail()['valid_time'].tolist()}")
                
            except Exception as e:
                print(f"❌ {watershed}: 拼接失败 - {e}")
                print(f"   详细错误: {traceback.format_exc()}")
        else:
            print(f"❌ {watershed}: 无数据可拼接")
    
    if stats['failed_folders']:
        print(f"\n❌ 失败的文件夹 ({len(stats['failed_folders'])} 个):")
        for failed in stats['failed_folders'][:10]:  # 只显示前10个
            print(f"  {failed}")
        if len(stats['failed_folders']) > 10:
            print(f"  ... 还有 {len(stats['failed_folders'])-10} 个失败文件夹")
    
    return stats

def validate_output_files():
    """
    验证输出文件的质量
    """
    print("\n🔍 验证输出文件...")
    
    # watersheds = ['lianghe', 'yantang', 'yinhe']
    # watersheds = ['chengkou', 'guojia']
    watersheds = ['mituo', 'dongxi']
    output_dir = Path("China_Product")
    
    for watershed in watersheds:
        output_file = output_dir / f"{watershed}_2024_hourly.csv"
        
        if output_file.exists():
            try:
                df = pd.read_csv(output_file)
                df['valid_time'] = pd.to_datetime(df['valid_time'])
                
                print(f"\n📋 {watershed} 文件验证:")
                print(f"   文件大小: {output_file.stat().st_size / 1024 / 1024:.2f} MB")
                print(f"   总记录数: {len(df)}")
                print(f"   时间范围: {df['valid_time'].min()} 到 {df['valid_time'].max()}")
                print(f"   时间跨度: {(df['valid_time'].max() - df['valid_time'].min()).days} 天")
                
                # 检查时间连续性
                time_diff = df['valid_time'].diff().dropna()
                expected_diff = pd.Timedelta(hours=1)
                irregular_intervals = time_diff[time_diff != expected_diff]

                if len(irregular_intervals) == 0:
                    print(f"   ✅ 时间间隔: 完全连续（1小时间隔）")
                else:
                    print(f"   ⚠️  时间间隔: 发现 {len(irregular_intervals)} 个不规律间隔")

                    # 显示不规律间隔的详细信息
                    print(f"   📍 不规律间隔详情:")
                    for idx, interval in irregular_intervals.items():
                        prev_time = df.loc[idx-1, 'valid_time']
                        curr_time = df.loc[idx, 'valid_time']
                        print(f"     位置 {idx}: {prev_time} -> {curr_time} (间隔: {interval})")

                        # 检查是否有缺失的日期
                        if interval > pd.Timedelta(hours=1):
                            missing_hours = int(interval.total_seconds() / 3600) - 1
                            print(f"       ⚠️  缺失 {missing_hours} 小时的数据")
                
                # 检查tp值统计
                tp_stats = df['tp'].describe()
                print(f"   降水统计: 最小值={tp_stats['min']:.3f}, 最大值={tp_stats['max']:.3f}, 平均值={tp_stats['mean']:.3f}")
                
            except Exception as e:
                print(f"❌ {watershed}: 验证失败 - {e}")
        else:
            print(f"❌ {watershed}: 输出文件不存在")

if __name__ == "__main__":
    print("🌧️  开始处理和拼接降水预报数据...")
    print("=" * 60)
    
    # 处理所有数据
    results = process_all_watersheds()
    
    if results:
        # 验证输出文件
        validate_output_files()
        
        print("\n🎉 处理完成！")
        print("生成的文件:")
        print("  - mituo_2025_hourly.csv")
        print("  - dongxi_2025_hourly.csv")        
        # print("  - chengkou_2025_hourly.csv")
        # print("  - guojia_2025_hourly.csv")
        # print("  - lianghe_2025_hourly.csv")
        # print("  - yantang_2025_hourly.csv") 
        # print("  - yinhe_2025_hourly.csv")
        print("\n每个文件包含:")
        # print("  - valid_time: 预测时间（2024年3月28日09:00 到 2025年1月1日08:00）")
        print("  - tp: 降水量总和（流域内所有网格点的降水量之和）")
        print("  - 时间频率: 1小时")
    else:
        print("❌ 处理失败！")
