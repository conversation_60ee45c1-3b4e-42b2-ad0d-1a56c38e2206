#!/usr/bin/env python3
"""
检查China_Procduct/2025文件夹中的日期完整性和文件存在情况
从2025年1月1日到2025年5月26日
"""

import os
import datetime
from pathlib import Path

def generate_date_range(start_date, end_date):
    """生成日期范围"""
    current_date = start_date
    dates = []
    while current_date <= end_date:
        dates.append(current_date.strftime("%Y%m%d"))
        current_date += datetime.timedelta(days=1)
    return dates

def check_directory_and_files(base_path):
    """检查目录和文件存在情况"""
    # 定义日期范围
    start_date = datetime.date(2025, 1, 1)
    end_date = datetime.date(2025, 5, 26)
    
    # 生成所有应该存在的日期
    expected_dates = generate_date_range(start_date, end_date)
    
    print(f"检查期间：{start_date} 到 {end_date}")
    print(f"总共应该有 {len(expected_dates)} 个日期文件夹")
    print("=" * 60)
    
    # 检查实际存在的文件夹
    base_dir = Path(base_path)
    if not base_dir.exists():
        print(f"错误：基础目录 {base_path} 不存在！")
        return
    
    existing_folders = []
    for item in base_dir.iterdir():
        if item.is_dir() and item.name.isdigit() and len(item.name) == 8:
            existing_folders.append(item.name)
    
    existing_folders.sort()
    
    # 找出缺失的日期
    missing_dates = []
    for date in expected_dates:
        if date not in existing_folders:
            missing_dates.append(date)
    
    # 找出多余的日期（不在预期范围内的）
    extra_dates = []
    for folder in existing_folders:
        if folder not in expected_dates:
            extra_dates.append(folder)
    
    # 检查每个存在的文件夹是否包含文件
    empty_folders = []
    folders_with_files = []
    
    for date_folder in existing_folders:
        folder_path = base_dir / date_folder
        files = list(folder_path.glob("*"))
        # 过滤掉临时文件（以~结尾的文件）
        actual_files = [f for f in files if not f.name.endswith('~')]
        
        if len(actual_files) == 0:
            empty_folders.append(date_folder)
        else:
            folders_with_files.append((date_folder, len(actual_files)))
    
    # 输出结果
    print("📊 统计结果：")
    print(f"✅ 存在的日期文件夹：{len(existing_folders)} 个")
    print(f"❌ 缺失的日期文件夹：{len(missing_dates)} 个")
    print(f"⚠️  多余的日期文件夹：{len(extra_dates)} 个")
    print(f"📁 空文件夹：{len(empty_folders)} 个")
    print(f"📄 有文件的文件夹：{len(folders_with_files)} 个")
    print()
    
    # 详细输出缺失的日期
    if missing_dates:
        print("❌ 缺失的日期文件夹：")
        for i, date in enumerate(missing_dates):
            # 转换为可读格式
            readable_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
            print(f"  {i+1:3d}. {date} ({readable_date})")
        print()
    
    # 详细输出多余的日期
    if extra_dates:
        print("⚠️  多余的日期文件夹（不在预期范围内）：")
        for i, date in enumerate(extra_dates):
            readable_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
            print(f"  {i+1:3d}. {date} ({readable_date})")
        print()
    
    # 详细输出空文件夹
    if empty_folders:
        print("📁 空文件夹（没有文件）：")
        for i, date in enumerate(empty_folders):
            readable_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
            print(f"  {i+1:3d}. {date} ({readable_date})")
        print()
    
    # 输出文件数量统计
    if folders_with_files:
        print("📄 有文件的文件夹及文件数量：")
        print("    日期文件夹    |  文件数量")
        print("    " + "-" * 30)
        for date, file_count in folders_with_files:
            readable_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
            print(f"    {date} ({readable_date}) | {file_count:3d} 个文件")
        print()
    
    # 总结
    print("=" * 60)
    print("📋 总结：")
    if not missing_dates and not empty_folders:
        print("✅ 所有日期文件夹都存在且包含文件！")
    else:
        if missing_dates:
            print(f"❌ 发现 {len(missing_dates)} 个缺失的日期")
        if empty_folders:
            print(f"📁 发现 {len(empty_folders)} 个空文件夹")
    
    return {
        'total_expected': len(expected_dates),
        'existing_folders': len(existing_folders),
        'missing_dates': missing_dates,
        'extra_dates': extra_dates,
        'empty_folders': empty_folders,
        'folders_with_files': folders_with_files
    }

if __name__ == "__main__":
    # 检查China_Procduct/2025目录
    base_path = "China_Procduct/2025"
    result = check_directory_and_files(base_path)
