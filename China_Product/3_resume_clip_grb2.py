#!/usr/bin/env python3
"""
恢复GRB2文件批量裁剪任务
从第141个文件夹开始继续处理
"""

import os
import xarray as xr
import geopandas as gpd
import rioxarray as rxr
from shapely.geometry import mapping
import pandas as pd
import numpy as np
from pathlib import Path
import traceback
import gc  # 垃圾回收

def clip_grib_with_shapefile(grib_file, shapefile_path, output_csv=None):
    """
    使用shapefile裁剪GRIB2文件，优化内存管理
    """
    
    ds = None
    gdf = None
    clipped_ds = None
    df = None
    
    try:
        # 1. 读取GRIB2文件
        ds = xr.open_dataset(grib_file, engine='cfgrib')
        
        # 2. 设置空间参考系统 (CRS)
        ds = ds.rio.write_crs("EPSG:4326")
        
        # 3. 读取 shapefile
        gdf = gpd.read_file(shapefile_path)

        # 如果 shapefile 没有 crs，就手动设置为 EPSG:4326
        if gdf.crs is None:
            gdf = gdf.set_crs("EPSG:4326")

        # 如果原始是其他坐标系，再转换
        elif gdf.crs.to_string() != "EPSG:4326":
            gdf = gdf.to_crs("EPSG:4326")

        # 4. 进行裁剪
        clipped_ds = ds.rio.clip(gdf.geometry.apply(mapping), gdf.crs, drop=False)
        
        # 5. 转换为DataFrame并保存CSV（可选）
        if output_csv:
            df = clipped_ds.to_dataframe().reset_index()
            df = df.dropna()  # 移除NaN值
            
            # 删除不需要的列并重命名
            if 'surface' in df.columns:
                df = df.drop('surface', axis=1)
            if 'spatial_ref' in df.columns:
                df = df.drop('spatial_ref', axis=1)
            df = df.rename(columns={'unknown': 'tp'})
            
            # 重新排列列的顺序
            df = df[['time', 'step', 'valid_time', 'latitude', 'longitude', 'tp']]

            df.to_csv(output_csv, index=False)
            
            result = df.shape
        else:
            result = None
            
    finally:
        # 强制释放内存
        if ds is not None:
            ds.close()
            del ds
        if gdf is not None:
            del gdf
        if clipped_ds is not None:
            del clipped_ds
        if df is not None:
            del df
        gc.collect()  # 强制垃圾回收
    
    return result

def process_remaining_folders():
    """
    处理剩余的文件夹（从第141个开始）
    """
    
    # 定义流域和对应的shapefile路径
    watersheds = {
        # 'lianghe': 'boundary/lianghe.shp',
        # 'yantang': 'boundary/yantang.shp',
        # 'yinhe': 'boundary/yinhe.shp',
        # 'chengkou': 'boundary/chengkou.shp',
        # 'guojia': 'boundary/guojia.shp'
        'dongxi' : 'boundary/dongxi.shp',
        'mituo': 'boundary/mituo.shp'
    }
    
    # 源目录
    source_directory = "China_Product/2024_final"
    source_path = Path(source_directory)
    
    if not source_path.exists():
        print(f"错误：源目录 {source_directory} 不存在！")
        return None
    
    # 获取所有日期文件夹
    date_folders = [f for f in source_path.iterdir() if f.is_dir() and f.name.isdigit()]
    date_folders.sort()
    
    # 从第141个文件夹开始（索引140）
    remaining_folders = date_folders[218:]  # 从20240815开始
    
    print(f"🚀 恢复处理剩余的 {len(remaining_folders)} 个文件夹...")
    print(f"开始日期: {remaining_folders[0].name if remaining_folders else '无'}")
    print(f"结束日期: {remaining_folders[-1].name if remaining_folders else '无'}")
    print("=" * 60)
    
    # 统计信息
    total_stats = {
        'total_folders': len(remaining_folders),
        'processed_folders': 0,
        'total_success': 0,
        'total_failed': 0,
        'total_skipped': 0,
        'failed_folders': []
    }
    
    # 处理每个日期文件夹
    for i, date_folder in enumerate(remaining_folders, 1):
        print(f"📁 处理 {i}/{len(remaining_folders)}: {date_folder.name}")
        
        try:
            # 查找GRB2文件
            grb2_files = list(date_folder.glob("*.GRB2"))
            
            if not grb2_files:
                print(f"❌ {date_folder.name}: 未找到GRB2文件")
                total_stats['failed_folders'].append({
                    'date': date_folder.name,
                    'errors': ['未找到GRB2文件']
                })
                continue
            
            if len(grb2_files) > 1:
                print(f"❌ {date_folder.name}: 找到多个GRB2文件")
                total_stats['failed_folders'].append({
                    'date': date_folder.name,
                    'errors': ['找到多个GRB2文件']
                })
                continue
            
            grb2_file = grb2_files[0]
            folder_success = 0
            folder_failed = 0
            folder_skipped = 0
            
            # 为每个流域进行裁剪
            for watershed_name, shapefile_path in watersheds.items():
                try:
                    # 创建输出文件路径
                    output_filename = f"{watershed_name}_{grb2_file.stem}.csv"
                    output_file_path = date_folder / output_filename
                    
                    # 检查文件是否已存在
                    if output_file_path.exists():
                        print(f"⏭️  {date_folder.name}/{watershed_name}: 文件已存在，跳过")
                        folder_skipped += 1
                        continue
                    
                    # 执行裁剪
                    data_shape = clip_grib_with_shapefile(
                        str(grb2_file), 
                        shapefile_path, 
                        str(output_file_path)
                    )
                    
                    print(f"✅ {date_folder.name}/{watershed_name}: 成功裁剪，保存到 {output_filename}")
                    print(f"   数据形状: {data_shape}")
                    folder_success += 1
                    
                except Exception as e:
                    error_msg = f"{watershed_name}: {str(e)}"
                    print(f"❌ {date_folder.name}/{watershed_name}: 裁剪失败 - {error_msg}")
                    folder_failed += 1
            
            total_stats['processed_folders'] += 1
            total_stats['total_success'] += folder_success
            total_stats['total_failed'] += folder_failed
            total_stats['total_skipped'] += folder_skipped
            
            if folder_failed > 0:
                total_stats['failed_folders'].append({
                    'date': date_folder.name,
                    'errors': [f"部分流域处理失败"]
                })
            
            # 每处理10个文件夹强制垃圾回收
            if i % 10 == 0:
                gc.collect()
                print(f"🔄 已处理 {i} 个文件夹，执行内存清理")
                
        except Exception as e:
            print(f"❌ {date_folder.name}: 处理文件夹失败 - {e}")
            total_stats['failed_folders'].append({
                'date': date_folder.name,
                'errors': [f"文件夹处理失败: {str(e)}"]
            })
    
    # 输出总体统计结果
    print("\n" + "=" * 60)
    print("📊 恢复处理统计结果：")
    print(f"剩余文件夹数: {total_stats['total_folders']}")
    print(f"成功处理: {total_stats['processed_folders']}")
    print(f"总成功裁剪: {total_stats['total_success']}")
    print(f"总跳过文件: {total_stats['total_skipped']}")
    print(f"总失败裁剪: {total_stats['total_failed']}")
    
    if total_stats['total_success'] + total_stats['total_failed'] > 0:
        success_rate = total_stats['total_success']/(total_stats['total_success']+total_stats['total_failed'])*100
        print(f"成功率: {success_rate:.1f}%")
    
    if total_stats['failed_folders']:
        print(f"\n❌ 失败的文件夹 ({len(total_stats['failed_folders'])} 个):")
        for failed in total_stats['failed_folders'][:5]:  # 只显示前5个
            print(f"  {failed['date']}: {', '.join(failed['errors'])}")
        if len(total_stats['failed_folders']) > 5:
            print(f"  ... 还有 {len(total_stats['failed_folders'])-5} 个失败文件夹")
    
    return total_stats

if __name__ == "__main__":
    print("🔄 恢复GRB2文件批量裁剪任务...")
    print("从第141个文件夹（20240815）开始继续处理")
    print("=" * 60)
    
    results = process_remaining_folders()
    
    if results:
        print("\n🎉 恢复处理完成！")
        print(f"总共处理了 {results['processed_folders']} 个文件夹")
        print(f"生成了 {results['total_success']} 个CSV文件")
    else:
        print("❌ 恢复处理失败！")
