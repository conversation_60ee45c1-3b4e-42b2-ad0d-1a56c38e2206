#!/usr/bin/env python3
"""
筛选China_Procduct/2024中的最佳文件
筛选规则：保留预测发布时间早于且最接近早上八点整，同时预报开始时间是早上八点整的文件
"""

import os
import shutil
import re
from pathlib import Path
from datetime import datetime, time
import xarray as xr
import numpy as np

def parse_filename(filename):
    """
    解析文件名，提取预测发布时间和预报开始时间
    文件名格式：Z_NWGD_C_BABJ_20240328043341_P_RFFC_SCMOC-ER01_202403280800_07201.GRB2
    返回：(预测发布时间, 预报开始时间) 或 None
    """
    # 使用正则表达式匹配文件名格式
    pattern = r'Z_NWGD_C_BABJ_(\d{14})_P_RFFC_SCMOC-ER01_(\d{12})_\d+\.GRB2$'
    match = re.match(pattern, filename)
    
    if not match:
        return None
    
    try:
        # 提取时间字符串
        forecast_issue_str = match.group(1)  # 预测发布时间 (14位)
        forecast_start_str = match.group(2)  # 预报开始时间 (12位)
        
        # 解析预测发布时间：YYYYMMDDHHMMSS
        forecast_issue = datetime.strptime(forecast_issue_str, '%Y%m%d%H%M%S')
        
        # 解析预报开始时间：YYYYMMDDHHMM
        forecast_start = datetime.strptime(forecast_start_str, '%Y%m%d%H%M')
        
        return forecast_issue, forecast_start
        
    except ValueError as e:
        print(f"解析时间失败: {filename}, 错误: {e}")
        return None

def is_valid_forecast_start_time(forecast_start):
    """
    检查预报开始时间是否为早上8点整
    """
    return forecast_start.hour == 8 and forecast_start.minute == 0

def check_tp_values(file_path):
    """
    检查GRB2文件中的tp列是否包含9999数值

    Parameters:
    -----------
    file_path : str or Path
        GRB2文件路径

    Returns:
    --------
    bool : True表示文件有效（不包含9999），False表示文件无效（包含9999）
    """
    try:
        # 读取GRB2文件
        ds = xr.open_dataset(file_path, engine='cfgrib')

        # 获取tp数据（降水量数据）
        # 可能的变量名：'tp', 'unknown', 'precipitation'等
        tp_var = None
        for var_name in ['tp', 'unknown', 'precipitation']:
            if var_name in ds.variables:
                tp_var = ds[var_name]
                break

        if tp_var is None:
            print(f"⚠️  警告：在文件 {file_path} 中未找到tp变量")
            ds.close()
            return True  # 如果找不到tp变量，假设文件有效

        # 检查是否包含9999值
        # 使用numpy的isclose来处理浮点数比较
        has_9999 = np.any(np.isclose(tp_var.values, 9999.0, rtol=1e-5))

        ds.close()

        if has_9999:
            print(f"⚠️  文件 {file_path.name} 包含9999数值，跳过")
            return False
        else:
            return True

    except Exception as e:
        print(f"❌ 检查文件 {file_path} 时出错: {e}")
        return False  # 如果读取失败，认为文件无效

def find_best_file(files, target_date, date_folder_path):
    """
    从文件列表中找到最佳文件
    规则：
    1. 优先选择预测发布时间早于且最接近早上八点整，同时预报开始时间是早上八点整的文件
    2. 如果没有早于八点的文件，选择最接近早上八点的文件（可以晚于八点）
    3. 检查选中文件的tp列是否包含9999数值，如果包含则选择前一个时间点的文件

    Parameters:
    -----------
    files : list
        文件名列表
    target_date : datetime.date
        目标日期
    date_folder_path : Path
        日期文件夹路径
    """
    before_8am_files = []  # 早于8点的文件
    after_8am_files = []   # 晚于8点的文件
    target_datetime = datetime.combine(target_date, time(8, 0))  # 目标日期的早上8点

    for filename in files:
        # 跳过非GRB2文件和临时文件
        if not filename.endswith('.GRB2') or filename.endswith('~'):
            continue

        parsed = parse_filename(filename)
        if parsed is None:
            continue

        forecast_issue, forecast_start = parsed

        # 检查预报开始时间是否为早上8点整
        if not is_valid_forecast_start_time(forecast_start):
            continue

        # 计算与目标时间的距离
        if forecast_issue < target_datetime:
            # 早于8点的文件
            time_diff = target_datetime - forecast_issue
            before_8am_files.append((filename, forecast_issue, forecast_start, time_diff, 'before'))
        else:
            # 晚于或等于8点的文件
            time_diff = forecast_issue - target_datetime
            after_8am_files.append((filename, forecast_issue, forecast_start, time_diff, 'after'))

    # 优先选择早于8点的文件
    if before_8am_files:
        # 按时间差排序，选择最接近8点的（时间差最小的）
        before_8am_files.sort(key=lambda x: x[3])

        # 逐个检查文件，直到找到不包含9999的文件
        for best_file in before_8am_files:
            file_path = date_folder_path / best_file[0]
            if check_tp_values(file_path):
                return {
                    'filename': best_file[0],
                    'forecast_issue': best_file[1],
                    'forecast_start': best_file[2],
                    'time_diff': best_file[3],
                    'selection_type': 'before_8am'
                }
            else:
                print(f"   跳过文件 {best_file[0]}（包含9999数值）")

        # 如果所有早于8点的文件都包含9999，则继续检查晚于8点的文件
        print(f"   所有早于8点的文件都包含9999数值，检查晚于8点的文件")

    # 如果没有早于8点的文件，或者早于8点的文件都包含9999，选择晚于8点且最接近8点的文件
    if after_8am_files:
        # 按时间差排序，选择最接近8点的（时间差最小的）
        after_8am_files.sort(key=lambda x: x[3])

        # 逐个检查文件，直到找到不包含9999的文件
        for best_file in after_8am_files:
            file_path = date_folder_path / best_file[0]
            if check_tp_values(file_path):
                return {
                    'filename': best_file[0],
                    'forecast_issue': best_file[1],
                    'forecast_start': best_file[2],
                    'time_diff': best_file[3],
                    'selection_type': 'after_8am'
                }
            else:
                print(f"   跳过文件 {best_file[0]}（包含9999数值）")

        # 如果所有文件都包含9999
        print(f"   所有文件都包含9999数值，无法找到有效文件")

    # 如果没有找到任何符合条件的文件
    return None

def process_directory(source_dir, target_dir):
    """
    处理整个目录，筛选每个日期文件夹中的最佳文件
    """
    source_path = Path(source_dir)
    target_path = Path(target_dir)
    
    if not source_path.exists():
        print(f"错误：源目录 {source_dir} 不存在！")
        return
    
    # 创建目标目录
    target_path.mkdir(parents=True, exist_ok=True)
    
    # 统计信息
    total_folders = 0
    processed_folders = 0
    failed_folders = []
    before_8am_count = 0
    after_8am_count = 0
    
    # 遍历所有日期文件夹
    for date_folder in sorted(source_path.iterdir()):
        if not date_folder.is_dir():
            continue
            
        # 检查文件夹名是否为8位数字（日期格式）
        if not (date_folder.name.isdigit() and len(date_folder.name) == 8):
            continue
            
        total_folders += 1
        date_str = date_folder.name
        
        try:
            # 解析日期
            target_date = datetime.strptime(date_str, '%Y%m%d').date()
            
            # 获取文件夹中的所有文件
            files = [f.name for f in date_folder.iterdir() if f.is_file()]
            
            if not files:
                print(f"⚠️  {date_str}: 文件夹为空")
                failed_folders.append((date_str, "文件夹为空"))
                continue
            
            # 找到最佳文件
            best_file_info = find_best_file(files, target_date, date_folder)
            
            if best_file_info is None:
                print(f"❌ {date_str}: 未找到符合条件的文件")
                failed_folders.append((date_str, "未找到符合条件的文件"))
                continue
            
            # 创建目标日期文件夹
            target_date_folder = target_path / date_str
            target_date_folder.mkdir(exist_ok=True)
            
            # 复制最佳文件
            source_file = date_folder / best_file_info['filename']
            target_file = target_date_folder / best_file_info['filename']
            
            shutil.copy2(source_file, target_file)
            
            # 输出处理结果
            forecast_issue = best_file_info['forecast_issue']
            forecast_start = best_file_info['forecast_start']
            time_diff = best_file_info['time_diff']
            selection_type = best_file_info.get('selection_type', 'before_8am')

            # 根据选择类型显示不同的图标和说明
            if selection_type == 'before_8am':
                icon = "✅"
                type_desc = "早于8点"
            else:
                icon = "🔶"
                type_desc = "晚于8点"

            print(f"{icon} {date_str}: {best_file_info['filename']} ({type_desc})")
            print(f"   预测发布: {forecast_issue.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   预报开始: {forecast_start.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   时间差: {time_diff}")

            # 更新统计计数器
            if selection_type == 'before_8am':
                before_8am_count += 1
            else:
                after_8am_count += 1

            processed_folders += 1
            
        except Exception as e:
            print(f"❌ {date_str}: 处理失败 - {e}")
            failed_folders.append((date_str, str(e)))
    
    # 输出统计结果
    print("\n" + "=" * 60)
    print("📊 处理统计：")
    print(f"总文件夹数: {total_folders}")
    print(f"成功处理: {processed_folders}")
    print(f"处理失败: {len(failed_folders)}")
    print(f"成功率: {processed_folders/total_folders*100:.1f}%" if total_folders > 0 else "成功率: 0%")
    print()
    print("📈 选择类型统计：")
    print(f"✅ 早于8点的文件: {before_8am_count} 个 ({before_8am_count/processed_folders*100:.1f}%)" if processed_folders > 0 else "✅ 早于8点的文件: 0 个")
    print(f"🔶 晚于8点的文件: {after_8am_count} 个 ({after_8am_count/processed_folders*100:.1f}%)" if processed_folders > 0 else "🔶 晚于8点的文件: 0 个")
    
    if failed_folders:
        print("\n❌ 失败的文件夹：")
        for date_str, reason in failed_folders:
            print(f"  {date_str}: {reason}")

if __name__ == "__main__":
    # 设置源目录和目标目录
    source_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2025"
    target_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    
    print("🚀 开始筛选最佳文件...")
    print(f"源目录: {source_directory}")
    print(f"目标目录: {target_directory}")
    print("=" * 60)
    
    process_directory(source_directory, target_directory)
    
    print("\n🎉 筛选完成！")
