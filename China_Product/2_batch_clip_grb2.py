#!/usr/bin/env python3
"""
批量处理China_Product/2024_final中的GRB2文件
使用shapefile裁剪每个文件，为三个流域（liangh<PERSON>、yanta<PERSON>、yin<PERSON>）生成裁剪后的数据
"""

import os
import xarray as xr
import geopandas as gpd
import rioxarray as rxr
from shapely.geometry import mapping
import pandas as pd
import numpy as np
from pathlib import Path
import traceback
import gc  # 垃圾回收

def clip_grib_with_shapefile(grib_file, shapefile_path, output_csv=None):
    """
    使用shapefile裁剪GRIB2文件

    Parameters:
    -----------
    grib_file : str
        GRIB2文件路径
    shapefile_path : str
        shapefile文件路径
    output_csv : str, optional
        输出CSV文件路径

    Returns:
    --------
    clipped_ds : xarray.Dataset
        裁剪后的数据集
    """

    ds = None
    gdf = None
    clipped_ds = None
    df = None

    try:
        # 1. 读取GRIB2文件
        ds = xr.open_dataset(grib_file, engine='cfgrib')

        # 2. 设置空间参考系统 (CRS)
        ds = ds.rio.write_crs("EPSG:4326")

        # 3. 读取 shapefile
        gdf = gpd.read_file(shapefile_path)

        # 如果 shapefile 没有 crs，就手动设置为 EPSG:4326
        if gdf.crs is None:
            gdf = gdf.set_crs("EPSG:4326")

        # 如果原始是其他坐标系，再转换
        elif gdf.crs.to_string() != "EPSG:4326":
            gdf = gdf.to_crs("EPSG:4326")

        # 4. 进行裁剪
        clipped_ds = ds.rio.clip(gdf.geometry.apply(mapping), gdf.crs, drop=False)

        # 5. 转换为DataFrame并保存CSV（可选）
        if output_csv:
            df = clipped_ds.to_dataframe().reset_index()
            df = df.dropna()  # 移除NaN值

            # 删除不需要的列并重命名
            if 'surface' in df.columns:
                df = df.drop('surface', axis=1)
            if 'spatial_ref' in df.columns:
                df = df.drop('spatial_ref', axis=1)
            df = df.rename(columns={'unknown': 'tp'})

            # 重新排列列的顺序
            df = df[['time', 'step', 'valid_time', 'latitude', 'longitude', 'tp']]

            df.to_csv(output_csv, index=False)

            result = (clipped_ds, df)
        else:
            result = clipped_ds

    finally:
        # 强制释放内存
        if ds is not None:
            ds.close()
            del ds
        if gdf is not None:
            del gdf
        if clipped_ds is not None and output_csv:
            del clipped_ds
        if df is not None:
            del df
        gc.collect()  # 强制垃圾回收

    return result

def check_files_exist(date_folder_path, watersheds, grb2_file):
    """
    检查该日期文件夹中是否已经存在所有流域的CSV文件

    Parameters:
    -----------
    date_folder_path : Path
        日期文件夹路径
    watersheds : dict
        流域名称和shapefile路径的字典
    grb2_file : Path
        GRB2文件路径

    Returns:
    --------
    bool : 是否所有文件都已存在
    """
    for watershed_name in watersheds.keys():
        output_filename = f"{watershed_name}_{grb2_file.stem}.csv"
        output_file_path = date_folder_path / output_filename
        if not output_file_path.exists():
            return False
    return True

def process_single_date_folder(date_folder_path, watersheds):
    """
    处理单个日期文件夹中的GRB2文件

    Parameters:
    -----------
    date_folder_path : Path
        日期文件夹路径
    watersheds : dict
        流域名称和shapefile路径的字典

    Returns:
    --------
    dict : 处理结果统计
    """
    results = {
        'date': date_folder_path.name,
        'success': 0,
        'failed': 0,
        'errors': [],
        'skipped': 0
    }

    # 查找GRB2文件
    grb2_files = list(date_folder_path.glob("*.GRB2"))

    if not grb2_files:
        results['errors'].append("未找到GRB2文件")
        results['failed'] = 1
        return results

    if len(grb2_files) > 1:
        results['errors'].append(f"找到多个GRB2文件: {[f.name for f in grb2_files]}")
        results['failed'] = 1
        return results

    grb2_file = grb2_files[0]

    # 检查是否已经处理过
    if check_files_exist(date_folder_path, watersheds, grb2_file):
        print(f"⏭️  {results['date']}: 所有文件已存在，跳过处理")
        results['success'] = len(watersheds)
        results['skipped'] = len(watersheds)
        return results
    
    # 为每个流域进行裁剪
    for watershed_name, shapefile_path in watersheds.items():
        try:
            # 创建输出文件路径
            output_filename = f"{watershed_name}_{grb2_file.stem}.csv"
            output_file_path = date_folder_path / output_filename
            
            # 检查单个文件是否已存在
            if output_file_path.exists():
                print(f"⏭️  {results['date']}/{watershed_name}: 文件已存在，跳过")
                results['success'] += 1
                results['skipped'] += 1
                continue

            # 执行裁剪
            _, clipped_df = clip_grib_with_shapefile(
                str(grb2_file),
                shapefile_path,
                str(output_file_path)
            )

            print(f"✅ {results['date']}/{watershed_name}: 成功裁剪，保存到 {output_filename}")
            print(f"   数据形状: {clipped_df.shape}")
            results['success'] += 1
            
        except Exception as e:
            error_msg = f"{watershed_name}: {str(e)}"
            results['errors'].append(error_msg)
            results['failed'] += 1
            print(f"❌ {results['date']}/{watershed_name}: 裁剪失败 - {error_msg}")
            # 打印详细错误信息用于调试
            print(f"   详细错误: {traceback.format_exc()}")
    
    return results

def batch_process_grb2_files(source_dir, watersheds, max_folders=None):
    """
    批量处理所有日期文件夹中的GRB2文件
    
    Parameters:
    -----------
    source_dir : str
        源目录路径 (China_Product/2024_final)
    watersheds : dict
        流域名称和shapefile路径的字典
    max_folders : int, optional
        最大处理文件夹数量（用于测试）
    
    Returns:
    --------
    dict : 总体处理结果统计
    """
    source_path = Path(source_dir)
    
    if not source_path.exists():
        print(f"错误：源目录 {source_dir} 不存在！")
        return None
    
    # 获取所有日期文件夹
    date_folders = [f for f in source_path.iterdir() if f.is_dir() and f.name.isdigit()]
    date_folders.sort()
    
    if max_folders:
        date_folders = date_folders[:max_folders]
    
    print(f"🚀 开始批量处理 {len(date_folders)} 个日期文件夹...")
    print(f"源目录: {source_dir}")
    print(f"流域: {list(watersheds.keys())}")
    print("=" * 60)
    
    # 统计信息
    total_stats = {
        'total_folders': len(date_folders),
        'processed_folders': 0,
        'total_success': 0,
        'total_failed': 0,
        'failed_folders': [],
        'watershed_stats': {name: {'success': 0, 'failed': 0} for name in watersheds.keys()}
    }
    
    # 处理每个日期文件夹
    for i, date_folder in enumerate(date_folders, 1):
        print(f"📁 处理 {i}/{len(date_folders)}: {date_folder.name}")
        
        try:
            results = process_single_date_folder(date_folder, watersheds)
            
            total_stats['processed_folders'] += 1
            total_stats['total_success'] += results['success']
            total_stats['total_failed'] += results['failed']
            
            # 更新流域统计
            for watershed_name in watersheds.keys():
                watershed_success = sum(1 for error in results['errors'] if not error.startswith(watershed_name))
                watershed_failed = sum(1 for error in results['errors'] if error.startswith(watershed_name))
                
                if results['success'] > 0:
                    total_stats['watershed_stats'][watershed_name]['success'] += 1
                if watershed_failed > 0:
                    total_stats['watershed_stats'][watershed_name]['failed'] += 1
            
            if results['failed'] > 0:
                total_stats['failed_folders'].append({
                    'date': results['date'],
                    'errors': results['errors']
                })
                
        except Exception as e:
            print(f"❌ {date_folder.name}: 处理文件夹失败 - {e}")
            total_stats['failed_folders'].append({
                'date': date_folder.name,
                'errors': [f"文件夹处理失败: {str(e)}"]
            })
    
    # 输出总体统计结果
    print("\n" + "=" * 60)
    print("📊 批量处理统计结果：")
    print(f"总文件夹数: {total_stats['total_folders']}")
    print(f"成功处理: {total_stats['processed_folders']}")
    print(f"总成功裁剪: {total_stats['total_success']}")
    print(f"总失败裁剪: {total_stats['total_failed']}")
    print(f"成功率: {total_stats['total_success']/(total_stats['total_success']+total_stats['total_failed'])*100:.1f}%" if (total_stats['total_success']+total_stats['total_failed']) > 0 else "成功率: 0%")
    
    print("\n📈 各流域统计：")
    for watershed_name, stats in total_stats['watershed_stats'].items():
        total_watershed = stats['success'] + stats['failed']
        success_rate = stats['success']/total_watershed*100 if total_watershed > 0 else 0
        print(f"  {watershed_name}: 成功 {stats['success']}, 失败 {stats['failed']}, 成功率 {success_rate:.1f}%")
    
    if total_stats['failed_folders']:
        print(f"\n❌ 失败的文件夹 ({len(total_stats['failed_folders'])} 个):")
        for failed in total_stats['failed_folders'][:10]:  # 只显示前10个
            print(f"  {failed['date']}: {', '.join(failed['errors'])}")
        if len(total_stats['failed_folders']) > 10:
            print(f"  ... 还有 {len(total_stats['failed_folders'])-10} 个失败文件夹")
    
    return total_stats

if __name__ == "__main__":
    # 定义流域和对应的shapefile路径
    watersheds = {
        # 'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        # 'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        # 'yinhe': '/home/<USER>/Flood_flow_prediction/boundary/yinhe.shp',
        # 'chengkou': '/home/<USER>/Flood_flow_prediction/boundary/chengkou.shp',
        # 'guojia': '/home/<USER>/Flood_flow_prediction/boundary/guojia.shp',
        'mituo': '/home/<USER>/Flood_flow_prediction/boundary/mituo.shp',
        'dongxi': '/home/<USER>/Flood_flow_prediction/boundary/dongxi.shp'
    }
    
    # 源目录
    source_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2024_final"
    
    # 执行批量处理
    # 先测试处理前5个文件夹
    print("🧪 测试模式：处理前5个文件夹...")
    test_results = batch_process_grb2_files(source_directory, watersheds, max_folders=5)
    
    if test_results and test_results['total_success'] > 0:
        print("\n✅ 测试成功！是否继续处理所有文件夹？")
        user_input = input("输入 'y' 继续处理所有文件夹，或按任意键退出: ")
        
        if user_input.lower() == 'y':
            print("\n🚀 开始处理所有文件夹...")
            final_results = batch_process_grb2_files(source_directory, watersheds)
            print("\n🎉 批量处理完成！")
        else:
            print("👋 用户取消，退出程序。")
    else:
        print("❌ 测试失败，请检查配置和依赖。")
