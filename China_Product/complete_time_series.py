#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
补全lianghe_2025_hourly.csv中缺失的时间点
缺失时间范围：2025-02-19 09:00:00 到 2025-04-17 08:00:00
"""

import csv
from datetime import datetime, timedelta

def parse_datetime(date_str):
    """解析日期时间字符串"""
    return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')

def format_datetime(dt):
    """格式化日期时间为字符串"""
    return dt.strftime('%Y-%m-%d %H:%M:%S')

def complete_time_series():
    """补全时间序列中的缺失时间点"""

    # 读取原始文件
    print("正在读取原始文件...")
    data = []
    with open('dongxi_2025_hourly.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data.append({
                'valid_time': parse_datetime(row['valid_time']),
                'tp': float(row['tp'])
            })

    # 按时间排序
    data.sort(key=lambda x: x['valid_time'])

    print(f"原始数据行数: {len(data)}")
    print(f"时间范围: {data[0]['valid_time']} 到 {data[-1]['valid_time']}")

    # 检查缺失的时间段
    print("\n检查时间间隔...")
    gaps = []
    for i in range(1, len(data)):
        time_diff = data[i]['valid_time'] - data[i-1]['valid_time']
        if time_diff > timedelta(hours=1):
            gaps.append({
                'start': data[i-1]['valid_time'],
                'end': data[i]['valid_time'],
                'gap': time_diff
            })

    if gaps:
        print("发现时间间隔大于1小时的缺失:")
        for gap in gaps:
            print(f"  从 {gap['start']} 到 {gap['end']}, 间隔: {gap['gap']}")

    # 创建完整的时间序列
    print("\n创建完整的时间序列...")
    start_time = data[0]['valid_time']
    end_time = data[-1]['valid_time']

    # 生成完整的小时时间序列
    complete_data = {}
    current_time = start_time
    while current_time <= end_time:
        complete_data[current_time] = 0.0  # 默认tp值为0
        current_time += timedelta(hours=1)

    print(f"完整时间序列长度: {len(complete_data)}")

    # 填入原始数据的tp值
    print("合并数据...")
    for row in data:
        complete_data[row['valid_time']] = row['tp']

    # 转换为列表并排序
    complete_list = []
    for dt in sorted(complete_data.keys()):
        complete_list.append({
            'valid_time': dt,
            'tp': complete_data[dt]
        })

    print(f"补全后数据行数: {len(complete_list)}")
    print(f"新增数据点: {len(complete_list) - len(data)}")

    # 保存到新文件
    output_file = 'dongxi_2025_hourly_complete.csv'
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['valid_time', 'tp'])
        for row in complete_list:
            writer.writerow([format_datetime(row['valid_time']), row['tp']])

    print(f"\n已保存到: {output_file}")

    # 验证结果
    print("\n验证结果:")
    print(f"时间范围: {complete_list[0]['valid_time']} 到 {complete_list[-1]['valid_time']}")
    print(f"总数据点: {len(complete_list)}")

    non_zero_count = sum(1 for row in complete_list if row['tp'] != 0)
    zero_count = len(complete_list) - non_zero_count
    print(f"非零tp值数量: {non_zero_count}")
    print(f"零tp值数量: {zero_count}")

    # 检查是否还有时间间隔
    new_gaps = []
    for i in range(1, len(complete_list)):
        time_diff = complete_list[i]['valid_time'] - complete_list[i-1]['valid_time']
        if time_diff > timedelta(hours=1):
            new_gaps.append({
                'start': complete_list[i-1]['valid_time'],
                'end': complete_list[i]['valid_time'],
                'gap': time_diff
            })

    if len(new_gaps) == 0:
        print("✓ 时间序列已完整，无缺失时间点")
    else:
        print("⚠ 仍存在时间间隔:")
        for gap in new_gaps:
            print(f"  从 {gap['start']} 到 {gap['end']}, 间隔: {gap['gap']}")

    return complete_list

if __name__ == "__main__":
    complete_time_series()
