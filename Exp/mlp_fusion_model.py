"""
基于MLP (多层感知机) 的多源降水产品融合模型
使用深度神经网络方法融合三个降水产品，以实测雨量作为标签
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError as e:
    print(f"PyTorch import error: {e}")
    print("Falling back to sklearn MLPRegressor...")
    from sklearn.neural_network import MLPRegressor
    TORCH_AVAILABLE = False
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def add_time_features(data):
    """添加时间特征"""
    data['time'] = pd.to_datetime(data['time'])
    data['hour'] = data['time'].dt.hour
    data['month'] = data['time'].dt.month
    data['season'] = data['month'].apply(lambda x: (x-1)//3 + 1)  # 1:春, 2:夏, 3:秋, 4:冬

    # 周期性编码
    data['hour_sin'] = np.sin(2 * np.pi * data['hour'] / 24)
    data['hour_cos'] = np.cos(2 * np.pi * data['hour'] / 24)
    data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
    data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)

    return data

if TORCH_AVAILABLE:
    class MLPModel(nn.Module):
        """MLP模型类"""
        def __init__(self, input_dim):
            super(MLPModel, self).__init__()
            self.layers = nn.Sequential(
                nn.Linear(input_dim, 32),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(32, 16),
                nn.ReLU(),
                nn.Linear(16, 1)
            )

        def forward(self, x):
            return self.layers(x)

    def create_mlp_model(input_dim):
        """创建MLP模型"""
        model = MLPModel(input_dim)
        return model

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    
    # Nash-Sutcliffe Efficiency
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    
    # 相关系数
    corr = np.corrcoef(y_true, y_pred)[0, 1]
    
    return rmse, nse, corr

if TORCH_AVAILABLE:
    class EarlyStopping:
        """自定义早停类"""
        def __init__(self, patience=10, min_delta=0.001):
            self.patience = patience
            self.min_delta = min_delta
            self.best_loss = np.inf
            self.wait = 0
            self.stop_training = False

        def __call__(self, val_loss):
            if val_loss < self.best_loss - self.min_delta:
                self.best_loss = val_loss
                self.wait = 0
            else:
                self.wait += 1
                if self.wait >= self.patience:
                    self.stop_training = True
            return self.stop_training

    def train_model(model, train_loader, val_loader, num_epochs=100, learning_rate=0.001, device='cpu'):
        """训练模型"""
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10, min_lr=1e-6)
        early_stopping = EarlyStopping(patience=15, min_delta=0.001)

        train_losses = []
        val_losses = []

        model.to(device)

        for epoch in range(num_epochs):
            # 训练阶段
            model.train()
            train_loss = 0.0
            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)

                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

            train_loss /= len(train_loader)

            # 验证阶段
            model.eval()
            val_loss = 0.0
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                    outputs = model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    val_loss += loss.item()

            val_loss /= len(val_loader)

            train_losses.append(train_loss)
            val_losses.append(val_loss)

            # 学习率调度
            scheduler.step(val_loss)

            # 早停检查
            if early_stopping(val_loss):
                print(f"\nEarly stopping at epoch {epoch + 1}")
                break

            if (epoch + 1) % 10 == 0:
                print(f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

        return train_losses, val_losses

def main():
    global TORCH_AVAILABLE

    # 设置随机种子
    np.random.seed(42)

    if TORCH_AVAILABLE:
        try:
            torch.manual_seed(42)
            # 设置设备，强制使用CPU避免CUDA问题
            device = torch.device('cpu')  # 强制使用CPU
            print(f"Using PyTorch with device: {device}")
        except Exception as e:
            print(f"PyTorch device setup error: {e}")
            print("Falling back to sklearn...")
            TORCH_AVAILABLE = False

    if not TORCH_AVAILABLE:
        print("Using sklearn MLPRegressor")

    # 读取数据
    print("Loading data...")
    data = pd.read_csv('Exp/data/lianghe_final(时间换算后).csv')

    # 添加时间特征
    print("Adding time features...")
    data = add_time_features(data)

    # 准备特征和标签
    feature_columns = ['001', '002', '003', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'season']
    X = data[feature_columns].values
    y = data['tp'].values

    print(f"数据形状: X={X.shape}, y={y.shape}")
    print(f"特征列: {feature_columns}")
    print(f"数据统计:")
    print(f"Product1: mean={np.mean(data['001']):.4f}, std={np.std(data['001']):.4f}")
    print(f"Product2: mean={np.mean(data['002']):.4f}, std={np.std(data['002']):.4f}")
    print(f"Product3: mean={np.mean(data['003']):.4f}, std={np.std(data['003']):.4f}")
    print(f"Actual rainfall: mean={np.mean(y):.4f}, std={np.std(y):.4f}")

    # 划分训练集和测试集 (80/20)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 进一步划分验证集
    X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)

    print(f"\n训练集大小: {len(X_train)}")
    print(f"验证集大小: {len(X_val)}")
    print(f"测试集大小: {len(X_test)}")

    # 特征标准化
    print("\nStandardizing features...")
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()

    X_train_scaled = scaler_X.fit_transform(X_train)
    X_val_scaled = scaler_X.transform(X_val)
    X_test_scaled = scaler_X.transform(X_test)

    y_train_scaled = scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
    y_val_scaled = scaler_y.transform(y_val.reshape(-1, 1)).flatten()

    if TORCH_AVAILABLE:
        # 转换为PyTorch张量
        X_train_tensor = torch.FloatTensor(X_train_scaled)
        y_train_tensor = torch.FloatTensor(y_train_scaled)
        X_val_tensor = torch.FloatTensor(X_val_scaled)
        y_val_tensor = torch.FloatTensor(y_val_scaled)
        X_test_tensor = torch.FloatTensor(X_test_scaled)

        # 创建数据加载器
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        val_dataset = TensorDataset(X_val_tensor, y_val_tensor)

        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    else:
        # sklearn不需要数据加载器
        train_loader = val_loader = None

    # 创建和训练MLP模型
    print("\nCreating MLP model...")

    if TORCH_AVAILABLE:
        # PyTorch版本
        model = create_mlp_model(X_train_scaled.shape[1])

        print(f"Model architecture:")
        print(model)

        print("\nTraining MLP model...")
        train_losses, val_losses = train_model(model, train_loader, val_loader, num_epochs=100, device=device)
    else:
        # sklearn版本
        model = MLPRegressor(
            hidden_layer_sizes=(32, 16),
            activation='relu',
            solver='adam',
            alpha=0.001,
            learning_rate_init=0.001,
            max_iter=1000,
            early_stopping=True,
            validation_fraction=0.2,
            n_iter_no_change=15,
            random_state=42
        )

        print(f"Model: sklearn MLPRegressor with layers (32, 16)")
        print("\nTraining MLP model...")
        model.fit(X_train_scaled, y_train_scaled)
        train_losses, val_losses = [], []  # sklearn doesn't provide training history
    
    # 预测
    print("\nMaking predictions...")

    if TORCH_AVAILABLE:
        # PyTorch版本
        model.eval()
        with torch.no_grad():
            X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
            X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
            X_test_tensor = torch.FloatTensor(X_test_scaled).to(device)

            y_train_pred_scaled = model(X_train_tensor).cpu().numpy().flatten()
            y_val_pred_scaled = model(X_val_tensor).cpu().numpy().flatten()
            y_test_pred_scaled = model(X_test_tensor).cpu().numpy().flatten()
    else:
        # sklearn版本
        y_train_pred_scaled = model.predict(X_train_scaled)
        y_val_pred_scaled = model.predict(X_val_scaled)
        y_test_pred_scaled = model.predict(X_test_scaled)

    # 反标准化
    y_train_pred = scaler_y.inverse_transform(y_train_pred_scaled.reshape(-1, 1)).flatten()
    y_val_pred = scaler_y.inverse_transform(y_val_pred_scaled.reshape(-1, 1)).flatten()
    y_test_pred = scaler_y.inverse_transform(y_test_pred_scaled.reshape(-1, 1)).flatten()
    
    # 计算评估指标
    train_rmse, train_nse, train_corr = calculate_metrics(y_train, y_train_pred)
    val_rmse, val_nse, val_corr = calculate_metrics(y_val, y_val_pred)
    test_rmse, test_nse, test_corr = calculate_metrics(y_test, y_test_pred)
    
    print(f"\n=== MLP模型评估结果 ===")
    print(f"训练集 - RMSE: {train_rmse:.4f}, NSE: {train_nse:.4f}, Correlation: {train_corr:.4f}")
    print(f"验证集 - RMSE: {val_rmse:.4f}, NSE: {val_nse:.4f}, Correlation: {val_corr:.4f}")
    print(f"测试集 - RMSE: {test_rmse:.4f}, NSE: {test_nse:.4f}, Correlation: {test_corr:.4f}")
    
    # 可视化结果
    plt.figure(figsize=(20, 12))
    
    # 训练历史
    plt.subplot(3, 4, 1)
    if TORCH_AVAILABLE and train_losses:
        plt.plot(train_losses, label='Training Loss', linewidth=1)
        plt.plot(val_losses, label='Validation Loss', linewidth=1)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training History')
        plt.legend()
    else:
        plt.text(0.5, 0.5, 'Training History\nNot Available\n(sklearn version)',
                ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Training History')
    plt.grid(True, alpha=0.3)
    
    # 训练集结果
    plt.subplot(3, 4, 2)
    plt.scatter(y_train, y_train_pred, alpha=0.5, s=1)
    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Training Set\nRMSE={train_rmse:.4f}, NSE={train_nse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 验证集结果
    plt.subplot(3, 4, 3)
    plt.scatter(y_val, y_val_pred, alpha=0.5, s=1)
    plt.plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Validation Set\nRMSE={val_rmse:.4f}, NSE={val_nse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 测试集结果
    plt.subplot(3, 4, 4)
    plt.scatter(y_test, y_test_pred, alpha=0.5, s=1)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Test Set\nRMSE={test_rmse:.4f}, NSE={test_nse:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 时间序列对比 (显示测试集的一部分)
    plt.subplot(3, 4, 5)
    n_show = min(500, len(y_test))
    plt.plot(y_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(y_test_pred[:n_show], 'r-', linewidth=1, label='MLP Fusion', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Time Series Comparison (Test Set)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 残差分析
    plt.subplot(3, 4, 6)
    residuals = y_test - y_test_pred
    plt.scatter(y_test_pred, residuals, alpha=0.5, s=1)
    plt.axhline(y=0, color='r', linestyle='--', lw=1)
    plt.xlabel('Predicted Rainfall (mm)')
    plt.ylabel('Residuals (mm)')
    plt.title('Residual Plot')
    plt.grid(True, alpha=0.3)
    
    # 残差直方图
    plt.subplot(3, 4, 7)
    plt.hist(residuals, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('Residuals (mm)')
    plt.ylabel('Frequency')
    plt.title('Residual Distribution')
    plt.grid(True, alpha=0.3)
    
    # 产品对比
    plt.subplot(3, 4, 8)
    n_show = min(200, len(y_test))
    plt.plot(y_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(X_test[:n_show, 0], '--', linewidth=1, label='Product 1', alpha=0.7, color='orange')
    plt.plot(X_test[:n_show, 1], '--', linewidth=1, label='Product 2', alpha=0.7, color='purple')
    plt.plot(X_test[:n_show, 2], '--', linewidth=1, label='Product 3', alpha=0.7, color='blue')
    plt.plot(y_test_pred[:n_show], 'r-', linewidth=2, label='MLP Fusion', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Products vs Fusion Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 模型性能对比
    plt.subplot(3, 4, 9)
    metrics = ['RMSE', 'NSE', 'Correlation']
    train_values = [train_rmse, train_nse, train_corr]
    val_values = [val_rmse, val_nse, val_corr]
    test_values = [test_rmse, test_nse, test_corr]
    
    x = np.arange(len(metrics))
    width = 0.25
    
    plt.bar(x - width, train_values, width, label='Training', alpha=0.8)
    plt.bar(x, val_values, width, label='Validation', alpha=0.8)
    plt.bar(x + width, test_values, width, label='Testing', alpha=0.8)
    plt.xlabel('Metrics')
    plt.ylabel('Values')
    plt.title('Model Performance')
    plt.xticks(x, metrics)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Exp/mlp_fusion_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存模型
    if TORCH_AVAILABLE:
        torch.save(model.state_dict(), 'Exp/mlp_fusion_model.pth')
        torch.save(scaler_X, 'Exp/mlp_scaler_X.pth')
        torch.save(scaler_y, 'Exp/mlp_scaler_y.pth')
    else:
        import joblib
        joblib.dump(model, 'Exp/mlp_fusion_model.pkl')
        joblib.dump(scaler_X, 'Exp/mlp_scaler_X.pkl')
        joblib.dump(scaler_y, 'Exp/mlp_scaler_y.pkl')
    
    # 保存结果
    results_df = pd.DataFrame({
        'Actual': y_test,
        'MLP_Prediction': y_test_pred,
        'Product1': X_test[:, 0],
        'Product2': X_test[:, 1],
        'Product3': X_test[:, 2]
    })
    results_df.to_csv('Exp/mlp_fusion_predictions.csv', index=False)
    
    print(f"\n结果已保存到:")
    if TORCH_AVAILABLE:
        print(f"- 模型: Exp/mlp_fusion_model.pth")
        print(f"- 标准化器: Exp/mlp_scaler_X.pth, Exp/mlp_scaler_y.pth")
    else:
        print(f"- 模型: Exp/mlp_fusion_model.pkl")
        print(f"- 标准化器: Exp/mlp_scaler_X.pkl, Exp/mlp_scaler_y.pkl")
    print(f"- 图表: Exp/mlp_fusion_results.png")
    print(f"- 预测结果: Exp/mlp_fusion_predictions.csv")

if __name__ == "__main__":
    main()
