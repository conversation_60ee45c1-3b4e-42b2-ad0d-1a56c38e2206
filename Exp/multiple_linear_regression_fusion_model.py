"""
基于多元线性回归的多源降水产品融合模型
使用多元线性回归方法融合三个降水产品，以实测雨量作为标签
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def add_time_features(data):
    """添加时间特征"""
    data['time'] = pd.to_datetime(data['time'])
    data['hour'] = data['time'].dt.hour
    data['month'] = data['time'].dt.month
    data['season'] = data['month'].apply(lambda x: (x-1)//3 + 1)  # 1:春, 2:夏, 3:秋, 4:冬
    data['day_of_year'] = data['time'].dt.dayofyear
    
    # 周期性编码
    data['hour_sin'] = np.sin(2 * np.pi * data['hour'] / 24)
    data['hour_cos'] = np.cos(2 * np.pi * data['hour'] / 24)
    data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
    data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)
    
    return data

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    
    # Nash-Sutcliffe Efficiency
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    
    # 相关系数
    corr = np.corrcoef(y_true, y_pred)[0, 1]
    
    # R²决定系数
    r2 = r2_score(y_true, y_pred)
    
    # 平均绝对误差
    mae = np.mean(np.abs(y_true - y_pred))
    
    return rmse, nse, corr, r2, mae

def analyze_residuals(y_true, y_pred):
    """残差分析"""
    residuals = y_true - y_pred
    
    # Shapiro-Wilk正态性检验
    shapiro_stat, shapiro_p = stats.shapiro(residuals[:5000] if len(residuals) > 5000 else residuals)
    
    # Durbin-Watson检验（自相关性）
    def durbin_watson(residuals):
        diff = np.diff(residuals)
        return np.sum(diff**2) / np.sum(residuals**2)
    
    dw_stat = durbin_watson(residuals)
    
    return {
        'mean': np.mean(residuals),
        'std': np.std(residuals),
        'shapiro_stat': shapiro_stat,
        'shapiro_p': shapiro_p,
        'durbin_watson': dw_stat
    }

def main():
    # 读取数据
    print("Loading data...")
    data = pd.read_csv('Exp/data/lianghe_final(时间换算后).csv')
    
    # 添加时间特征
    print("Adding time features...")
    data = add_time_features(data)
    
    # 基础模型：只使用三个产品
    basic_features = ['001', '002', '003']
    
    # 扩展模型：添加时间特征
    extended_features = ['001', '002', '003', 'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'season']
    
    # 准备数据
    X_basic = data[basic_features].values
    X_extended = data[extended_features].values
    y = data['tp'].values
    
    print(f"数据形状: X_basic={X_basic.shape}, X_extended={X_extended.shape}, y={y.shape}")
    print(f"数据统计:")
    print(f"Product1: mean={np.mean(data['001']):.4f}, std={np.std(data['001']):.4f}")
    print(f"Product2: mean={np.mean(data['002']):.4f}, std={np.std(data['002']):.4f}")
    print(f"Product3: mean={np.mean(data['003']):.4f}, std={np.std(data['003']):.4f}")
    print(f"Actual rainfall: mean={np.mean(y):.4f}, std={np.std(y):.4f}")
    
    # 划分训练集和测试集 (80/20)
    X_basic_train, X_basic_test, y_train, y_test = train_test_split(X_basic, y, test_size=0.2, random_state=42)
    X_ext_train, X_ext_test, _, _ = train_test_split(X_extended, y, test_size=0.2, random_state=42)
    
    print(f"\n训练集大小: {len(X_basic_train)}")
    print(f"测试集大小: {len(X_basic_test)}")
    
    # 特征标准化（可选）
    scaler_basic = StandardScaler()
    scaler_extended = StandardScaler()
    
    X_basic_train_scaled = scaler_basic.fit_transform(X_basic_train)
    X_basic_test_scaled = scaler_basic.transform(X_basic_test)
    
    X_ext_train_scaled = scaler_extended.fit_transform(X_ext_train)
    X_ext_test_scaled = scaler_extended.transform(X_ext_test)
    
    # 训练基础多元线性回归模型
    print("\n=== 基础多元线性回归模型 (仅产品特征) ===")
    basic_model = LinearRegression()
    basic_model.fit(X_basic_train, y_train)
    
    # 训练标准化版本
    basic_model_scaled = LinearRegression()
    basic_model_scaled.fit(X_basic_train_scaled, y_train)
    
    # 训练扩展模型
    print("\n=== 扩展多元线性回归模型 (产品+时间特征) ===")
    extended_model = LinearRegression()
    extended_model.fit(X_ext_train_scaled, y_train)
    
    # 预测
    y_basic_pred = basic_model.predict(X_basic_test)
    y_basic_scaled_pred = basic_model_scaled.predict(X_basic_test_scaled)
    y_extended_pred = extended_model.predict(X_ext_test_scaled)
    
    # 计算评估指标
    basic_metrics = calculate_metrics(y_test, y_basic_pred)
    basic_scaled_metrics = calculate_metrics(y_test, y_basic_scaled_pred)
    extended_metrics = calculate_metrics(y_test, y_extended_pred)
    
    print(f"\n=== 模型评估结果 ===")
    print(f"基础模型 - RMSE: {basic_metrics[0]:.4f}, NSE: {basic_metrics[1]:.4f}, Correlation: {basic_metrics[2]:.4f}, R²: {basic_metrics[3]:.4f}")
    print(f"基础模型(标准化) - RMSE: {basic_scaled_metrics[0]:.4f}, NSE: {basic_scaled_metrics[1]:.4f}, Correlation: {basic_scaled_metrics[2]:.4f}, R²: {basic_scaled_metrics[3]:.4f}")
    print(f"扩展模型 - RMSE: {extended_metrics[0]:.4f}, NSE: {extended_metrics[1]:.4f}, Correlation: {extended_metrics[2]:.4f}, R²: {extended_metrics[3]:.4f}")
    
    # 模型系数分析
    print(f"\n=== 基础模型系数 ===")
    print(f"截距: {basic_model.intercept_:.6f}")
    for i, coef in enumerate(basic_model.coef_):
        print(f"Product{i+1}系数: {coef:.6f}")
    
    print(f"\n=== 扩展模型系数 ===")
    print(f"截距: {extended_model.intercept_:.6f}")
    for i, (feature, coef) in enumerate(zip(extended_features, extended_model.coef_)):
        print(f"{feature}系数: {coef:.6f}")
    
    # 残差分析
    basic_residuals = analyze_residuals(y_test, y_basic_pred)
    extended_residuals = analyze_residuals(y_test, y_extended_pred)
    
    print(f"\n=== 残差分析 ===")
    print(f"基础模型残差 - 均值: {basic_residuals['mean']:.6f}, 标准差: {basic_residuals['std']:.4f}")
    print(f"扩展模型残差 - 均值: {extended_residuals['mean']:.6f}, 标准差: {extended_residuals['std']:.4f}")
    
    # 可视化结果
    plt.figure(figsize=(20, 15))
    
    # 散点图对比
    plt.subplot(3, 4, 1)
    plt.scatter(y_test, y_basic_pred, alpha=0.5, s=1)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Basic Model\nRMSE={basic_metrics[0]:.4f}, R²={basic_metrics[3]:.4f}')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(3, 4, 2)
    plt.scatter(y_test, y_extended_pred, alpha=0.5, s=1)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=1)
    plt.xlabel('Actual Rainfall (mm)')
    plt.ylabel('Predicted Rainfall (mm)')
    plt.title(f'Extended Model\nRMSE={extended_metrics[0]:.4f}, R²={extended_metrics[3]:.4f}')
    plt.grid(True, alpha=0.3)
    
    # 时间序列对比
    plt.subplot(3, 4, 3)
    n_show = min(500, len(y_test))
    plt.plot(y_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(y_basic_pred[:n_show], 'r-', linewidth=1, label='Basic MLR', alpha=0.8)
    plt.plot(y_extended_pred[:n_show], 'b-', linewidth=1, label='Extended MLR', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Time Series Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 残差图
    plt.subplot(3, 4, 4)
    residuals_basic = y_test - y_basic_pred
    plt.scatter(y_basic_pred, residuals_basic, alpha=0.5, s=1)
    plt.axhline(y=0, color='r', linestyle='--', lw=1)
    plt.xlabel('Predicted Rainfall (mm)')
    plt.ylabel('Residuals (mm)')
    plt.title('Basic Model Residuals')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(3, 4, 5)
    residuals_extended = y_test - y_extended_pred
    plt.scatter(y_extended_pred, residuals_extended, alpha=0.5, s=1)
    plt.axhline(y=0, color='r', linestyle='--', lw=1)
    plt.xlabel('Predicted Rainfall (mm)')
    plt.ylabel('Residuals (mm)')
    plt.title('Extended Model Residuals')
    plt.grid(True, alpha=0.3)
    
    # 残差分布
    plt.subplot(3, 4, 6)
    plt.hist(residuals_basic, bins=50, alpha=0.7, edgecolor='black', label='Basic')
    plt.hist(residuals_extended, bins=50, alpha=0.7, edgecolor='black', label='Extended')
    plt.xlabel('Residuals (mm)')
    plt.ylabel('Frequency')
    plt.title('Residual Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 系数可视化
    plt.subplot(3, 4, 7)
    plt.bar(basic_features, basic_model.coef_, alpha=0.8)
    plt.xlabel('Features')
    plt.ylabel('Coefficients')
    plt.title('Basic Model Coefficients')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    plt.subplot(3, 4, 8)
    plt.bar(extended_features, extended_model.coef_, alpha=0.8)
    plt.xlabel('Features')
    plt.ylabel('Coefficients')
    plt.title('Extended Model Coefficients')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 产品对比
    plt.subplot(3, 4, 9)
    n_show = min(200, len(y_test))
    plt.plot(y_test[:n_show], 'g-', linewidth=2, label='Actual', alpha=0.8)
    plt.plot(X_basic_test[:n_show, 0], '--', linewidth=1, label='Product 1', alpha=0.7, color='orange')
    plt.plot(X_basic_test[:n_show, 1], '--', linewidth=1, label='Product 2', alpha=0.7, color='purple')
    plt.plot(X_basic_test[:n_show, 2], '--', linewidth=1, label='Product 3', alpha=0.7, color='blue')
    plt.plot(y_extended_pred[:n_show], 'r-', linewidth=2, label='MLR Fusion', alpha=0.8)
    plt.xlabel('Time Index')
    plt.ylabel('Rainfall (mm)')
    plt.title('Products vs Fusion Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 模型性能对比
    plt.subplot(3, 4, 10)
    models = ['Basic', 'Basic\n(Scaled)', 'Extended']
    rmse_values = [basic_metrics[0], basic_scaled_metrics[0], extended_metrics[0]]
    nse_values = [basic_metrics[1], basic_scaled_metrics[1], extended_metrics[1]]
    r2_values = [basic_metrics[3], basic_scaled_metrics[3], extended_metrics[3]]
    
    x = np.arange(len(models))
    width = 0.25
    
    plt.bar(x - width, rmse_values, width, label='RMSE', alpha=0.8)
    plt.bar(x, nse_values, width, label='NSE', alpha=0.8)
    plt.bar(x + width, r2_values, width, label='R²', alpha=0.8)
    plt.xlabel('Models')
    plt.ylabel('Values')
    plt.title('Model Performance Comparison')
    plt.xticks(x, models)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Q-Q图检验残差正态性
    plt.subplot(3, 4, 11)
    stats.probplot(residuals_extended, dist="norm", plot=plt)
    plt.title('Q-Q Plot (Extended Model)')
    plt.grid(True, alpha=0.3)
    
    # 相关性热力图
    plt.subplot(3, 4, 12)
    corr_matrix = data[basic_features + ['tp']].corr()
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.3f', cbar_kws={'shrink': 0.8})
    plt.title('Feature Correlation Matrix')
    
    plt.tight_layout()
    plt.savefig('Exp/multiple_linear_regression_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存结果
    results_df = pd.DataFrame({
        'Actual': y_test,
        'Basic_MLR': y_basic_pred,
        'Extended_MLR': y_extended_pred,
        'Product1': X_basic_test[:, 0],
        'Product2': X_basic_test[:, 1],
        'Product3': X_basic_test[:, 2]
    })
    results_df.to_csv('Exp/multiple_linear_regression_predictions.csv', index=False)
    
    # 保存模型系数
    coefficients_df = pd.DataFrame({
        'Feature': ['Intercept'] + basic_features,
        'Basic_Model_Coefficient': [basic_model.intercept_] + list(basic_model.coef_)
    })
    
    extended_coefficients_df = pd.DataFrame({
        'Feature': ['Intercept'] + extended_features,
        'Extended_Model_Coefficient': [extended_model.intercept_] + list(extended_model.coef_)
    })
    
    coefficients_df.to_csv('Exp/mlr_basic_coefficients.csv', index=False)
    extended_coefficients_df.to_csv('Exp/mlr_extended_coefficients.csv', index=False)
    
    print(f"\n结果已保存到:")
    print(f"- 图表: Exp/multiple_linear_regression_results.png")
    print(f"- 预测结果: Exp/multiple_linear_regression_predictions.csv")
    print(f"- 基础模型系数: Exp/mlr_basic_coefficients.csv")
    print(f"- 扩展模型系数: Exp/mlr_extended_coefficients.csv")

if __name__ == "__main__":
    main()
