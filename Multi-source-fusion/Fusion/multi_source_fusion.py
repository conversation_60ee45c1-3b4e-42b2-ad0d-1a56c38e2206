import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set font for plots (support Chinese characters)
import matplotlib
import matplotlib.font_manager as fm

# 清除字体缓存并重建
try:
    fm.fontManager.__init__()
except:
    pass

# 设置支持中文的字体
matplotlib.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'SimHei', 'DejaVu Sans', 'Arial', 'Liberation Sans', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

# 为中文标签创建字体属性
try:
    chinese_font_path = '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc'
    chinese_font_prop = fm.FontProperties(fname=chinese_font_path)
except:
    chinese_font_prop = fm.FontProperties()

class MultiSourceFusion:
    def __init__(self, data_path):
        """
        初始化多源降水融合类
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.forecast_cols = ['E1', 'E2', 'China', 'NOAA']
        self.target_col = 'real_tp'
        
    def load_data(self, file_name):
        """加载数据"""
        file_path = f"{self.data_path}/{file_name}"
        self.data = pd.read_csv(file_path)
        self.data['valid_time'] = pd.to_datetime(self.data['valid_time'])
        return self.data
    
    def split_data(self, train_ratio=0.7, val_ratio=0.0):
        """
        按时间顺序分割数据

        Args:
            train_ratio: 训练集比例
            val_ratio: 验证集比例（0表示不使用验证集）

        Returns:
            tuple: (train_indices, val_indices, test_indices)
        """
        n = len(self.data)
        train_end = int(n * train_ratio)

        if val_ratio > 0:
            val_end = int(n * (train_ratio + val_ratio))
            train_indices = slice(0, train_end)
            val_indices = slice(train_end, val_end)
            test_indices = slice(val_end, n)
            return train_indices, val_indices, test_indices
        else:
            train_indices = slice(0, train_end)
            test_indices = slice(train_end, n)
            return train_indices, None, test_indices

    def get_evaluation_indices(self):
        """
        获取新的评估策略的数据索引
        - 训练集: 0%-70%的数据 (0-0.7)
        - 验证集: 70%-80%的数据 (0.7-0.8)
        - 测试集: 80%-100%的数据 (0.8-1.0)

        Returns:
            tuple: (train_indices, val_indices, test_indices)
        """
        n = len(self.data)
        train_end = int(n * 0.7)
        val_start = int(n * 0.7)
        val_end = int(n * 0.8)
        test_start = int(n * 0.8)

        train_indices = slice(0, train_end)  # 0%-70%用于训练
        val_indices = slice(val_start, val_end)  # 70%-80%用于验证
        test_indices = slice(test_start, n)  # 80%-100%用于测试

        return train_indices, val_indices, test_indices

    def calculate_metrics(self, observed, predicted):
        """
        计算评价指标

        Args:
            observed: 观测值
            predicted: 预测值

        Returns:
            dict: 包含NSE、相关系数、RMSE、MAE的字典
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        obs = observed[mask]
        pred = predicted[mask]

        if len(obs) == 0:
            return {'NSE': np.nan, 'Correlation': np.nan, 'RMSE': np.nan, 'MAE': np.nan}

        # NSE (Nash-Sutcliffe Efficiency)
        nse = 1 - (np.sum((obs - pred) ** 2) / np.sum((obs - np.mean(obs)) ** 2))

        # 相关系数
        correlation = np.corrcoef(obs, pred)[0, 1] if len(obs) > 1 else np.nan

        # RMSE
        rmse = np.sqrt(mean_squared_error(obs, pred))

        # MAE
        mae = mean_absolute_error(obs, pred)

        return {
            'NSE': nse,
            'Correlation': correlation,
            'RMSE': rmse,
            'MAE': mae
        }
    
    def simple_average(self):
        """方法1: 简单平均法 - 无需训练，直接对所有数据应用"""
        forecast_data = self.data[self.forecast_cols].values
        fusion_result = np.mean(forecast_data, axis=1)
        return fusion_result

    def rmse_weighted_average(self):
        """方法2: RMSE倒数加权平均法 - 使用所有可用数据计算权重"""
        forecast_data = self.data[self.forecast_cols].values
        observed = self.data[self.target_col].values

        # 使用新的评估策略：0%-70%数据用于训练
        train_indices, _, _ = self.get_evaluation_indices()

        # 使用训练集数据计算权重
        train_forecast = forecast_data[train_indices]
        train_observed = observed[train_indices]

        weights = []
        for i, _ in enumerate(self.forecast_cols):
            pred = train_forecast[:, i]
            mask = ~(np.isnan(train_observed) | np.isnan(pred))
            if np.sum(mask) > 0:
                rmse = np.sqrt(mean_squared_error(train_observed[mask], pred[mask]))
                weight = 1 / (rmse + 1e-8)  # 避免除零
            else:
                weight = 0
            weights.append(weight)

        # 归一化权重
        weights = np.array(weights)
        weights = weights / np.sum(weights) if np.sum(weights) > 0 else np.ones(len(weights)) / len(weights)

        # 对所有数据应用权重
        fusion_result = np.sum(forecast_data * weights, axis=1)
        return fusion_result
    
    def multiple_linear_regression(self):
        """方法3: 多元线性回归法 - 使用所有可用数据训练"""
        forecast_data = self.data[self.forecast_cols].values
        observed = self.data[self.target_col].values

        # 使用新的评估策略：0%-70%数据用于训练
        train_indices, _, _ = self.get_evaluation_indices()

        # 使用训练集数据训练
        train_forecast = forecast_data[train_indices]
        train_observed = observed[train_indices]

        # 移除包含NaN的行
        mask = ~(np.isnan(train_observed) | np.any(np.isnan(train_forecast), axis=1))
        X_train = train_forecast[mask]
        y_train = train_observed[mask]

        if len(X_train) == 0:
            return np.full(len(observed), np.nan)

        # 训练线性回归模型
        model = LinearRegression()
        model.fit(X_train, y_train)

        # 对所有数据进行预测
        fusion_result = model.predict(forecast_data)
        return fusion_result
    
    def ensemble_kalman_filter(self):
        """方法4: 集合卡尔曼滤波 (简化版本)"""
        forecast_data = self.data[self.forecast_cols].values
        observed = self.data[self.target_col].values
        
        n_forecasts = len(self.forecast_cols)
        n_times = len(observed)
        
        # 初始化
        fusion_result = np.zeros(n_times)
        P = np.eye(n_forecasts)  # 误差协方差矩阵
        Q = 0.01 * np.eye(n_forecasts)  # 过程噪声
        R = 1.0  # 观测噪声
        
        # 初始状态
        x = np.mean(forecast_data[0]) if not np.any(np.isnan(forecast_data[0])) else 0
        
        for t in range(n_times):
            # 预测步骤
            if t > 0:
                x_pred = x
                P_pred = P + Q
            else:
                x_pred = np.mean(forecast_data[t]) if not np.any(np.isnan(forecast_data[t])) else 0
                P_pred = P
            
            # 更新步骤
            if not np.isnan(observed[t]):
                H = np.ones((1, n_forecasts)) / n_forecasts  # 观测矩阵
                K = P_pred @ H.T / (H @ P_pred @ H.T + R)  # 卡尔曼增益
                x = x_pred + K.flatten()[0] * (observed[t] - H @ np.ones(n_forecasts) * x_pred)
                P = (np.eye(n_forecasts) - K @ H) @ P_pred
            else:
                x = x_pred
                P = P_pred
            
            fusion_result[t] = x
        
        return fusion_result
    
    def bayesian_model_averaging(self):
        """方法5: 贝叶斯模型平均 - 使用所有可用数据计算权重"""
        forecast_data = self.data[self.forecast_cols].values
        observed = self.data[self.target_col].values

        # 使用新的评估策略：0%-70%数据用于训练
        train_indices, _, _ = self.get_evaluation_indices()

        # 使用训练集数据计算权重
        train_forecast = forecast_data[train_indices]
        train_observed = observed[train_indices]

        # 计算每个模型的似然权重
        weights = []
        for i, _ in enumerate(self.forecast_cols):
            pred = train_forecast[:, i]
            mask = ~(np.isnan(train_observed) | np.isnan(pred))
            if np.sum(mask) > 0:
                # 计算似然 (基于正态分布假设)
                residuals = train_observed[mask] - pred[mask]
                sigma = np.std(residuals)
                likelihood = np.exp(-0.5 * np.sum(residuals**2) / (sigma**2 + 1e-8))
            else:
                likelihood = 1e-8
            weights.append(likelihood)

        # 归一化权重
        weights = np.array(weights)
        weights = weights / np.sum(weights) if np.sum(weights) > 0 else np.ones(len(weights)) / len(weights)

        # 对所有数据应用贝叶斯平均
        fusion_result = np.sum(forecast_data * weights, axis=1)
        return fusion_result
    
    def xgboost_method(self):
        """方法6: XGBoost机器学习方法 - 使用所有可用数据训练"""
        forecast_data = self.data[self.forecast_cols].values
        observed = self.data[self.target_col].values

        # 使用新的评估策略
        train_indices, val_indices, _ = self.get_evaluation_indices()

        # 使用0%-70%数据训练，70%-80%数据作为验证集
        train_forecast = forecast_data[train_indices]
        train_observed = observed[train_indices]
        val_forecast = forecast_data[val_indices]
        val_observed = observed[val_indices]

        # 移除包含NaN的行
        train_mask = ~(np.isnan(train_observed) | np.any(np.isnan(train_forecast), axis=1))
        val_mask = ~(np.isnan(val_observed) | np.any(np.isnan(val_forecast), axis=1))

        X_train = train_forecast[train_mask]
        y_train = train_observed[train_mask]
        X_val = val_forecast[val_mask]
        y_val = val_observed[val_mask]

        if len(X_train) == 0:
            return np.full(len(observed), np.nan)

        # 训练XGBoost模型（使用验证集进行早停）
        model = xgb.XGBRegressor(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            random_state=42,
            early_stopping_rounds=10
        )

        # 如果有验证集，使用早停
        if len(X_val) > 0:
            model.fit(X_train, y_train,
                     eval_set=[(X_val, y_val)],
                     verbose=False)
        else:
            model.fit(X_train, y_train)

        # 对所有数据进行预测
        fusion_result = model.predict(forecast_data)
        return fusion_result
    
    def deep_learning_mlp(self):
        """方法7: 深度学习MLP方法 - 使用所有可用数据训练"""
        forecast_data = self.data[self.forecast_cols].values
        observed = self.data[self.target_col].values

        # 使用新的评估策略
        train_indices, val_indices, _ = self.get_evaluation_indices()

        # 使用0%-70%数据训练，70%-80%数据作为验证集
        train_forecast = forecast_data[train_indices]
        train_observed = observed[train_indices]
        val_forecast = forecast_data[val_indices]
        val_observed = observed[val_indices]

        # 移除包含NaN的行
        train_mask = ~(np.isnan(train_observed) | np.any(np.isnan(train_forecast), axis=1))
        val_mask = ~(np.isnan(val_observed) | np.any(np.isnan(val_forecast), axis=1))

        X_train = train_forecast[train_mask]
        y_train = train_observed[train_mask]
        X_val = val_forecast[val_mask]
        y_val = val_observed[val_mask]

        if len(X_train) == 0:
            return np.full(len(observed), np.nan)

        # 数据标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)

        # 训练MLP模型（使用验证集进行早停）
        model = MLPRegressor(
            hidden_layer_sizes=(64, 32, 16),
            activation='relu',
            solver='adam',
            max_iter=1000,
            random_state=42,
            early_stopping=True,
            validation_fraction=0.0,  # 我们手动提供验证集
            n_iter_no_change=20
        )

        # 如果有验证集，使用它进行训练
        if len(X_val) > 0:
            X_val_scaled = scaler.transform(X_val)
            # 合并训练和验证数据，但标记验证集用于早停
            X_combined = np.vstack([X_train_scaled, X_val_scaled])
            y_combined = np.hstack([y_train, y_val])

            # 使用sklearn的内置验证机制
            model.validation_fraction = len(X_val_scaled) / len(X_combined)
            model.fit(X_combined, y_combined)
        else:
            model.fit(X_train_scaled, y_train)

        # 对所有数据进行预测
        forecast_data_scaled = scaler.transform(forecast_data)
        fusion_result = model.predict(forecast_data_scaled)
        return fusion_result

    def evaluate_individual_products(self):
        """评估各个预报产品在测试集上的性能"""
        print("\n" + "="*60)
        print("评估各个预报产品在测试集上的性能")
        print("="*60)

        # 使用新的评估策略：在80%-100%的数据上评估
        _, _, test_indices = self.get_evaluation_indices()

        # 获取测试集数据
        test_observed = self.data[self.target_col].values[test_indices]
        test_forecast_data = self.data[self.forecast_cols].values[test_indices]

        product_results = {}

        print(f"测试集样本数: {len(test_observed)}")
        print("-" * 60)

        for i, product_name in enumerate(self.forecast_cols):
            test_predicted = test_forecast_data[:, i]
            metrics = self.calculate_metrics(test_observed, test_predicted)

            product_results[product_name] = metrics

            print(f"{product_name}:")
            print(f"  NSE: {metrics['NSE']:.4f}")
            print(f"  相关系数: {metrics['Correlation']:.4f}")
            print(f"  RMSE: {metrics['RMSE']:.4f}")
            print(f"  MAE: {metrics['MAE']:.4f}")
            print()

        return product_results

    def run_all_methods(self):
        """运行所有融合方法并在测试集上评估"""
        methods = {
            '简单平均法': self.simple_average,
            'RMSE倒数加权平均法': self.rmse_weighted_average,
            '多元线性回归法': self.multiple_linear_regression,
            '集合卡尔曼滤波': self.ensemble_kalman_filter,
            '贝叶斯模型平均': self.bayesian_model_averaging,
            'XGBoost方法': self.xgboost_method,
            '深度学习MLP方法': self.deep_learning_mlp
        }

        # 定义哪些方法需要验证集
        methods_need_validation = {'XGBoost方法', '深度学习MLP方法'}

        results = {}
        observed = self.data[self.target_col].values

        # 首先评估各个预报产品的性能
        product_results = self.evaluate_individual_products()
        results['预报产品性能'] = product_results

        print("\n" + "="*60)
        print("评估融合方法在测试集上的性能")
        print("="*60)

        for method_name, method_func in methods.items():
            print(f"正在运行: {method_name}")
            try:
                predicted = method_func()

                # 使用新的评估策略：在80%-100%的数据上评估
                _, _, test_indices = self.get_evaluation_indices()
                test_observed = observed[test_indices]
                test_predicted = predicted[test_indices]
                metrics = self.calculate_metrics(test_observed, test_predicted)
                print(f"  (测试集评估: {len(test_observed)}个样本，占总数据的20%，位置80%-100%)")

                results[method_name] = {
                    'predicted': predicted,
                    'metrics': metrics
                }
                print(f"  NSE: {metrics['NSE']:.4f}, 相关系数: {metrics['Correlation']:.4f}, "
                      f"RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")
            except Exception as e:
                print(f"  错误: {str(e)}")
                results[method_name] = {
                    'predicted': np.full(len(observed), np.nan),
                    'metrics': {'NSE': np.nan, 'Correlation': np.nan, 'RMSE': np.nan, 'MAE': np.nan}
                }

        return results


def analyze_all_watersheds():
    """分析所有流域的数据"""
    watersheds = ['chengkou.csv', 'guojia.csv', 'lianghe.csv', 'yantang.csv']
    data_path = '../data'

    all_results = {}

    for watershed in watersheds:
        print(f"\n{'='*50}")
        print(f"分析流域: {watershed}")
        print(f"{'='*50}")

        # 创建融合对象
        fusion = MultiSourceFusion(data_path)

        # 加载数据
        data = fusion.load_data(watershed)
        print(f"数据形状: {data.shape}")
        print(f"时间范围: {data['valid_time'].min()} 到 {data['valid_time'].max()}")

        # 运行所有方法
        results = fusion.run_all_methods()
        all_results[watershed.replace('.csv', '')] = results

    return all_results


def create_comparison_report(all_results, output_path='./'):
    """创建比较报告"""
    # 创建融合方法结果汇总表
    summary_data = []
    # 创建预报产品结果汇总表
    product_summary_data = []

    for watershed, methods in all_results.items():
        for method_name, result in methods.items():
            if method_name == '预报产品性能':
                # 处理预报产品性能结果
                for product_name, metrics in result.items():
                    product_summary_data.append({
                        '流域': watershed,
                        '预报产品': product_name,
                        'NSE': metrics['NSE'],
                        '相关系数': metrics['Correlation'],
                        'RMSE': metrics['RMSE'],
                        'MAE': metrics['MAE']
                    })
            else:
                # 处理融合方法结果
                metrics = result['metrics']
                summary_data.append({
                    '流域': watershed,
                    '方法': method_name,
                    'NSE': metrics['NSE'],
                    '相关系数': metrics['Correlation'],
                    'RMSE': metrics['RMSE'],
                    'MAE': metrics['MAE']
                })

    # 创建数据框
    summary_df = pd.DataFrame(summary_data)
    product_summary_df = pd.DataFrame(product_summary_data)

    # 保存详细结果（保持中文列名）
    summary_df.to_csv(f'{output_path}/fusion_results_summary.csv', index=False, encoding='utf-8-sig')
    product_summary_df.to_csv(f'{output_path}/product_performance_summary.csv', index=False, encoding='utf-8-sig')

    # 为绘图创建英文列名的副本
    summary_df_en = summary_df.copy()
    summary_df_en = summary_df_en.rename(columns={
        '流域': 'Watershed',
        '方法': 'Method',
        '相关系数': 'Correlation',
        '预报产品': 'Product'
    })

    # 创建可视化
    create_visualizations(summary_df_en, all_results, output_path)

    # 生成最佳方法报告
    generate_best_methods_report(summary_df, output_path)

    return summary_df


def create_visualizations(summary_df, all_results, output_path):
    """Create visualization charts"""
    # Set chart style
    plt.style.use('default')

    # 1. NSE comparison of different methods across watersheds
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    metrics = ['NSE', 'Correlation', 'RMSE', 'MAE']

    for i, metric in enumerate(metrics):
        ax = axes[i//2, i%2]
        pivot_data = summary_df.pivot(index='Method', columns='Watershed', values=metric)

        # Create heatmap
        sns.heatmap(pivot_data, annot=True, fmt='.3f', cmap='RdYlBu_r' if metric in ['RMSE', 'MAE'] else 'RdYlBu',
                   ax=ax, cbar_kws={'shrink': 0.8})
        ax.set_title(f'{metric} 对比', fontproperties=chinese_font_prop, fontsize=12, fontweight='bold')
        ax.set_xlabel('流域', fontproperties=chinese_font_prop, fontsize=10)
        ax.set_ylabel('融合方法', fontproperties=chinese_font_prop, fontsize=10)

        # 旋转x轴标签
        ax.tick_params(axis='x', rotation=45)
        ax.tick_params(axis='y', rotation=0)

    plt.tight_layout()
    plt.savefig(f'{output_path}/methods_comparison_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 各方法综合性能雷达图
    create_radar_chart(summary_df, output_path)

    # 3. 时间序列对比图 (选择最佳方法)
    create_time_series_plots(all_results, summary_df, output_path)


def create_radar_chart(summary_df, output_path):
    """Create radar chart showing comprehensive performance of each method"""
    # Calculate average performance of each method
    # summary_df should already have English column names
    method_avg = summary_df.groupby('Method').agg({
        'NSE': 'mean',
        'Correlation': 'mean',
        'RMSE': 'mean',
        'MAE': 'mean'
    }).reset_index()

    # Normalize metrics (NSE and correlation: higher is better, RMSE and MAE: lower is better)
    method_avg['NSE_norm'] = (method_avg['NSE'] - method_avg['NSE'].min()) / (method_avg['NSE'].max() - method_avg['NSE'].min())
    method_avg['Corr_norm'] = (method_avg['Correlation'] - method_avg['Correlation'].min()) / (method_avg['Correlation'].max() - method_avg['Correlation'].min())
    method_avg['RMSE_norm'] = 1 - (method_avg['RMSE'] - method_avg['RMSE'].min()) / (method_avg['RMSE'].max() - method_avg['RMSE'].min())
    method_avg['MAE_norm'] = 1 - (method_avg['MAE'] - method_avg['MAE'].min()) / (method_avg['MAE'].max() - method_avg['MAE'].min())

    # Create radar chart
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

    angles = np.linspace(0, 2 * np.pi, 4, endpoint=False).tolist()
    angles += angles[:1]  # Close the plot

    labels = ['NSE', 'Correlation', 'RMSE', 'MAE']

    colors = plt.cm.Set3(np.linspace(0, 1, len(method_avg)))

    for i, (_, row) in enumerate(method_avg.iterrows()):
        values = [row['NSE_norm'], row['Corr_norm'], row['RMSE_norm'], row['MAE_norm']]
        values += values[:1]  # Close the plot

        ax.plot(angles, values, 'o-', linewidth=2, label=row['Method'], color=colors[i])
        ax.fill(angles, values, alpha=0.25, color=colors[i])

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(labels)
    ax.set_ylim(0, 1)
    ax.set_title('融合方法综合性能对比', fontproperties=chinese_font_prop, size=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), prop=chinese_font_prop)
    ax.grid(True)

    plt.tight_layout()
    plt.savefig(f'{output_path}/methods_radar_chart.png', dpi=300, bbox_inches='tight')
    plt.close()


def create_time_series_plots(all_results, summary_df, output_path):
    """Create time series comparison plots"""
    # Find the best method for each watershed
    # Handle both Chinese and English column names
    watershed_col = '流域' if '流域' in summary_df.columns else 'Watershed'
    method_col = '方法' if '方法' in summary_df.columns else 'Method'
    corr_col = '相关系数' if '相关系数' in summary_df.columns else 'Correlation'

    best_methods = {}
    for watershed in summary_df[watershed_col].unique():
        watershed_data = summary_df[summary_df[watershed_col] == watershed]
        # Comprehensive score: NSE and correlation are positive, RMSE and MAE are negative
        watershed_data = watershed_data.copy()
        watershed_data['Comprehensive_Score'] = (watershed_data['NSE'] + watershed_data[corr_col] -
                                watershed_data['RMSE']/watershed_data['RMSE'].max() -
                                watershed_data['MAE']/watershed_data['MAE'].max())
        best_method = watershed_data.loc[watershed_data['Comprehensive_Score'].idxmax(), method_col]
        best_methods[watershed] = best_method

    # Create time series plots
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    axes = axes.flatten()

    watersheds = list(all_results.keys())

    for i, watershed in enumerate(watersheds):
        ax = axes[i]

        # Get data
        fusion = MultiSourceFusion('../data')
        data = fusion.load_data(f'{watershed}.csv')

        observed = data['real_tp'].values
        time_index = data['valid_time']

        # Plot observed values
        ax.plot(time_index, observed, 'k-', label='观测值', linewidth=1, alpha=0.8)

        # Plot best method results
        best_method = best_methods[watershed]
        predicted = all_results[watershed][best_method]['predicted']
        ax.plot(time_index, predicted, 'r-', label=f'最佳方法: {best_method}', linewidth=1, alpha=0.8)

        # Plot simple average as baseline
        simple_avg_key = '简单平均法' if '简单平均法' in all_results[watershed] else 'Simple Average'
        simple_avg = all_results[watershed][simple_avg_key]['predicted']
        ax.plot(time_index, simple_avg, 'b--', label='简单平均法', linewidth=1, alpha=0.6)

        ax.set_title(f'{watershed} 流域 - 最佳融合方法对比', fontproperties=chinese_font_prop, fontsize=12, fontweight='bold')
        ax.set_xlabel('时间', fontproperties=chinese_font_prop)
        ax.set_ylabel('降水量 (mm)', fontproperties=chinese_font_prop)
        ax.legend(prop=chinese_font_prop)
        ax.grid(True, alpha=0.3)

        # Set x-axis date format
        ax.tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig(f'{output_path}/time_series_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()


def generate_best_methods_report(summary_df, output_path):
    """Generate best methods report"""
    # Handle both Chinese and English column names
    method_col = '方法' if '方法' in summary_df.columns else 'Method'
    watershed_col = '流域' if '流域' in summary_df.columns else 'Watershed'
    corr_col = '相关系数' if '相关系数' in summary_df.columns else 'Correlation'

    report = []
    report.append("# Multi-Source Precipitation Fusion Methods Evaluation Report\n")
    report.append("## 1. Overall Results Summary\n")

    # Best methods for each metric
    metrics = ['NSE', corr_col, 'RMSE', 'MAE']
    metric_names = ['NSE', 'Correlation', 'RMSE', 'MAE']

    for i, metric in enumerate(metrics):
        if metric in ['NSE', corr_col]:
            best_overall = summary_df.groupby(method_col)[metric].mean().idxmax()
            best_value = summary_df.groupby(method_col)[metric].mean().max()
        else:
            best_overall = summary_df.groupby(method_col)[metric].mean().idxmin()
            best_value = summary_df.groupby(method_col)[metric].mean().min()

        report.append(f"- **Best {metric_names[i]} Method**: {best_overall} ({best_value:.4f})")

    report.append("\n## 2. Best Methods for Each Watershed\n")

    for watershed in summary_df[watershed_col].unique():
        watershed_data = summary_df[summary_df[watershed_col] == watershed]
        report.append(f"### {watershed} Watershed\n")

        # Calculate comprehensive score
        watershed_data = watershed_data.copy()
        watershed_data['Comprehensive_Score'] = (watershed_data['NSE'] + watershed_data[corr_col] -
                                watershed_data['RMSE']/watershed_data['RMSE'].max() -
                                watershed_data['MAE']/watershed_data['MAE'].max())

        best_method_row = watershed_data.loc[watershed_data['Comprehensive_Score'].idxmax()]

        report.append(f"- **Best Method**: {best_method_row[method_col]}")
        report.append(f"- **NSE**: {best_method_row['NSE']:.4f}")
        report.append(f"- **Correlation**: {best_method_row[corr_col]:.4f}")
        report.append(f"- **RMSE**: {best_method_row['RMSE']:.4f}")
        report.append(f"- **MAE**: {best_method_row['MAE']:.4f}\n")

    report.append("## 3. Method Rankings\n")

    # Calculate overall ranking
    method_scores = summary_df.groupby(method_col).agg({
        'NSE': 'mean',
        corr_col: 'mean',
        'RMSE': 'mean',
        'MAE': 'mean'
    })

    # Normalize and calculate comprehensive score
    method_scores['Comprehensive_Score'] = ((method_scores['NSE'] - method_scores['NSE'].min()) /
                           (method_scores['NSE'].max() - method_scores['NSE'].min()) +
                           (method_scores[corr_col] - method_scores[corr_col].min()) /
                           (method_scores[corr_col].max() - method_scores[corr_col].min()) +
                           (method_scores['RMSE'].max() - method_scores['RMSE']) /
                           (method_scores['RMSE'].max() - method_scores['RMSE'].min()) +
                           (method_scores['MAE'].max() - method_scores['MAE']) /
                           (method_scores['MAE'].max() - method_scores['MAE'].min())) / 4

    method_ranking = method_scores.sort_values('Comprehensive_Score', ascending=False)

    for i, (method, row) in enumerate(method_ranking.iterrows(), 1):
        report.append(f"{i}. **{method}** (Comprehensive Score: {row['Comprehensive_Score']:.4f})")
        report.append(f"   - NSE: {row['NSE']:.4f}, Correlation: {row[corr_col]:.4f}, RMSE: {row['RMSE']:.4f}, MAE: {row['MAE']:.4f}\n")

    # Save report
    with open(f'{output_path}/fusion_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))

    print("Report saved to fusion_analysis_report.md")


if __name__ == "__main__":
    print("Starting multi-source precipitation fusion analysis...")

    # Analyze all watersheds
    all_results = analyze_all_watersheds()

    # Create comparison report
    print("\nGenerating comparison report and visualizations...")
    summary_df = create_comparison_report(all_results, './')

    print("\nAnalysis completed!")
    print("Generated files:")
    print("- fusion_results_summary.csv: Detailed results summary")
    print("- methods_comparison_heatmap.png: Methods comparison heatmap")
    print("- methods_radar_chart.png: Comprehensive performance radar chart")
    print("- time_series_comparison.png: Time series comparison plot")
    print("- fusion_analysis_report.md: Detailed analysis report")
