#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多源降水融合分析运行脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from multi_source_fusion import analyze_all_watersheds, create_comparison_report

def main():
    """主函数"""
    print("="*60)
    print("多源降水融合分析系统")
    print("="*60)
    print("分析方法包括:")
    print("1. 简单平均法")
    print("2. RMSE倒数加权平均法") 
    print("3. 多元线性回归法")
    print("4. 集合卡尔曼滤波")
    print("5. 贝叶斯模型平均")
    print("6. XGBoost机器学习方法")
    print("7. 深度学习MLP方法")
    print()
    print("评价指标: NSE、相关系数、RMSE、MAE")
    print("="*60)
    
    try:
        # 分析所有流域
        print("开始分析所有流域数据...")
        all_results = analyze_all_watersheds()
        
        # 创建比较报告
        print("\n生成比较报告和可视化...")
        summary_df = create_comparison_report(all_results, './')
        
        print("\n" + "="*60)
        print("分析完成！")
        print("="*60)
        print("生成的文件:")
        print("- fusion_results_summary.csv: 详细结果汇总表")
        print("- methods_comparison_heatmap.png: 方法对比热力图")
        print("- methods_radar_chart.png: 综合性能雷达图") 
        print("- time_series_comparison.png: 时间序列对比图")
        print("- fusion_analysis_report.md: 详细分析报告")
        print("="*60)
        
        # 显示简要结果
        print("\n简要结果预览:")
        print("-" * 40)
        
        # 显示各方法的平均性能
        avg_performance = summary_df.groupby('方法').agg({
            'NSE': 'mean',
            '相关系数': 'mean', 
            'RMSE': 'mean',
            'MAE': 'mean'
        }).round(4)
        
        print("各方法平均性能:")
        print(avg_performance.to_string())
        
        # 找出最佳方法
        avg_performance['综合得分'] = ((avg_performance['NSE'] - avg_performance['NSE'].min()) / 
                                   (avg_performance['NSE'].max() - avg_performance['NSE'].min()) +
                                   (avg_performance['相关系数'] - avg_performance['相关系数'].min()) / 
                                   (avg_performance['相关系数'].max() - avg_performance['相关系数'].min()) +
                                   (avg_performance['RMSE'].max() - avg_performance['RMSE']) / 
                                   (avg_performance['RMSE'].max() - avg_performance['RMSE'].min()) +
                                   (avg_performance['MAE'].max() - avg_performance['MAE']) / 
                                   (avg_performance['MAE'].max() - avg_performance['MAE'].min())) / 4
        
        best_method = avg_performance['综合得分'].idxmax()
        print(f"\n综合性能最佳方法: {best_method}")
        print(f"综合得分: {avg_performance.loc[best_method, '综合得分']:.4f}")
        
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
