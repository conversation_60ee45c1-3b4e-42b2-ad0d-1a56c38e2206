#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多源降水融合结果汇总展示
"""

import pandas as pd
import numpy as np

def display_results():
    """展示分析结果"""
    # 读取结果
    df = pd.read_csv('fusion_results_summary.csv')
    
    print("="*80)
    print("多源降水融合方法评估结果汇总")
    print("="*80)
    
    # 1. 各方法总体性能排名
    print("\n1. 各方法总体性能排名 (按综合得分)")
    print("-"*60)
    
    method_avg = df.groupby('方法').agg({
        'NSE': 'mean',
        '相关系数': 'mean',
        'RMSE': 'mean',
        'MAE': 'mean'
    }).round(4)
    
    # 计算综合得分 (标准化后的加权平均)
    method_avg['综合得分'] = ((method_avg['NSE'] - method_avg['NSE'].min()) / 
                           (method_avg['NSE'].max() - method_avg['NSE'].min()) +
                           (method_avg['相关系数'] - method_avg['相关系数'].min()) / 
                           (method_avg['相关系数'].max() - method_avg['相关系数'].min()) +
                           (method_avg['RMSE'].max() - method_avg['RMSE']) / 
                           (method_avg['RMSE'].max() - method_avg['RMSE'].min()) +
                           (method_avg['MAE'].max() - method_avg['MAE']) / 
                           (method_avg['MAE'].max() - method_avg['MAE'].min())) / 4
    
    method_ranking = method_avg.sort_values('综合得分', ascending=False)
    
    for i, (method, row) in enumerate(method_ranking.iterrows(), 1):
        print(f"{i:2d}. {method:15s} | NSE: {row['NSE']:6.4f} | 相关系数: {row['相关系数']:6.4f} | "
              f"RMSE: {row['RMSE']:6.4f} | MAE: {row['MAE']:6.4f} | 综合得分: {row['综合得分']:6.4f}")
    
    # 2. 各流域最佳方法
    print(f"\n2. 各流域最佳方法")
    print("-"*60)
    
    watersheds = df['流域'].unique()
    for watershed in watersheds:
        watershed_data = df[df['流域'] == watershed].copy()
        
        # 计算综合得分
        watershed_data['综合得分'] = ((watershed_data['NSE'] - watershed_data['NSE'].min()) / 
                                   (watershed_data['NSE'].max() - watershed_data['NSE'].min()) +
                                   (watershed_data['相关系数'] - watershed_data['相关系数'].min()) / 
                                   (watershed_data['相关系数'].max() - watershed_data['相关系数'].min()) +
                                   (watershed_data['RMSE'].max() - watershed_data['RMSE']) / 
                                   (watershed_data['RMSE'].max() - watershed_data['RMSE'].min()) +
                                   (watershed_data['MAE'].max() - watershed_data['MAE']) / 
                                   (watershed_data['MAE'].max() - watershed_data['MAE'].min())) / 4
        
        best_method_row = watershed_data.loc[watershed_data['综合得分'].idxmax()]
        
        print(f"{watershed:10s} | 最佳方法: {best_method_row['方法']:15s} | "
              f"NSE: {best_method_row['NSE']:6.4f} | 相关系数: {best_method_row['相关系数']:6.4f} | "
              f"RMSE: {best_method_row['RMSE']:6.4f} | MAE: {best_method_row['MAE']:6.4f}")
    
    # 3. 各指标最佳方法
    print(f"\n3. 各评价指标最佳方法")
    print("-"*60)
    
    metrics = ['NSE', '相关系数', 'RMSE', 'MAE']
    for metric in metrics:
        if metric in ['NSE', '相关系数']:
            best_method = method_avg[metric].idxmax()
            best_value = method_avg[metric].max()
        else:
            best_method = method_avg[metric].idxmin()
            best_value = method_avg[metric].min()
        
        print(f"{metric:8s} 最佳: {best_method:15s} ({best_value:6.4f})")
    
    # 4. 方法性能对比表
    print(f"\n4. 详细性能对比表")
    print("-"*80)
    print(f"{'方法':15s} | {'NSE':8s} | {'相关系数':8s} | {'RMSE':8s} | {'MAE':8s} | {'综合得分':8s}")
    print("-"*80)
    
    for method, row in method_ranking.iterrows():
        print(f"{method:15s} | {row['NSE']:8.4f} | {row['相关系数']:8.4f} | "
              f"{row['RMSE']:8.4f} | {row['MAE']:8.4f} | {row['综合得分']:8.4f}")
    
    # 5. 关键发现
    print(f"\n5. 关键发现")
    print("-"*60)
    
    best_overall = method_ranking.index[0]
    second_best = method_ranking.index[1]
    
    print(f"• 综合性能最佳方法: {best_overall}")
    print(f"  - 在所有4个流域中均表现最佳")
    print(f"  - 平均NSE: {method_ranking.loc[best_overall, 'NSE']:.4f}")
    print(f"  - 平均相关系数: {method_ranking.loc[best_overall, '相关系数']:.4f}")
    print(f"  - 平均RMSE: {method_ranking.loc[best_overall, 'RMSE']:.4f}")
    print(f"  - 平均MAE: {method_ranking.loc[best_overall, 'MAE']:.4f}")
    
    print(f"\n• 第二佳方法: {second_best}")
    print(f"  - 综合得分: {method_ranking.loc[second_best, '综合得分']:.4f}")
    print(f"  - 与最佳方法的性能差距明显")
    
    # 传统方法vs机器学习方法对比
    traditional_methods = ['简单平均法', 'RMSE倒数加权平均法', '多元线性回归法', '集合卡尔曼滤波', '贝叶斯模型平均']
    ml_methods = ['XGBoost方法', '深度学习MLP方法']
    
    traditional_avg = method_avg.loc[traditional_methods].mean()
    ml_avg = method_avg.loc[ml_methods].mean()
    
    print(f"\n• 传统方法 vs 机器学习方法:")
    print(f"  传统方法平均NSE: {traditional_avg['NSE']:.4f}")
    print(f"  机器学习方法平均NSE: {ml_avg['NSE']:.4f}")
    print(f"  机器学习方法NSE提升: {((ml_avg['NSE'] - traditional_avg['NSE']) / traditional_avg['NSE'] * 100):.1f}%")
    
    print("\n" + "="*80)
    print("分析完成！详细结果请查看生成的图表和报告文件。")
    print("="*80)

if __name__ == "__main__":
    display_results()
