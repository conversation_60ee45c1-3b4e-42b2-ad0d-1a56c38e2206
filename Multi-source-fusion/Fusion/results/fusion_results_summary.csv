﻿流域,方法,<PERSON><PERSON>,相关系数,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,简单平均法,-0.15054021452297994,0.4079236590900344,0.3157532004303281,0.10042580875943628
cheng<PERSON>,<PERSON><PERSON><PERSON>倒数加权平均法,-0.135797311936233,0.4083432649507739,0.31372366357273795,0.09954849501261429
chengkou,多元线性回归法,0.13117482845587514,0.40029202152988697,0.27438670033202156,0.08179590408239949
chengkou,集合卡尔曼滤波,0.14489793894894376,0.38145581727684796,0.27221110360407896,0.08945702077837608
cheng<PERSON><PERSON>,贝叶斯模型平均,-0.15054021452297994,0.4079236590900344,0.3157532004303281,0.10042580875943628
cheng<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>法,-0.34956006257213623,0.2670462568646626,0.34197390572653535,0.09174026951806205
chengkou,深度学习MLP方法,-0.2446405434458523,0.35029177562996,0.3284118592960324,0.10812455605655222
guojia,简单平均法,0.3005174433048101,0.5496230643273101,0.42797412848983063,0.10646144533149508
guojia,RMSE倒数加权平均法,0.30276742011608637,0.5532508660475199,0.4272852567999131,0.10803514617555834
guojia,多元线性回归法,0.3129860064352896,0.5612082052324321,0.42414256985282917,0.12277188598497685
guojia,集合卡尔曼滤波,0.137814867747179,0.373197247059131,0.4751484715662038,0.1401447870184715
guojia,贝叶斯模型平均,0.3005174433048101,0.5496230643273101,0.42797412848983063,0.10646144533149508
guojia,XGBoost方法,0.23133410013364542,0.49379602008471163,0.4486398836682446,0.11286498144051259
guojia,深度学习MLP方法,0.2857543129487853,0.5349881808722375,0.4324669118898745,0.11222480895853477
lianghe,简单平均法,0.20549486875726786,0.45395890165810177,0.7246494265314931,0.1637492512657791
lianghe,RMSE倒数加权平均法,0.2062615686911825,0.45475406233900023,0.7242996976584553,0.16368723823381218
lianghe,多元线性回归法,0.27854768389567186,0.5397444535519116,0.6905313744657428,0.1526979109347604
lianghe,集合卡尔曼滤波,0.17687800470755555,0.4244329123546161,0.7375843666669618,0.19564101221115757
lianghe,贝叶斯模型平均,0.20549486875726786,0.45395890165810177,0.7246494265314931,0.1637492512657791
lianghe,XGBoost方法,0.0689445658523049,0.31041759604056834,0.7844537966341205,0.17084848176776127
lianghe,深度学习MLP方法,0.17360056208599228,0.422644327448182,0.739051335651513,0.15205314869698686
yantang,简单平均法,0.10571474703711792,0.38182740365093926,0.7854566605055343,0.1810351558425789
yantang,RMSE倒数加权平均法,0.10147119111452585,0.3775334483397714,0.787318026619472,0.1815799675316531
yantang,多元线性回归法,0.14516845056588878,0.38340842876686954,0.7679350028329763,0.17536397615523022
yantang,集合卡尔曼滤波,0.12128090438304528,0.3575456188722479,0.7785907222333435,0.18923475238163776
yantang,贝叶斯模型平均,0.10571474703711792,0.38182740365093926,0.7854566605055343,0.1810351558425789
yantang,XGBoost方法,0.11314988830337613,0.34266645696269715,0.7821846785387031,0.17083896950398678
yantang,深度学习MLP方法,0.12782568027481833,0.35838368082517885,0.7756857975417559,0.15925740939386626
