#!/usr/bin/env python3
"""
分析所有流域的预报产品和融合方法性能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置中文字体支持
import matplotlib
import matplotlib.font_manager as fm

# 清除字体缓存并重建
try:
    fm.fontManager.__init__()
except:
    pass

# 设置支持中文的字体
matplotlib.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'SimHei', 'DejaVu Sans', 'Arial', 'Liberation Sans', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

# 为中文标签创建字体属性
try:
    chinese_font_path = '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc'
    chinese_font_prop = fm.FontProperties(fname=chinese_font_path)
except:
    chinese_font_prop = fm.FontProperties()

from multi_source_fusion import MultiSourceFusion, analyze_all_watersheds, create_comparison_report
import pandas as pd
import numpy as np

def create_product_performance_report(all_results, output_path='./'):
    """创建预报产品性能报告"""
    print("\n" + "="*60)
    print("创建预报产品性能报告")
    print("="*60)
    
    # 收集所有产品性能数据
    product_data = []
    
    for watershed, methods in all_results.items():
        if '预报产品性能' in methods:
            product_results = methods['预报产品性能']
            for product_name, metrics in product_results.items():
                product_data.append({
                    '流域': watershed,
                    '预报产品': product_name,
                    'NSE': metrics['NSE'],
                    '相关系数': metrics['Correlation'],
                    'RMSE': metrics['RMSE'],
                    'MAE': metrics['MAE']
                })
    
    # 创建数据框
    product_df = pd.DataFrame(product_data)
    
    # 保存详细结果
    product_df.to_csv(f'{output_path}/product_performance_detailed.csv', index=False, encoding='utf-8-sig')
    
    # 创建汇总统计
    print("\n各流域预报产品性能汇总:")
    print("-" * 80)
    
    for watershed in product_df['流域'].unique():
        watershed_data = product_df[product_df['流域'] == watershed]
        print(f"\n{watershed} 流域:")
        print(f"{'产品':<10} {'NSE':<8} {'相关系数':<8} {'RMSE':<8} {'MAE':<8}")
        print("-" * 50)
        
        # 按NSE排序
        watershed_sorted = watershed_data.sort_values('NSE', ascending=False)
        for _, row in watershed_sorted.iterrows():
            print(f"{row['预报产品']:<10} {row['NSE']:<8.4f} {row['相关系数']:<8.4f} "
                  f"{row['RMSE']:<8.4f} {row['MAE']:<8.4f}")
    
    # 创建各产品的平均性能
    print(f"\n各预报产品的平均性能:")
    print("-" * 60)
    product_avg = product_df.groupby('预报产品').agg({
        'NSE': 'mean',
        '相关系数': 'mean', 
        'RMSE': 'mean',
        'MAE': 'mean'
    }).round(4)
    
    # 按平均NSE排序
    product_avg_sorted = product_avg.sort_values('NSE', ascending=False)
    print(f"{'产品':<10} {'平均NSE':<10} {'平均相关系数':<12} {'平均RMSE':<10} {'平均MAE':<10}")
    print("-" * 60)
    for product, row in product_avg_sorted.iterrows():
        print(f"{product:<10} {row['NSE']:<10.4f} {row['相关系数']:<12.4f} "
              f"{row['RMSE']:<10.4f} {row['MAE']:<10.4f}")
    
    # 保存平均性能
    product_avg_sorted.to_csv(f'{output_path}/product_average_performance.csv', encoding='utf-8-sig')
    
    return product_df, product_avg_sorted

def create_comprehensive_comparison(all_results, output_path='./'):
    """创建综合比较报告（产品+融合方法）"""
    print("\n" + "="*60)
    print("创建综合比较报告")
    print("="*60)
    
    # 收集所有结果数据
    all_performance_data = []
    
    for watershed, methods in all_results.items():
        # 添加预报产品性能
        if '预报产品性能' in methods:
            product_results = methods['预报产品性能']
            for product_name, metrics in product_results.items():
                all_performance_data.append({
                    '流域': watershed,
                    '方法类型': '预报产品',
                    '方法名称': product_name,
                    'NSE': metrics['NSE'],
                    '相关系数': metrics['Correlation'],
                    'RMSE': metrics['RMSE'],
                    'MAE': metrics['MAE']
                })
        
        # 添加融合方法性能
        for method_name, result in methods.items():
            if method_name != '预报产品性能':
                metrics = result['metrics']
                all_performance_data.append({
                    '流域': watershed,
                    '方法类型': '融合方法',
                    '方法名称': method_name,
                    'NSE': metrics['NSE'],
                    '相关系数': metrics['Correlation'],
                    'RMSE': metrics['RMSE'],
                    'MAE': metrics['MAE']
                })
    
    # 创建综合数据框
    comprehensive_df = pd.DataFrame(all_performance_data)
    comprehensive_df.to_csv(f'{output_path}/comprehensive_performance.csv', index=False, encoding='utf-8-sig')
    
    # 为每个流域找出最佳方法
    print("\n各流域最佳方法:")
    print("-" * 80)
    
    best_methods_summary = []
    
    for watershed in comprehensive_df['流域'].unique():
        watershed_data = comprehensive_df[comprehensive_df['流域'] == watershed]
        
        # 按NSE找出最佳方法
        best_method = watershed_data.loc[watershed_data['NSE'].idxmax()]
        
        best_methods_summary.append({
            '流域': watershed,
            '最佳方法类型': best_method['方法类型'],
            '最佳方法名称': best_method['方法名称'],
            'NSE': best_method['NSE'],
            '相关系数': best_method['相关系数'],
            'RMSE': best_method['RMSE'],
            'MAE': best_method['MAE']
        })
        
        print(f"{watershed}: {best_method['方法名称']} ({best_method['方法类型']}) - NSE: {best_method['NSE']:.4f}")
    
    # 保存最佳方法汇总
    best_methods_df = pd.DataFrame(best_methods_summary)
    best_methods_df.to_csv(f'{output_path}/best_methods_summary.csv', index=False, encoding='utf-8-sig')
    
    return comprehensive_df, best_methods_df

def main():
    """主函数"""
    print("开始分析所有流域的预报产品和融合方法性能...")
    
    # 分析所有流域
    all_results = analyze_all_watersheds()
    
    # 创建输出目录
    output_path = './results'
    os.makedirs(output_path, exist_ok=True)
    
    # 创建预报产品性能报告
    product_df, product_avg = create_product_performance_report(all_results, output_path)
    
    # 创建综合比较报告
    comprehensive_df, best_methods_df = create_comprehensive_comparison(all_results, output_path)
    
    # 创建原有的融合方法比较报告
    print("\n生成融合方法比较报告和可视化...")
    summary_df = create_comparison_report(all_results, output_path)
    
    print("\n分析完成！")
    print("生成的文件:")
    print(f"- {output_path}/product_performance_detailed.csv: 各流域预报产品详细性能")
    print(f"- {output_path}/product_average_performance.csv: 各预报产品平均性能")
    print(f"- {output_path}/comprehensive_performance.csv: 综合性能比较（产品+融合方法）")
    print(f"- {output_path}/best_methods_summary.csv: 各流域最佳方法汇总")
    print(f"- {output_path}/fusion_results_summary.csv: 融合方法详细结果")
    print(f"- {output_path}/product_performance_summary.csv: 预报产品性能汇总")
    print(f"- {output_path}/methods_comparison_heatmap.png: 方法对比热力图")
    print(f"- {output_path}/methods_radar_chart.png: 综合性能雷达图")
    print(f"- {output_path}/time_series_comparison.png: 时间序列对比图")
    print(f"- {output_path}/fusion_analysis_report.md: 详细分析报告")

if __name__ == "__main__":
    main()
