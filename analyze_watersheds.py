#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流域降水数据分析脚本
分析cheng<PERSON><PERSON>、g<PERSON><PERSON><PERSON>、<PERSON><PERSON><PERSON><PERSON>、yantang四个流域的预报降水量和实测降水量
"""

import csv
import math
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_stats(data):
    """计算均值和标准差"""
    n = len(data)
    if n == 0:
        return 0, 0

    mean_val = sum(data) / n
    variance = sum((x - mean_val) ** 2 for x in data) / n
    std_val = math.sqrt(variance)
    return mean_val, std_val

def calculate_correlation(x, y):
    """计算皮尔逊相关系数"""
    n = len(x)
    if n == 0:
        return 0

    mean_x = sum(x) / n
    mean_y = sum(y) / n

    numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
    sum_sq_x = sum((x[i] - mean_x) ** 2 for i in range(n))
    sum_sq_y = sum((y[i] - mean_y) ** 2 for i in range(n))

    denominator = math.sqrt(sum_sq_x * sum_sq_y)
    if denominator == 0:
        return 0

    return numerator / denominator

def calculate_rmse(actual, predicted):
    """计算RMSE"""
    n = len(actual)
    if n == 0:
        return 0
    mse = sum((actual[i] - predicted[i]) ** 2 for i in range(n)) / n
    return math.sqrt(mse)

def calculate_mae(actual, predicted):
    """计算MAE"""
    n = len(actual)
    if n == 0:
        return 0
    return sum(abs(actual[i] - predicted[i]) for i in range(n)) / n

def load_and_analyze_watershed(file_path, watershed_name):
    """
    加载并分析单个流域数据
    """
    print(f"\n=== 分析 {watershed_name} 流域 ===")

    # 读取数据
    times = []
    tp_values = []
    real_tp_values = []

    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # 处理不同的时间列名
            time_col = 'valid_time' if 'valid_time' in row else 'time'
            times.append(datetime.strptime(row[time_col], '%Y/%m/%d %H:%M'))
            tp_values.append(float(row['tp']))
            real_tp_values.append(float(row['real_tp']))

    print(f"数据行数: {len(tp_values)}")

    # 基本统计信息
    tp_mean, tp_std = calculate_stats(tp_values)
    real_tp_mean, real_tp_std = calculate_stats(real_tp_values)

    # 计算相关系数
    correlation = calculate_correlation(tp_values, real_tp_values)

    # 计算RMSE和MAE
    rmse = calculate_rmse(real_tp_values, tp_values)
    mae = calculate_mae(real_tp_values, tp_values)
    
    # 打印统计结果
    print(f"预报降水量 (tp):")
    print(f"  均值: {tp_mean:.4f}")
    print(f"  标准差: {tp_std:.4f}")
    print(f"实测降水量 (real_tp):")
    print(f"  均值: {real_tp_mean:.4f}")
    print(f"  标准差: {real_tp_std:.4f}")
    print(f"相关系数: {correlation:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")

    # 绘制时间序列图
    plt.figure(figsize=(15, 8))
    plt.plot(times, tp_values, color='blue', label='预报降水量 (tp)', linewidth=1, alpha=0.8)
    plt.plot(times, real_tp_values, color='red', label='实测降水量 (real_tp)', linewidth=1, alpha=0.8)
    
    plt.title(f'{watershed_name}流域 - 预报降水量与实测降水量对比', fontsize=16, fontweight='bold')
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('降水量', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 格式化x轴日期
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=30))
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    
    # 保存图片
    output_file = f'{watershed_name}_precipitation_comparison.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"图片已保存: {output_file}")
    plt.show()
    
    # 返回统计结果
    return {
        'watershed': watershed_name,
        'tp_mean': tp_mean,
        'tp_std': tp_std,
        'real_tp_mean': real_tp_mean,
        'real_tp_std': real_tp_std,
        'correlation': correlation,
        'rmse': rmse,
        'mae': mae,
        'data_points': len(tp_values)
    }

def main():
    """
    主函数：分析所有流域数据
    """
    # 定义文件路径和流域名称
    watersheds = {
        'chengkou': 'Final_data/China/C_chengkou.csv',
        'guojia': 'Final_data/China/C_guojia.csv', 
        'lianghe': 'Final_data/China/C_lianghe.csv',
        'yantang': 'Final_data/China/C_yantang.csv'
    }
    
    # 存储所有结果
    all_results = []
    
    # 分析每个流域
    for watershed_name, file_path in watersheds.items():
        if os.path.exists(file_path):
            result = load_and_analyze_watershed(file_path, watershed_name)
            all_results.append(result)
        else:
            print(f"警告: 文件 {file_path} 不存在")
    
    # 生成汇总表格
    print("\n" + "="*80)
    print("所有流域统计结果汇总")
    print("="*80)

    # 打印汇总表格
    print(f"{'流域':<10} {'预报均值':<10} {'预报标准差':<12} {'实测均值':<10} {'实测标准差':<12} {'相关系数':<10} {'RMSE':<10} {'MAE':<10}")
    print("-" * 90)

    for result in all_results:
        print(f"{result['watershed']:<10} {result['tp_mean']:<10.4f} {result['tp_std']:<12.4f} "
              f"{result['real_tp_mean']:<10.4f} {result['real_tp_std']:<12.4f} "
              f"{result['correlation']:<10.4f} {result['rmse']:<10.4f} {result['mae']:<10.4f}")

    # 保存汇总结果到CSV
    with open('watershed_analysis_summary.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['watershed', 'tp_mean', 'tp_std', 'real_tp_mean', 'real_tp_std',
                     'correlation', 'rmse', 'mae', 'data_points']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for result in all_results:
            writer.writerow(result)

    print(f"\n汇总结果已保存到: watershed_analysis_summary.csv")

if __name__ == "__main__":
    main()
